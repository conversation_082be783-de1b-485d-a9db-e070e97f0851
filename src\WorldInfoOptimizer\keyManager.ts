/**
 * 世界书优化器键管理模块
 * 使用 Base64 编码管理复合 itemKey，确保特殊字符处理的安全性
 */

import type { IBookEntryId } from './types';

/**
 * 创建项目键
 * 将复合ID对象序列化为JSON并进行Base64编码
 */
export const createItemKey = (compositeId: IBookEntryId): string => {
  try {
    const jsonString = JSON.stringify(compositeId);
    // 处理 Unicode 字符，确保 Base64 编码的兼容性
    const encoded = btoa(unescape(encodeURIComponent(jsonString)));
    return encoded;
  } catch (error) {
    console.error('[WorldInfoOptimizer] Error creating item key:', error);
    throw new Error(`Failed to create item key: ${error}`);
  }
};

/**
 * 解析项目键
 * 将Base64编码的字符串解码并反序列化为复合ID对象
 */
export const parseItemKey = (encodedKey: string): IBookEntryId => {
  try {
    // 处理 Unicode 字符的 Base64 解码
    const decoded = decodeURIComponent(escape(atob(encodedKey)));
    const compositeId: IBookEntryId = JSON.parse(decoded);
    return compositeId;
  } catch (error) {
    console.error('[WorldInfoOptimizer] Error parsing item key:', error, encodedKey);
    throw new Error(`Failed to parse item key: ${error}`);
  }
};

/**
 * 便捷方法：创建世界书项目键
 */
export const createBookItemKey = (bookName: string): string => {
  return createItemKey({
    type: 'book',
    bookName,
  });
};

/**
 * 便捷方法：创建条目项目键
 */
export const createEntryItemKey = (bookName: string, entryId: string): string => {
  return createItemKey({
    type: 'entry',
    bookName,
    entryId,
  });
};

/**
 * 便捷方法：创建正则表达式项目键
 */
export const createRegexItemKey = (scope: string, regexId: string): string => {
  return createItemKey({
    type: 'regex',
    scope,
    regexId,
  });
};