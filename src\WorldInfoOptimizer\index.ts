// ==UserScript==
// @name         世界书优化器 (World Info Optimizer)
// @namespace    SillyTavern.WorldInfoOptimizer.v1_0_0
// @match        */*
// @version      1.0.0
// @description  【世界书管理专家】基于 Linus 哲学的简洁高效世界书管理工具。提供搜索、替换、批量操作等核心功能。
// <AUTHOR> & AI Assistant
// @grant        none
// @inject-into  content
// ==/UserScript==

'use strict';

import { initializeTavernAPI, loadAllData } from './api';
import { bindEventHandlers, setGlobalDependencies as setEventsDependencies } from './events';
import { hydrateAppState } from './state';
import {
  addBasicStyles,
  createExtensionButton,
  createMainPanel,
  renderContent,
  setGlobalDependencies as setUIDependencies,
} from './ui';
import { errorCatched } from './utils';

// 使用IIFE封装，避免全局污染
(() => {
  console.log('[WorldInfoOptimizer] Script execution started.');

  /**
   * 等待DOM和API就绪
   * @param {Function} callback - 准备就绪后执行的回调函数
   */
  function onReady(callback: (jquery: any, tavernHelper: any) => void): void {
    const domSelector = '#extensions_settings';
    const timeout = 30000; // 30秒超时

    console.log(
      `[WorldInfoOptimizer] Starting readiness check. Watching for DOM element "${domSelector}" with a ${
        timeout / 1000
      }s timeout.`,
    );

    let observer: MutationObserver | null = null;
    let timeoutId: number | null = null;

    const cleanup = () => {
      if (observer) {
        observer.disconnect();
        observer = null;
        console.log('[WorldInfoOptimizer] MutationObserver disconnected.');
      }
      if (timeoutId) {
        clearTimeout(timeoutId);
        timeoutId = null;
      }
    };

    const checkForReadyState = () => {
      const parentWin = window.parent as any;
      const parentDoc = parentWin.document;

      const domReady = parentDoc.querySelector(domSelector) !== null;
      const apiReady =
        parentWin.TavernHelper && typeof parentWin.TavernHelper.getCharData === 'function' && parentWin.jQuery;

      if (domReady && apiReady) {
        cleanup();
        console.log(`[WorldInfoOptimizer] SUCCESS: Both DOM and Core APIs are ready. Initializing script.`);
        try {
          callback(parentWin.jQuery, parentWin.TavernHelper);
        } catch (e) {
          console.error('[WorldInfoOptimizer] FATAL: Error during main callback execution.', e);
        }
        return true;
      }
      return false;
    };

    // 初始检查，可能已经就绪
    if (checkForReadyState()) {
      return;
    }

    observer = new MutationObserver(mutations => {
      // 优化：只在有节点添加时才执行检查
      const hasAddedNodes = mutations.some(m => m.addedNodes.length > 0);
      if (hasAddedNodes && checkForReadyState()) {
        // 成功后，checkForReadyState内部会调用cleanup
      }
    });

    observer.observe(window.parent.document.body, {
      childList: true,
      subtree: true,
    });

    timeoutId = window.setTimeout(() => {
      const parentWin = window.parent as any;
      const parentDoc = parentWin.document;
      const domReady = parentDoc.querySelector(domSelector) !== null;
      const apiReady =
        parentWin.TavernHelper && typeof parentWin.TavernHelper.getCharData === 'function' && parentWin.jQuery;

      cleanup(); // 停止所有监听
      console.error(`[WorldInfoOptimizer] FATAL: Readiness check timed out after ${timeout / 1000} seconds.`);
      if (!domReady) console.error(`[WorldInfoOptimizer] -> Failure: DOM element "${domSelector}" not found.`);
      if (!apiReady) console.error(`[WorldInfoOptimizer] -> Failure: Core APIs not available.`);
    }, timeout);
  }

  /**
   * 主初始化函数
   * @param {any} jquery - jQuery对象
   * @param {any} tavernHelper - TavernHelper对象
   */
  const main = errorCatched((jquery: any, tavernHelper: any): void => {
    // 1. 设置全局依赖
    const dependencies = {
      $: jquery,
      TavernHelper: tavernHelper,
      parentWin: window.parent,
    };
    initializeTavernAPI(dependencies); // 初始化API模块

    // 设置UI和事件模块的全局依赖
    setUIDependencies(jquery, window.parent);
    setEventsDependencies(jquery, window.parent);

    // 2. 注入UI和样式
    addBasicStyles();
    createMainPanel();
    createExtensionButton();

    // 3. 绑定核心事件处理器
    bindEventHandlers();

    // 4. 初始数据加载
    // loadAllData 会触发 hydrateAppState 和 renderContent
    loadAllData(dependencies)
      .then(data => {
        hydrateAppState(data); // 使用返回的数据更新状态
        renderContent(); // 初始渲染
        console.log('[WorldInfoOptimizer] Initial data load, state hydration, and render complete.');
      })
      .catch(error => {
        console.error('[WorldInfoOptimizer] Failed to complete initial data orchestration:', error);
      });

    console.log('[WorldInfoOptimizer] Initialization sequence complete.');
  }, 'WorldInfoOptimizer-Main');

  // --- 脚本启动 ---
  onReady(main);
})();
