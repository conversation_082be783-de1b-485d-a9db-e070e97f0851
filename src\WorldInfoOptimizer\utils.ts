/**
 * 世界书优化器工具函数
 * 包含 escapeHtml、highlightText、debounce 等纯函数工具
 */

import { REGEX_PATTERNS } from './constants';
import type { ErrorCatchedFunction } from './types';

// --- HTML 处理工具 ---

/**
 * 转义HTML特殊字符
 */
export const escapeHtml = (text: any): string => {
  if (typeof text !== 'string') return String(text);
  const div = document.createElement('div');
  div.textContent = text;
  return div.innerHTML;
};

/**
 * 处理文本内容并保留换行符
 */
export const escapeHtmlWithNewlines = (text: any): string => {
  const escaped = escapeHtml(text);
  return escaped.replace(/\n/g, '<br>');
};

/**
 * 反转义HTML
 */
export const unescapeHtml = (html: string): string => {
  if (typeof html !== 'string') return String(html);
  const area = document.createElement('textarea');
  area.innerHTML = html;
  return area.value;
};

// --- 搜索和高亮工具 ---

/**
 * 高亮搜索文本
 */
export const highlightText = (text: string, searchTerm: string): string => {
  if (!text) return '';

  // 移除任何现有的高亮标签，防止嵌套
  const cleanText = text.replace(REGEX_PATTERNS.SEARCH_HIGHLIGHT, '');

  if (!searchTerm.trim()) return cleanText;

  try {
    // 首先尝试简单的文本包含检查（对JSON更友好）
    const lowerText = cleanText.toLowerCase();
    const lowerSearchTerm = searchTerm.toLowerCase();

    if (lowerText.includes(lowerSearchTerm)) {
      // 使用简单的字符串替换进行高亮
      const regex = new RegExp(escapeRegExp(searchTerm), 'gi');
      return cleanText.replace(regex, '<mark class="wio-highlight">$&</mark>');
    }

    return cleanText;
  } catch (error) {
    console.warn('[WorldInfoOptimizer] Error highlighting text:', error);
    return cleanText;
  }
};

/**
 * 检查文本是否匹配搜索词
 */
export const isMatch = (text: string, searchTerm: string): boolean => {
  if (!searchTerm.trim()) return false;
  if (!text) return false;

  try {
    // 首先尝试简单的文本包含检查（对JSON更友好）
    const lowerText = text.toLowerCase();
    const lowerSearchTerm = searchTerm.toLowerCase();
    return lowerText.includes(lowerSearchTerm);
  } catch (error) {
    console.warn('[WorldInfoOptimizer] Error in text matching:', error);
    return false;
  }
};

/**
 * 转义正则表达式特殊字符
 */
export const escapeRegExp = (string: string): string => {
  return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
};

// --- 防抖和节流工具 ---

/**
 * 防抖函数
 */
export const debounce = (func: Function, delay: number) => {
  let timeout: number;
  return (...args: any[]) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), delay);
  };
};

/**
 * 节流函数
 */
export const throttle = (func: Function, limit: number) => {
  let inThrottle: boolean;
  return (...args: any[]) => {
    if (!inThrottle) {
      func.apply(this, args);
      inThrottle = true;
      setTimeout(() => (inThrottle = false), limit);
    }
  };
};

// --- ID 生成工具 ---

/**
 * 创建安全的ID
 */
export const createSafeId = (name: string): string => {
  return 'entries-' + btoa(encodeURIComponent(name)).replace(REGEX_PATTERNS.SAFE_ID, '');
};

/**
 * 生成唯一ID
 */
export const generateUniqueId = (): string => {
  return Date.now().toString(36) + Math.random().toString(36).substr(2);
};

// --- 错误处理工具 ---

/**
 * 错误捕获包装器
 */
export const errorCatched = <T extends (...args: any[]) => any>(
  fn: T,
  context = 'Unknown'
): ErrorCatchedFunction<T> => {
  return ((...args: any[]) => {
    try {
      const result = fn(...args);
      if (result instanceof Promise) {
        return result.catch((error: any) => {
          console.error(`[WorldInfoOptimizer] Error in ${context}:`, error);
          throw error;
        });
      }
      return result;
    } catch (error) {
      console.error(`[WorldInfoOptimizer] Error in ${context}:`, error);
      throw error;
    }
  }) as ErrorCatchedFunction<T>;
};

// --- 数组和对象工具 ---

/**
 * 深拷贝对象
 */
export const deepClone = <T>(obj: T): T => {
  if (obj === null || typeof obj !== 'object') return obj;
  if (obj instanceof Date) return new Date(obj.getTime()) as unknown as T;
  if (obj instanceof Array) return obj.map(item => deepClone(item)) as unknown as T;
  if (typeof obj === 'object') {
    const clonedObj = {} as T;
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        clonedObj[key] = deepClone(obj[key]);
      }
    }
    return clonedObj;
  }
  return obj;
};

/**
 * 检查对象是否为空
 */
export const isEmpty = (obj: any): boolean => {
  if (obj == null) return true;
  if (Array.isArray(obj) || typeof obj === 'string') return obj.length === 0;
  if (obj instanceof Map || obj instanceof Set) return obj.size === 0;
  return Object.keys(obj).length === 0;
};

// --- 字符串工具 ---

/**
 * 截断文本
 */
export const truncateText = (text: string, maxLength: number, suffix = '...'): string => {
  if (!text || text.length <= maxLength) return text;
  return text.substring(0, maxLength - suffix.length) + suffix;
};

/**
 * 首字母大写
 */
export const capitalize = (str: string): string => {
  if (!str) return str;
  return str.charAt(0).toUpperCase() + str.slice(1);
};

// --- 类型检查工具 ---

/**
 * 检查是否为有效的字符串
 */
export const isValidString = (value: any): value is string => {
  return typeof value === 'string' && value.trim().length > 0;
};

/**
 * 检查是否为数字
 */
export const isNumber = (value: any): value is number => {
  return typeof value === 'number' && !isNaN(value);
};

/**
 * 检查是否为有效的数组
 */
export const isValidArray = (value: any): value is any[] => {
  return Array.isArray(value) && value.length > 0;
};

// --- 时间工具 ---

/**
 * 格式化时间戳
 */
export const formatTimestamp = (timestamp: number): string => {
  const date = new Date(timestamp);
  return date.toLocaleString('zh-CN');
};

/**
 * 获取相对时间
 */
export const getRelativeTime = (timestamp: number): string => {
  const now = Date.now();
  const diff = now - timestamp;
  const seconds = Math.floor(diff / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);
  const days = Math.floor(hours / 24);

  if (days > 0) return `${days}天前`;
  if (hours > 0) return `${hours}小时前`;
  if (minutes > 0) return `${minutes}分钟前`;
  return '刚刚';
};
