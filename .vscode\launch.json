{"version": "0.2.0", "configurations": [{"name": "调试酒馆网页(Chrome)", "type": "chrome", "request": "launch", "preLaunchTask": "开始任务", "postDebugTask": "结束任务", "url": "https://st.tanyaleoallen.fun/", "disableNetworkCache": true, "internalConsoleOptions": "openOnSessionStart", "cwd": "${workspaceFolder}", "sourceMapPathOverrides": {"webpack://tavern_helper_template/*": "${workspaceFolder}/*", "webpack-internal:///*": "${webRoot}/scripts/extensions/third-party/*", "webpack:/*": "${webRoot}/scripts/extensions/third-party/*"}, "skipFiles": ["**/jquery*.min.js"]}, {"name": "调试酒馆网页(Edge)", "type": "msedge", "request": "launch", "preLaunchTask": "开始任务", "postDebugTask": "结束任务", "url": "https://st.tanyaleoallen.fun/", "disableNetworkCache": true, "internalConsoleOptions": "openOnSessionStart", "cwd": "${workspaceFolder}", "sourceMapPathOverrides": {"webpack://tavern_helper_template/*": "${workspaceFolder}/*", "webpack-internal:///*": "${webRoot}/scripts/extensions/third-party/*", "webpack:/*": "${webRoot}/scripts/extensions/third-party/*"}, "skipFiles": ["**/jquery*.min.js"]}]}