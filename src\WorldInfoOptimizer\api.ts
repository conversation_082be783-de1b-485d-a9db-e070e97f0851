/**
 * 世界书优化器 API 交互模块
 * 包含所有与 SillyTavern API 交互的函数
 */

import { MESSAGES, PANEL_ID } from './constants';
import {
  appState,
  safeClearLorebookEntries,
  safeSetLorebookEntries,
  setDataLoaded,
  updateCharacterRegexes,
} from './state';
import type { InitialDataPayload, Lorebook, LorebookEntry, TavernHelperAPI, TavernRegex } from './types';
import { errorCatched } from './utils';

// --- 全局变量 ---
// --- API 包装器 ---
export let TavernAPI: any = null;

/**
 * 初始化 TavernAPI
 */
export const initializeTavernAPI = (dependencies: {
  $: any;
  parentWin: any;
  TavernHelper: TavernHelperAPI;
}) => {
  const { $, parentWin, TavernHelper } = dependencies;

  TavernAPI = {
    createLorebook: errorCatched(async (name: string) => await TavernHelper.createLorebook(name), 'createLorebook'),
    deleteLorebook: errorCatched(async (name: string) => await TavernHelper.deleteLorebook(name), 'deleteLorebook'),
    getLorebooks: errorCatched(async () => await TavernHelper.getLorebooks(), 'getLorebooks'),
    setLorebookSettings: errorCatched(
      async (settings: any) => await TavernHelper.setLorebookSettings(settings),
      'setLorebookSettings',
    ),
    getCharData: errorCatched(async () => await TavernHelper.getCharData(), 'getCharData'),
    Character: TavernHelper.Character || null,
    getRegexes: errorCatched(async () => await TavernHelper.getTavernRegexes({ scope: 'all' }), 'getRegexes'),
    replaceRegexes: errorCatched(
      async (regexes: any) => await TavernHelper.replaceTavernRegexes(regexes, { scope: 'all' }),
      'replaceRegexes',
    ),
    createLorebookEntries: errorCatched(
      async (bookName: string, entries: any[]) => await TavernHelper.createLorebookEntries(bookName, entries),
      'createLorebookEntries',
    ),
    setLorebookEntries: errorCatched(
      async (bookName: string, entries: any[]) => await TavernHelper.setLorebookEntries(bookName, entries),
      'setLorebookEntries',
    ),
    deleteLorebookEntry: errorCatched(
      async (bookName: string, uid: number) => await TavernHelper.deleteLorebookEntry(bookName, uid),
      'deleteLorebookEntry',
    ),
    getLorebookSettings: errorCatched(async () => await TavernHelper.getLorebookSettings(), 'getLorebookSettings'),
    getCurrentCharLorebooks: errorCatched(
      async () => await TavernHelper.getCurrentCharLorebooks(),
      'getCurrentCharLorebooks',
    ),
    getChatLorebook: errorCatched(async () => await TavernHelper.getChatLorebook(), 'getChatLorebook'),
  };
};

/**
 * 更新进度显示
 */

/**
 * 加载所有数据
 */
export const loadAllData = errorCatched(async (dependencies: {
  $: any;
  parentWin: any;
}): Promise<InitialDataPayload> => {
  const { $, parentWin } = dependencies;
  const $content = $(`#${PANEL_ID}-content`, parentWin.document);
  $content.html(`
    <div class="wio-loading-container">
      <div class="wio-loading-title">数据同步中...</div>
      <div class="wio-loading-progress-bar-container">
        <div id="wio-loading-bar" class="wio-loading-progress-bar" style="width: 0%;"></div>
      </div>
      <div id="wio-loading-status" class="wio-loading-status-text">${MESSAGES.LOADING.INITIALIZING}</div>
    </div>
  `);

  try {

    if (!parentWin.SillyTavern || !parentWin.SillyTavern.getContext) {
      console.warn('[WorldInfoOptimizer] SillyTavern API not available, initializing with empty data');
      return {
        globalRegex: [],
        characterRegex: [],
        allLorebooks: [],
        characterLorebooks: [],
        chatLorebook: null,
        lorebookEntries: new Map(),
      };
    }

    const context = parentWin.SillyTavern.getContext() || {};
    const hasActiveCharacter = context.characterId !== undefined && context.characterId !== null;
    const hasActiveChat = context.chatId !== undefined && context.chatId !== null;


    const promises = [
      TavernAPI.getRegexes().catch(() => []),
      TavernAPI.getLorebookSettings().catch(() => ({})),
      TavernAPI.getLorebooks().catch(() => []),
      hasActiveCharacter ? TavernAPI.getCharData().catch(() => null) : Promise.resolve(null),
      hasActiveCharacter ? TavernAPI.getCurrentCharLorebooks().catch(() => []) : Promise.resolve([]),
      hasActiveChat ? TavernAPI.getChatLorebook().catch(() => null) : Promise.resolve(null),
    ];

    const results = await Promise.allSettled(promises);

    const allUirRegexes: TavernRegex[] = results[0].status === 'fulfilled' ? results[0].value : [];
    const globalSettings = results[1].status === 'fulfilled' ? results[1].value : {};
    const allBookFileNames: string[] = results[2].status === 'fulfilled' ? results[2].value : [];
    const charData = results[3].status === 'fulfilled' ? results[3].value : null;
    const charLinkedBooks: string[] = results[4].status === 'fulfilled' ? results[4].value : [];
    const chatLorebook: string | null = results[5].status === 'fulfilled' ? results[5].value : null;

    const globalRegex = allUirRegexes.filter(r => r.scope === 'global');
    const characterRegex = allUirRegexes.filter(r => r.scope === 'character');

    const allLorebooks: Lorebook[] = allBookFileNames.map(name => ({
      name,
      enabled: globalSettings.world_info_include?.includes(name) || false,
    }));

    const lorebookEntries = await loadLorebookEntries(allBookFileNames, parentWin);


    const payload: InitialDataPayload = {
      globalRegex,
      characterRegex,
      allLorebooks,
      characterLorebooks: charLinkedBooks,
      chatLorebook,
      lorebookEntries,
    };

    return payload;
  } catch (error) {
    console.error('[WorldInfoOptimizer] Error loading data:', error);
    setTimeout(() => {
      $content.html('<p class="wio-error-text">数据加载失败，请点击刷新按钮重试。</p>');
    }, 1000);
    throw error;
  }
}, 'loadAllData');

/**
 * 加载世界书条目
 */
const loadLorebookEntries = async (
  bookNames: string[],
  parentWin: any
): Promise<Map<string, LorebookEntry[]>> => {
  const entriesMap = new Map<string, LorebookEntry[]>();

  if (!Array.isArray(bookNames) || bookNames.length === 0) {
    console.log('[WorldInfoOptimizer] No lorebooks to load entries for');
    return entriesMap;
  }

  const batchSize = 5;
  const totalBooks = bookNames.length;
  let processedBooks = 0;

  for (let i = 0; i < bookNames.length; i += batchSize) {
    const batch = bookNames.slice(i, i + batchSize);
    const batchPromises = batch.map(async (bookName: string) => {
      try {
        const entries = await TavernHelper.getLorebookEntries(bookName);
        if (Array.isArray(entries)) {
          entriesMap.set(bookName, entries as any as LorebookEntry[]);
        }
      } catch (error) {
        console.warn(`[WorldInfoOptimizer] Failed to load entries for ${bookName}:`, error);
      } finally {
        processedBooks++;
        const progress = 50 + (processedBooks / totalBooks) * 40;
      }
    });

    await Promise.allSettled(batchPromises);

    if (i + batchSize < bookNames.length) {
      await new Promise(resolve => setTimeout(resolve, 100));
    }
  }

  console.log(`[WorldInfoOptimizer] Loaded entries for ${processedBooks} lorebooks`);
  return entriesMap;
};

/**
 * 刷新数据
 */
export const refreshData = async (renderContent: () => void) => {
  console.log('[WorldInfoOptimizer] Refreshing data...');
  setDataLoaded(false);
  const data = await loadAllData({ $: (window as any).$, parentWin: window.parent });
  //  hydrateAppState(data); // This will be added in the next step
  renderContent();
};

/**
 * 创建世界书
 */
export const createLorebook = async (name: string): Promise<boolean> => {
  try {
    await TavernAPI.createLorebook(name);
    return true;
  } catch (error) {
    console.error('[WorldInfoOptimizer] Failed to create lorebook:', error);
    return false;
  }
};

/**
 * 删除世界书
 */
export const deleteLorebook = async (name: string): Promise<boolean> => {
  try {
    await TavernAPI.deleteLorebook(name);
    return true;
  } catch (error) {
    console.error('[WorldInfoOptimizer] Failed to delete lorebook:', error);
    return false;
  }
};

/**
 * 创建世界书条目
 */
export const createLorebookEntry = async (bookName: string, entryData: Partial<LorebookEntry>): Promise<boolean> => {
  try {
    await TavernAPI.createLorebookEntries(bookName, [entryData]);
    return true;
  } catch (error) {
    console.error('[WorldInfoOptimizer] Failed to create lorebook entry:', error);
    return false;
  }
};

/**
 * 删除世界书条目
 */
export const deleteLorebookEntry = async (bookName: string, uid: number): Promise<boolean> => {
  try {
    await TavernAPI.deleteLorebookEntry(bookName, uid);
    return true;
  } catch (error) {
    console.error('[WorldInfoOptimizer] Failed to delete lorebook entry:', error);
    return false;
  }
};

/**
 * 批量删除世界书条目
 */
export const deleteLorebookEntries = async (bookName: string, uids: number[]): Promise<boolean> => {
  try {
    // TavernAPI可能没有批量删除的端点，所以我们并行执行单个删除
    const deletePromises = uids.map(uid => TavernAPI.deleteLorebookEntry(bookName, uid));
    await Promise.all(deletePromises);
    return true;
  } catch (error) {
    console.error(`[WorldInfoOptimizer] Failed to delete lorebook entries from book "${bookName}":`, error);
    return false;
  }
};

/**
 * 更新世界书条目
 */
export const updateLorebookEntries = async (bookName: string, entries: any[]): Promise<boolean> => {
  try {
    await TavernAPI.setLorebookEntries(bookName, entries);
    return true;
  } catch (error) {
    console.error('[WorldInfoOptimizer] Failed to update lorebook entries:', error);
    return false;
  }
};
