<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WorldInfoOptimizer 函数测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <div class="container">
        <h1>WorldInfoOptimizer 函数测试</h1>
        <p>此页面用于测试 WorldInfoOptimizer 的全局函数是否正确定义。</p>
        
        <div class="test-section">
            <h3>基本函数测试</h3>
            <button class="test-button" onclick="testToggleBookEntries()">测试 toggleBookEntries</button>
            <button class="test-button" onclick="testEditBook()">测试 editBook</button>
            <button class="test-button" onclick="testRenameBook()">测试 renameBook</button>
            <button class="test-button" onclick="testDeleteBook()">测试 deleteBook</button>
        </div>

        <div class="test-section">
            <h3>条目操作测试</h3>
            <button class="test-button" onclick="testEditEntry()">测试 editEntry</button>
            <button class="test-button" onclick="testToggleEntry()">测试 toggleEntry</button>
            <button class="test-button" onclick="testDeleteEntry()">测试 deleteEntry</button>
        </div>

        <div class="test-section">
            <h3>批量操作测试</h3>
            <button class="test-button" onclick="testSelectAllEntries()">测试 selectAllEntries</button>
            <button class="test-button" onclick="testSelectNoneEntries()">测试 selectNoneEntries</button>
            <button class="test-button" onclick="testBatchEnableEntries()">测试 batchEnableEntries</button>
            <button class="test-button" onclick="testBatchDisableEntries()">测试 batchDisableEntries</button>
            <button class="test-button" onclick="testBatchDeleteEntries()">测试 batchDeleteEntries</button>
        </div>

        <div class="test-section">
            <h3>绑定操作测试</h3>
            <button class="test-button" onclick="testUnbindPrimaryBook()">测试 unbindPrimaryBook</button>
            <button class="test-button" onclick="testUnbindAdditionalBook()">测试 unbindAdditionalBook</button>
            <button class="test-button" onclick="testUnbindChatBook()">测试 unbindChatBook</button>
        </div>

        <div id="status" class="status info">
            准备测试...点击任意按钮开始测试函数是否正确定义。
        </div>
    </div>

    <script>
        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
        }

        function testFunction(funcName, ...args) {
            try {
                if (typeof window[funcName] === 'function') {
                    updateStatus(`✅ ${funcName} 函数已正确定义`, 'success');
                    console.log(`${funcName} 函数测试通过`);
                } else {
                    updateStatus(`❌ ${funcName} 函数未定义或不是函数`, 'error');
                    console.error(`${funcName} 函数测试失败`);
                }
            } catch (error) {
                updateStatus(`❌ 测试 ${funcName} 时出错: ${error.message}`, 'error');
                console.error(`测试 ${funcName} 时出错:`, error);
            }
        }

        // 测试函数
        function testToggleBookEntries() { testFunction('toggleBookEntries', 'testBook'); }
        function testEditBook() { testFunction('editBook', 'testBook'); }
        function testRenameBook() { testFunction('renameBook', 'testBook'); }
        function testDeleteBook() { testFunction('deleteBook', 'testBook'); }
        function testEditEntry() { testFunction('editEntry', 'testBook', 1); }
        function testToggleEntry() { testFunction('toggleEntry', 'testBook', 1); }
        function testDeleteEntry() { testFunction('deleteEntry', 'testBook', 1); }
        function testSelectAllEntries() { testFunction('selectAllEntries', 'testBook'); }
        function testSelectNoneEntries() { testFunction('selectNoneEntries', 'testBook'); }
        function testBatchEnableEntries() { testFunction('batchEnableEntries', 'testBook'); }
        function testBatchDisableEntries() { testFunction('batchDisableEntries', 'testBook'); }
        function testBatchDeleteEntries() { testFunction('batchDeleteEntries', 'testBook'); }
        function testUnbindPrimaryBook() { testFunction('unbindPrimaryBook'); }
        function testUnbindAdditionalBook() { testFunction('unbindAdditionalBook', 'testBook'); }
        function testUnbindChatBook() { testFunction('unbindChatBook'); }

        // 页面加载时进行初始检查
        window.addEventListener('load', function() {
            updateStatus('页面已加载，可以开始测试函数定义。', 'info');
        });
    </script>
</body>
</html>
