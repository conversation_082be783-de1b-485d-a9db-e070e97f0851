/**
 * 世界书优化器 UI 渲染模块
 * 包含所有DOM操作、组件创建和渲染函数
 */

import {
  BUTTON_ICON_URL,
  BUTTON_ID,
  BUTTON_TOOLTIP,
  CSS_CLASSES,
  MESSAGES,
  PANEL_ID,
  REFRESH_BTN_ID,
  SEARCH_INPUT_ID,
  TOAST_DURATION,
} from './constants';
import { appState, safeGetLorebookEntries } from './state';
import { createBookItemKey, createEntryItemKey, createRegexItemKey } from './keyManager';
import type { LorebookEntry, ModalOptions, ProgressToast } from './types';
import { escapeHtml, escapeHtmlWithNewlines, highlightText, isMatch } from './utils';

// --- 全局变量 ---
let parentWin: any;
let $: any;

/**
 * 设置全局依赖
 */
export const setGlobalDependencies = (jquery: any, parentWindow: any) => {
  $ = jquery;
  parentWin = parentWindow;
};

// --- 通知和模态框函数 ---

/**
 * 显示成功提示
 */
export const showSuccessTick = (
  message: string = MESSAGES.SUCCESS.OPERATION_SUCCESS,
  duration = TOAST_DURATION,
): void => {
  const $panel = $(`#${PANEL_ID}`, parentWin.document);
  if ($panel.length === 0) return;

  $panel.find(`.${CSS_CLASSES.TOAST_NOTIFICATION}`).remove();

  const $toast = $(`
    <div class="${CSS_CLASSES.TOAST_NOTIFICATION}">
      <i class="fa-solid fa-check-circle"></i>
      <span>${escapeHtml(message)}</span>
    </div>
  `);

  $panel.append($toast);
  setTimeout(() => $toast.addClass('visible'), 10);
  setTimeout(() => {
    $toast.removeClass('visible');
    setTimeout(() => $toast.remove(), 300);
  }, duration);
};

/**
 * 显示进度提示
 */
export const showProgressToast = (initialMessage = '正在处理...'): ProgressToast => {
  const $panel = $(`#${PANEL_ID}`, parentWin.document);
  if ($panel.length === 0) return { update: () => {}, remove: () => {} };

  $panel.find(`.${CSS_CLASSES.PROGRESS_TOAST}`).remove();

  const $toast = $(`
    <div class="${CSS_CLASSES.PROGRESS_TOAST}">
      <i class="fa-solid fa-spinner fa-spin"></i>
      <span class="wio-progress-text">${escapeHtml(initialMessage)}</span>
    </div>
  `);

  $panel.append($toast);
  setTimeout(() => $toast.addClass('visible'), 10);

  const update = (newMessage: string) => {
    $toast.find('.wio-progress-text').html(escapeHtml(newMessage));
  };

  const remove = () => {
    $toast.removeClass('visible');
    setTimeout(() => {
      $toast.remove();
    }, 300);
  };

  return { update, remove };
};

/**
 * 显示模态框
 */
export const showModal = (options: ModalOptions): Promise<any> => {
  return new Promise((resolve, reject) => {
    const { type = 'alert', title = '通知', text = '', placeholder = '', value = '' } = options;
    let buttonsHtml = '';
    if (type === 'alert') buttonsHtml = '<button class="wio-modal-btn wio-modal-ok">确定</button>';
    else if (type === 'confirm')
      buttonsHtml =
        '<button class="wio-modal-btn wio-modal-cancel">取消</button><button class="wio-modal-btn wio-modal-ok">确定</button>';
    else if (type === 'prompt')
      buttonsHtml =
        '<button class="wio-modal-btn wio-modal-cancel">取消</button><button class="wio-modal-btn wio-modal-ok">确定</button>';

    const inputHtml =
      type === 'prompt'
        ? `<input type="text" class="wio-modal-input" placeholder="${escapeHtml(placeholder)}" value="${escapeHtml(value)}">`
        : '';

    const modalHtml = `<div class="${CSS_CLASSES.MODAL_OVERLAY}"><div class="${CSS_CLASSES.MODAL_CONTENT}"><div class="wio-modal-header">${escapeHtml(title)}</div><div class="wio-modal-body"><p>${escapeHtmlWithNewlines(text)}</p>${inputHtml}</div><div class="wio-modal-footer">${buttonsHtml}</div></div></div>`;

    const $modal = $(modalHtml).hide();
    const $panel = $(`#${PANEL_ID}`, parentWin.document);
    if ($panel.length > 0) {
      $panel.append($modal);
    } else {
      $('body', parentWin.document).append($modal);
    }

    $modal.fadeIn(200);

    const $input = $modal.find('.wio-modal-input');
    if (type === 'prompt') $input.focus().select();

    const closeModal = (isSuccess: boolean, val?: any) => {
      $modal.fadeOut(200, () => {
        $modal.remove();
        if (isSuccess) resolve(val);
        else reject();
      });
    };

    $modal.on('click', '.wio-modal-ok', () => {
      if (type === 'prompt') {
        const inputVal = $input.val();
        closeModal(true, inputVal);
      } else {
        closeModal(true);
      }
    });

    $modal.on('click', '.wio-modal-cancel', () => closeModal(false));
    $modal.on('click', (e: any) => {
      if (e.target === $modal[0]) closeModal(false);
    });
  });
};

// --- UI 创建函数 ---

/**
 * 创建主面板
 */
export const createMainPanel = (): void => {
  const parentDoc = parentWin.document;

  // 检查面板是否已存在
  if ($(`#${PANEL_ID}`, parentDoc).length > 0) {
    console.log('[WorldInfoOptimizer] Panel already exists, skipping creation.');
    return;
  }

  const panelHtml = `
    <div id="${PANEL_ID}" class="${CSS_CLASSES.PANEL}" style="display: none;">
      <div class="${CSS_CLASSES.PANEL_HEADER}">
        <h3 class="wio-panel-title">
          <i class="fa-solid fa-book"></i> 世界书优化器
        </h3>
        <div class="wio-panel-controls">
          <button id="${REFRESH_BTN_ID}" class="wio-btn wio-btn-icon" title="刷新数据">
            <i class="fa-solid fa-sync-alt"></i>
          </button>
          <button id="wio-close-btn" class="wio-btn wio-btn-icon ${CSS_CLASSES.PANEL_CLOSE}" title="关闭">
            <i class="fa-solid fa-times"></i>
          </button>
        </div>
      </div>
      <div class="${CSS_CLASSES.PANEL_BODY}">
        <div class="wio-tabs">
          <button class="${CSS_CLASSES.TAB_BTN} ${CSS_CLASSES.ACTIVE}" data-tab="global-lore">全局世界书</button>
          <button class="${CSS_CLASSES.TAB_BTN}" data-tab="char-lore">角色世界书</button>
          <button class="${CSS_CLASSES.TAB_BTN}" data-tab="chat-lore">聊天世界书</button>
          <button class="${CSS_CLASSES.TAB_BTN}" data-tab="global-regex">全局正则</button>
          <button class="${CSS_CLASSES.TAB_BTN}" data-tab="char-regex">角色正则</button>
        </div>
        <div class="wio-search-container">
          <div class="wio-search-input-wrapper">
            <input type="text" id="${SEARCH_INPUT_ID}" class="wio-search-input" placeholder="搜索世界书、条目或正则表达式...">
            <button id="wio-clear-search-btn" class="wio-btn wio-btn-icon wio-clear-search" title="清空搜索">
              <i class="fa-solid fa-times"></i>
            </button>
          </div>
          <div class="wio-search-filters">
            <label><input type="checkbox" id="wio-filter-book-name" checked> 世界书名</label>
            <label><input type="checkbox" id="wio-filter-entry-name" checked> 条目名</label>
            <label><input type="checkbox" id="wio-filter-keywords" checked> 关键词</label>
            <label><input type="checkbox" id="wio-filter-content" checked> 内容</label>
          </div>
        </div>
        <div class="wio-toolbar">
          <div class="wio-toolbar-left">
            <button id="wio-multi-select-toggle" class="wio-btn wio-multi-select-toggle">
              <i class="fa-solid fa-check-square"></i> 多选模式
            </button>
            <span id="wio-selection-count" class="wio-selection-count" style="display: none;">已选择: 0</span>
          </div>
          <div class="wio-toolbar-right">
            <button id="wio-collapse-current-btn" class="wio-btn wio-btn-secondary" title="折叠当前标签页">
              <i class="fa-solid fa-compress-alt"></i> 折叠当前
            </button>
            <button id="wio-collapse-all-btn" class="wio-btn wio-btn-secondary" title="折叠所有">
              <i class="fa-solid fa-compress"></i> 全部折叠
            </button>
          </div>
        </div>
        <div class="wio-multi-select-controls" id="wio-multi-select-controls" style="display: none;">
          <button id="wio-select-all-btn" class="wio-btn wio-btn-small">全选</button>
          <button id="wio-deselect-all-btn" class="wio-btn wio-btn-small">取消全选</button>
          <button id="wio-invert-selection-btn" class="wio-btn wio-btn-small">反选</button>
          <button id="wio-batch-enable-btn" class="wio-btn wio-btn-small wio-btn-success">批量启用</button>
          <button id="wio-batch-disable-btn" class="wio-btn wio-btn-small wio-btn-warning">批量禁用</button>
          <button id="wio-batch-delete-btn" class="wio-btn wio-btn-small wio-btn-danger">批量删除</button>
        </div>
        <div class="wio-replace-container" style="display: none;">
          <div class="wio-replace-inputs">
            <input type="text" id="wio-replace-input" class="wio-replace-input" placeholder="替换为...">
            <button id="wio-replace-btn" class="wio-btn wio-btn-primary">替换</button>
            <button id="wio-replace-close-btn" class="wio-btn wio-btn-secondary">关闭</button>
          </div>
        </div>
        <div id="${PANEL_ID}-content" class="wio-content">
          <p class="wio-info-text">正在加载数据...</p>
        </div>
      </div>
    </div>
  `;

  $('body', parentDoc).append(panelHtml);
  addBasicStyles();
  console.log('[WorldInfoOptimizer] Main panel created successfully.');
};

/**
 * 创建扩展按钮
 */
export const createExtensionButton = (): void => {
  const parentDoc = parentWin.document;

  // 检查按钮是否已存在
  if ($(`#${BUTTON_ID}`, parentDoc).length > 0) {
    console.log('[WorldInfoOptimizer] Extension button already exists, skipping creation.');
    return;
  }

  const buttonHtml = `
    <div id="${BUTTON_ID}" class="list-group-item flex-container flexGap5" title="${BUTTON_TOOLTIP}">
      <div class="fa-solid fa-book extensionsMenuExtensionButton" style="background-image: url('${BUTTON_ICON_URL}'); background-size: contain; background-repeat: no-repeat; background-position: center;"></div>
      世界书优化器
    </div>
  `;

  const $extensionsMenu = $('#extensionsMenu', parentDoc);
  if ($extensionsMenu.length > 0) {
    $extensionsMenu.append(buttonHtml);
    console.log('[WorldInfoOptimizer] Extension button created successfully.');
  } else {
    console.warn('[WorldInfoOptimizer] Extensions menu not found, button not created.');
  }
};

/**
 * 添加基础样式
 */
export const addBasicStyles = (): void => {
  const parentDoc = parentWin.document;

  // 检查样式是否已添加
  if ($('#wio-basic-styles', parentDoc).length > 0) {
    return;
  }

  const styles = `
    <style id="wio-basic-styles">
      .${CSS_CLASSES.PANEL} {
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 90vw;
        max-width: 1200px;
        height: 80vh;
        background: var(--SmartThemeBodyColor);
        border: 1px solid var(--SmartThemeBorderColor);
        border-radius: 10px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        z-index: 10000;
        display: flex;
        flex-direction: column;
        font-family: var(--mainFontFamily);
        color: var(--SmartThemeEmColor);
      }

      .${CSS_CLASSES.PANEL_HEADER} {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 15px 20px;
        background: var(--SmartThemeQuoteColor);
        border-bottom: 1px solid var(--SmartThemeBorderColor);
        border-radius: 10px 10px 0 0;
      }

      .wio-panel-title {
        margin: 0;
        font-size: 18px;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 8px;
      }

      .wio-panel-controls {
        display: flex;
        gap: 8px;
      }

      .${CSS_CLASSES.PANEL_BODY} {
        flex: 1;
        display: flex;
        flex-direction: column;
        overflow: hidden;
      }

      .wio-tabs {
        display: flex;
        background: var(--SmartThemeQuoteColor);
        border-bottom: 1px solid var(--SmartThemeBorderColor);
      }

      .${CSS_CLASSES.TAB_BTN} {
        flex: 1;
        padding: 12px 16px;
        background: transparent;
        border: none;
        color: var(--SmartThemeEmColor);
        cursor: pointer;
        transition: all 0.2s ease;
        font-size: 14px;
        font-weight: 500;
      }

      .${CSS_CLASSES.TAB_BTN}:hover {
        background: var(--SmartThemeBodyColor);
      }

      .${CSS_CLASSES.TAB_BTN}.${CSS_CLASSES.ACTIVE} {
        background: var(--SmartThemeBodyColor);
        border-bottom: 2px solid var(--SmartThemeEmColor);
      }

      .wio-search-container {
        padding: 15px 20px;
        background: var(--SmartThemeQuoteColor);
        border-bottom: 1px solid var(--SmartThemeBorderColor);
      }

      .wio-search-input-wrapper {
        position: relative;
        margin-bottom: 10px;
      }

      .wio-search-input {
        width: 100%;
        padding: 10px 40px 10px 15px;
        border: 1px solid var(--SmartThemeBorderColor);
        border-radius: 6px;
        background: var(--SmartThemeBodyColor);
        color: var(--SmartThemeEmColor);
        font-size: 14px;
      }

      .wio-clear-search {
        position: absolute;
        right: 8px;
        top: 50%;
        transform: translateY(-50%);
        background: transparent;
        border: none;
        color: var(--SmartThemeEmColor);
        cursor: pointer;
        padding: 4px;
      }

      .wio-search-filters {
        display: flex;
        gap: 15px;
        flex-wrap: wrap;
      }

      .wio-search-filters label {
        display: flex;
        align-items: center;
        gap: 5px;
        font-size: 13px;
        cursor: pointer;
      }

      .wio-toolbar {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 10px 20px;
        background: var(--SmartThemeQuoteColor);
        border-bottom: 1px solid var(--SmartThemeBorderColor);
      }

      .wio-toolbar-left, .wio-toolbar-right {
        display: flex;
        align-items: center;
        gap: 10px;
      }

      .wio-content {
        flex: 1;
        overflow-y: auto;
        padding: 20px;
      }

      .wio-btn {
        padding: 8px 16px;
        border: 1px solid var(--SmartThemeBorderColor);
        border-radius: 4px;
        background: var(--SmartThemeBodyColor);
        color: var(--SmartThemeEmColor);
        cursor: pointer;
        font-size: 13px;
        transition: all 0.2s ease;
        display: inline-flex;
        align-items: center;
        gap: 6px;
      }

      .wio-btn:hover {
        background: var(--SmartThemeQuoteColor);
      }

      .wio-btn-icon {
        padding: 8px;
        min-width: 32px;
        justify-content: center;
      }

      .${CSS_CLASSES.TOAST_NOTIFICATION} {
        position: absolute;
        top: 20px;
        right: 20px;
        background: #4CAF50;
        color: white;
        padding: 12px 16px;
        border-radius: 6px;
        display: flex;
        align-items: center;
        gap: 8px;
        opacity: 0;
        transform: translateX(100%);
        transition: all 0.3s ease;
        z-index: 10001;
      }

      .${CSS_CLASSES.TOAST_NOTIFICATION}.visible {
        opacity: 1;
        transform: translateX(0);
      }

      .${CSS_CLASSES.PROGRESS_TOAST} {
        position: absolute;
        top: 20px;
        right: 20px;
        background: var(--SmartThemeBodyColor);
        border: 1px solid var(--SmartThemeBorderColor);
        color: var(--SmartThemeEmColor);
        padding: 12px 16px;
        border-radius: 6px;
        display: flex;
        align-items: center;
        gap: 8px;
        opacity: 0;
        transform: translateX(100%);
        transition: all 0.3s ease;
        z-index: 10001;
      }

      .${CSS_CLASSES.PROGRESS_TOAST}.visible {
        opacity: 1;
        transform: translateX(0);
      }
    </style>
  `;

  $('head', parentDoc).append(styles);
};

// --- 面板显示/隐藏函数 ---

/**
 * 隐藏面板
 */
export const hidePanel = (): void => {
  const parentDoc = parentWin.document;
  const $panel = $(`#${PANEL_ID}`, parentDoc);
  $panel.hide();
  $(`#${BUTTON_ID}`, parentDoc).removeClass(CSS_CLASSES.ACTIVE);
};

/**
 * 显示面板
 */
export const showPanel = async (loadDataCallback: () => Promise<void>): Promise<void> => {
  const parentDoc = parentWin.document;
  const $panel = $(`#${PANEL_ID}`, parentDoc);
  $panel.css('display', 'flex');
  $(`#${BUTTON_ID}`, parentDoc).addClass(CSS_CLASSES.ACTIVE);

  // 如果数据未加载，则加载数据
  if (!appState.isDataLoaded) {
    await loadDataCallback();
  }
};

// --- 渲染函数 ---

/**
 * 渲染内容
 */
export const renderContent = (): void => {
  const $container = $(`#${PANEL_ID}-content`, parentWin.document);
  const searchTerm = $(`#${SEARCH_INPUT_ID}`, parentWin.document).val() as string;

  if (!appState.isDataLoaded) {
    $container.html('<p class="wio-info-text">正在加载数据...</p>');
    return;
  }

  // 根据活动标签页渲染不同内容
  switch (appState.activeTab) {
    case 'global-lore':
      renderGlobalLorebookView(searchTerm, $container);
      break;
    case 'char-lore':
      renderCharacterLorebookView(searchTerm, $container);
      break;
    case 'chat-lore':
      renderChatLorebookView(searchTerm, $container);
      break;
    case 'global-regex':
      renderRegexView(appState.globalRegex, searchTerm, $container, '全局正则');
      break;
    case 'char-regex':
      renderRegexView(appState.characterRegex, searchTerm, $container, '角色正则');
      break;
    default:
      $container.html('<p class="wio-error-text">未知的标签页类型</p>');
  }
};

/**
 * 更新选择计数
 */
export const updateSelectionCount = (count: number): void => {
  const parentDoc = parentWin.document;
  $(`#wio-selection-count`, parentDoc).text(`已选择: ${count}`);
};

/**
 * 更新多选模式视图
 */
export const updateMultiSelectModeView = (isEnabled: boolean): void => {
  const parentDoc = parentWin.document;
  const $panel = $(`#${PANEL_ID}`, parentDoc);
  const $toggleButton = $(`#wio-multi-select-toggle`, parentDoc);
  const $controls = $(`#wio-multi-select-controls`, parentDoc);
  const $selectionCount = $(`#wio-selection-count`, parentDoc);

  if (isEnabled) {
    $panel.addClass('wio-multi-select-mode');
    $toggleButton.addClass(CSS_CLASSES.ACTIVE).html('<i class="fa-solid fa-times"></i> 退出多选');
    $controls.show();
    $selectionCount.show();
  } else {
    $panel.removeClass('wio-multi-select-mode');
    $toggleButton.removeClass(CSS_CLASSES.ACTIVE).html('<i class="fa-solid fa-check-square"></i> 多选模式');
    $controls.hide();
    $selectionCount.hide();
  }
};

/**
 * 更新项目选择视图
 */
export const updateItemSelectionView = (itemKey: string, isSelected: boolean): void => {
  const parentDoc = parentWin.document;
  // Use attribute selector for safety, as itemKey can contain special characters
  const $item = $(`[data-key="${itemKey}"]`, parentDoc);
  if ($item.length > 0) {
    $item.toggleClass('selected', isSelected);
  }
};

/**
 * 渲染全局世界书视图
 */
const renderGlobalLorebookView = (searchTerm: string, $container: any): void => {
  const books = [...appState.allLorebooks].sort(
    (a, b) => Number(b.enabled) - Number(a.enabled) || a.name.localeCompare(b.name),
  );

  const filteredData = getFilteredLorebookData(books, searchTerm);

  if (filteredData.length === 0) {
    $container.html('<p class="wio-info-text">没有找到匹配的世界书。</p>');
    return;
  }

  let html = '';
  filteredData.forEach(({ book, entries, shouldExpand }) => {
    const bookHtml = createBookElement(book, entries, shouldExpand, searchTerm);
    html += bookHtml;
  });

  $container.html(html);
};

/**
 * 渲染角色世界书视图
 */
const renderCharacterLorebookView = (searchTerm: string, $container: any): void => {
  const linkedBooks = appState.lorebooks.character;
  const context = parentWin.SillyTavern.getContext();
  const hasActiveCharacter = context.characterId !== undefined && context.characterId !== null;

  if (!hasActiveCharacter) {
    $container.html('<p class="wio-info-text">没有激活的角色。请先选择一个角色。</p>');
    return;
  }

  if (!Array.isArray(linkedBooks) || linkedBooks.length === 0) {
    $container.html('<p class="wio-info-text">当前角色没有关联的世界书。</p>');
    return;
  }

  const books = linkedBooks.map(bookName => ({
    name: bookName,
    enabled: appState.allLorebooks.find(b => b.name === bookName)?.enabled || false,
  }));

  const filteredData = getFilteredLorebookData(books, searchTerm);

  if (filteredData.length === 0) {
    $container.html('<p class="wio-info-text">没有找到匹配的角色世界书。</p>');
    return;
  }

  let html = '';
  filteredData.forEach(({ book, entries, shouldExpand }) => {
    const bookHtml = createBookElement(book, entries, shouldExpand, searchTerm);
    html += bookHtml;
  });

  $container.html(html);
};

/**
 * 渲染聊天世界书视图
 */
const renderChatLorebookView = (searchTerm: string, $container: any): void => {
  const bookName = appState.chatLorebook;
  const context = parentWin.SillyTavern.getContext();
  const hasActiveChat = context.chatId !== undefined && context.chatId !== null;

  if (!hasActiveChat) {
    $container.html('<p class="wio-info-text">没有激活的聊天。请先开始一个聊天。</p>');
    return;
  }

  if (!bookName) {
    $container.html('<p class="wio-info-text">当前聊天没有关联的世界书。</p>');
    return;
  }

  const book = { name: bookName, enabled: true };
  const entries = safeGetLorebookEntries(bookName);
  const filteredEntries = filterEntries(entries, searchTerm);

  if (filteredEntries.length === 0 && searchTerm) {
    $container.html('<p class="wio-info-text">没有找到匹配的聊天世界书条目。</p>');
    return;
  }

  const bookHtml = createBookElement(book, filteredEntries, !!searchTerm, searchTerm);
  $container.html(bookHtml);
};

// --- 辅助函数 ---

/**
 * 获取过滤后的世界书数据
 */
const getFilteredLorebookData = (books: any[], searchTerm: string) => {
  if (!searchTerm) {
    return books.map(book => ({
      book,
      entries: [...safeGetLorebookEntries(book.name)],
      shouldExpand: false, // 无搜索词时不自动展开
    }));
  }

  return books
    .map(book => {
      const entries = safeGetLorebookEntries(book.name);
      const filteredEntries = filterEntries(entries, searchTerm);

      // 检查书名是否匹配
      const bookNameMatches = isMatch(book.name, searchTerm);

      return {
        book,
        entries: bookNameMatches ? entries : filteredEntries,
        shouldExpand: true, // 有搜索词时自动展开
      };
    })
    .filter(({ book, entries }) => isMatch(book.name, searchTerm) || entries.length > 0);
};

/**
 * 过滤条目
 */
const filterEntries = (entries: LorebookEntry[], searchTerm: string): LorebookEntry[] => {
  if (!searchTerm) return entries;

  return entries.filter(entry => {
    const { searchFilters } = appState;

    if (searchFilters.entryName && isMatch(entry.comment || '', searchTerm)) return true;
    if (searchFilters.keywords && entry.keys?.some(key => isMatch(key, searchTerm))) return true;
    if (searchFilters.content && isMatch(entry.content || '', searchTerm)) return true;

    return false;
  });
};

/**
 * 创建世界书元素
 */
const createBookElement = (book: any, entries: LorebookEntry[], shouldExpand: boolean, searchTerm: string): string => {
  const bookKey = createBookItemKey(book.name);
  const entriesHtml = entries.map(entry => createItemElement(entry, 'lore', book.name, searchTerm)).join('');

  return `
    <div class="${CSS_CLASSES.BOOK_GROUP} ${shouldExpand ? '' : CSS_CLASSES.COLLAPSED}" data-book-name="${escapeHtml(book.name)}" data-key="${bookKey}">
      <div class="${CSS_CLASSES.ITEM_HEADER}">
        <div class="${CSS_CLASSES.ITEM_NAME}">
          ${highlightText(escapeHtml(book.name), searchTerm)}
          <span class="wio-entry-count">(${entries.length})</span>
        </div>
        <div class="${CSS_CLASSES.ITEM_CONTROLS}">
          <button class="wio-btn wio-btn-icon wio-toggle-state" title="${book.enabled ? '禁用' : '启用'}">
            <i class="fa-solid ${book.enabled ? 'fa-eye' : 'fa-eye-slash'}"></i>
          </button>
        </div>
      </div>
      <div class="${CSS_CLASSES.COLLAPSIBLE_CONTENT}" ${shouldExpand ? 'style="display: block;"' : ''}>
        ${entriesHtml || '<p class="wio-info-text">此世界书没有条目。</p>'}
      </div>
    </div>
  `;
};

/**
 * 创建项目元素
 */
const createItemElement = (item: LorebookEntry | any, type: string, bookName = '', searchTerm = ''): string => {
  const isLore = type === 'lore';
  const id = isLore ? item.uid : item.id;
  const name = isLore ? item.comment || '无标题条目' : item.script_name || '未命名正则';

  let itemKey = '';
  if (isLore) {
    itemKey = createEntryItemKey(bookName, id);
  } else {
    // Assuming 'regex' type
    const scope = appState.activeTab === 'global-regex' ? 'global' : 'character';
    itemKey = createRegexItemKey(scope, id);
  }

  return `
    <div class="${CSS_CLASSES.ITEM_CONTAINER}" data-type="${type}" data-id="${id}" data-book-name="${escapeHtml(bookName)}" data-key="${itemKey}">
      <div class="${CSS_CLASSES.ITEM_HEADER}">
        <div class="${CSS_CLASSES.ITEM_NAME}">
          ${highlightText(escapeHtml(name), searchTerm)}
        </div>
        <div class="${CSS_CLASSES.ITEM_CONTROLS}">
          <button class="wio-btn wio-btn-icon wio-toggle-state" title="${item.enabled ? '禁用' : '启用'}">
            <i class="fa-solid ${item.enabled ? 'fa-eye' : 'fa-eye-slash'}"></i>
          </button>
        </div>
      </div>
    </div>
  `;
};

/**
 * 渲染正则表达式视图
 */
const renderRegexView = (regexes: any[], searchTerm: string, $container: any, title: string): void => {
  if (regexes.length === 0) {
    $container.html(`<p class="wio-info-text">没有找到${title}。</p>`);
    return;
  }

  const filteredRegexes = regexes.filter(regex => {
    if (!searchTerm) return true;
    return (
      isMatch(regex.script_name || '', searchTerm) ||
      isMatch(regex.find_regex || '', searchTerm) ||
      isMatch(regex.replace_string || '', searchTerm)
    );
  });

  if (filteredRegexes.length === 0) {
    $container.html(`<p class="wio-info-text">没有找到匹配的${title}。</p>`);
    return;
  }

  const regexHtml = filteredRegexes.map(regex => createItemElement(regex, 'regex', '', searchTerm)).join('');
  $container.html(regexHtml);
};
