/**
 * 世界书优化器常量定义
 * 包含所有配置常量、ID定义、选项配置等
 */

import type { LorebookOptions } from './types';

// --- 脚本基础配置 ---
export const SCRIPT_VERSION_TAG = 'v1_0_0';

// --- UI 元素 ID 定义 ---
export const PANEL_ID = 'world-info-optimizer-panel';
export const BUTTON_ID = 'world-info-optimizer-button';
export const SEARCH_INPUT_ID = 'wio-search-input';
export const REFRESH_BTN_ID = 'wio-refresh-btn';
export const COLLAPSE_CURRENT_BTN_ID = 'wio-collapse-current-btn';
export const COLLAPSE_ALL_BTN_ID = 'wio-collapse-all-btn';
export const CREATE_LOREBOOK_BTN_ID = 'wio-create-lorebook-btn';

// --- 按钮配置 ---
export const BUTTON_ICON_URL = 'https://i.postimg.cc/bY23wb9Y/IMG-20250626-000247.png';
export const BUTTON_TOOLTIP = '世界书优化器';
export const BUTTON_TEXT_IN_MENU = '世界书优化器';

// --- 世界书选项配置 ---
export const LOREBOOK_OPTIONS: LorebookOptions = {
  position: {
    before_character_definition: '角色定义前',
    after_character_definition: '角色定义后',
    before_example_messages: '聊天示例前',
    after_example_messages: '聊天示例后',
    before_author_note: '作者笔记前',
    after_author_note: '作者笔记后',
    at_depth_as_system: '@D ⚙ 系统',
    at_depth_as_assistant: '@D 🗨️ 角色',
    at_depth_as_user: '@D 👤 用户',
  },
  logic: {
    and_any: '任一 AND',
    and_all: '所有 AND',
    not_any: '任一 NOT',
    not_all: '所有 NOT',
  },
};

// --- 项目类型前缀枚举 ---
export enum ItemTypePrefix {
  BOOK = 'book',
  ENTRY = 'entry',
  REGEX = 'regex',
}

// --- 默认配置 ---
export const DEFAULT_SEARCH_FILTERS = {
  bookName: true,
  entryName: true,
  keywords: true,
  content: true,
};

export const DEFAULT_TAB = 'global-lore';

// --- 操作超时配置 ---
export const API_TIMEOUT = 30000; // 30秒
export const DEBOUNCE_DELAY = 300; // 300毫秒

// --- UI 动画配置 ---
export const ANIMATION_DURATION = 200; // 200毫秒
export const TOAST_DURATION = 1500; // 1.5秒

// --- 样式类名 ---
export const CSS_CLASSES = {
  PANEL: 'wio-panel',
  PANEL_HEADER: 'wio-panel-header',
  PANEL_BODY: 'wio-panel-body',
  PANEL_CLOSE: 'wio-panel-close',
  TAB_BTN: 'wio-tab-btn',
  TAB_CONTENT: 'wio-tab-content',
  ITEM_CONTAINER: 'wio-item-container',
  ITEM_HEADER: 'wio-item-header',
  ITEM_NAME: 'wio-item-name',
  ITEM_CONTROLS: 'wio-item-controls',
  BOOK_GROUP: 'wio-book-group',
  COLLAPSIBLE_CONTENT: 'wio-collapsible-content',
  LOADING_CONTAINER: 'wio-loading-container',
  MODAL_OVERLAY: 'wio-modal-overlay',
  MODAL_CONTENT: 'wio-modal-content',
  TOAST_NOTIFICATION: 'wio-toast-notification',
  PROGRESS_TOAST: 'wio-progress-toast',
  HIGHLIGHT: 'wio-highlight',
  SELECTED: 'selected',
  ACTIVE: 'active',
  COLLAPSED: 'collapsed',
  RENAMING: 'renaming',
  EDITING_ENTRIES: 'editing-entries',
} as const;

// --- 消息文本 ---
export const MESSAGES = {
  LOADING: {
    INITIALIZING: '正在初始化...',
    CONNECTING_API: '正在连接 SillyTavern API...',
    FETCHING_CORE_DATA: '正在获取核心设置...',
    ANALYZING_DATA: '核心数据已获取，正在分析...',
    LOADING_LOREBOOKS: '正在加载世界书...',
    PROCESSING_ENTRIES: '正在处理条目...',
    FINALIZING: '正在完成最后的处理...',
    COMPLETE: '数据加载完成！',
  },
  SUCCESS: {
    OPERATION_SUCCESS: '操作成功',
    DATA_REFRESHED: '数据已刷新',
    ENTRY_CREATED: '条目创建成功',
    ENTRY_DELETED: '条目删除成功',
    BOOK_CREATED: '世界书创建成功',
    BOOK_DELETED: '世界书删除成功',
    BOOK_RENAMED: '世界书重命名成功',
    BATCH_ENABLED: '批量启用完成',
    BATCH_DISABLED: '批量禁用完成',
    REPLACE_COMPLETE: '替换操作完成',
  },
  ERROR: {
    OPERATION_FAILED: '操作失败',
    API_ERROR: 'API调用失败',
    INVALID_INPUT: '输入无效',
    NO_SELECTION: '请先选择项目',
    DUPLICATE_NAME: '名称已存在',
    EMPTY_NAME: '名称不能为空',
  },
  CONFIRM: {
    DELETE_ENTRY: '确定要删除这个条目吗？',
    DELETE_BOOK: '确定要删除这个世界书吗？这将删除其中的所有条目。',
    BATCH_DELETE: '确定要删除选中的项目吗？',
    REPLACE_CONFIRM: '确定要执行替换操作吗？',
  },
} as const;

// --- 正则表达式 ---
export const REGEX_PATTERNS = {
  SAFE_ID: /[+/=]/g,
  HTML_ESCAPE: /[&<>"']/g,
  SEARCH_HIGHLIGHT: /<\/?mark\s+class="wio-highlight">/gi,
} as const;

// --- 限制配置 ---
export const LIMITS = {
  MAX_RETRIES: 100,
  MAX_ENTRY_NAME_LENGTH: 100,
  MAX_BOOK_NAME_LENGTH: 50,
  MAX_SEARCH_RESULTS: 1000,
} as const;
