var __webpack_modules__ = {
  "./src/WorldInfoOptimizer/api.ts": 
  /*!***************************************!*\
  !*** ./src/WorldInfoOptimizer/api.ts ***!
  \***************************************/ (__unused_webpack_module, __webpack_exports__, __webpack_require__) => {
    eval("{__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TavernAPI: () => (/* binding */ TavernAPI),\n/* harmony export */   createLorebook: () => (/* binding */ createLorebook),\n/* harmony export */   createLorebookEntry: () => (/* binding */ createLorebookEntry),\n/* harmony export */   deleteLorebook: () => (/* binding */ deleteLorebook),\n/* harmony export */   deleteLorebookEntries: () => (/* binding */ deleteLorebookEntries),\n/* harmony export */   deleteLorebookEntry: () => (/* binding */ deleteLorebookEntry),\n/* harmony export */   initializeTavernAPI: () => (/* binding */ initializeTavernAPI),\n/* harmony export */   loadAllData: () => (/* binding */ loadAllData),\n/* harmony export */   refreshData: () => (/* binding */ refreshData),\n/* harmony export */   updateLorebookEntries: () => (/* binding */ updateLorebookEntries)\n/* harmony export */ });\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./constants */ \"./src/WorldInfoOptimizer/constants.ts\");\n/* harmony import */ var _state__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./state */ \"./src/WorldInfoOptimizer/state.ts\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./utils */ \"./src/WorldInfoOptimizer/utils.ts\");\n/**\n * 世界书优化器 API 交互模块\n * 包含所有与 SillyTavern API 交互的函数\n */\n\n\n\n// --- 全局变量 ---\n// --- API 包装器 ---\nlet TavernAPI = null;\n/**\n * 初始化 TavernAPI\n */\nconst initializeTavernAPI = (dependencies) => {\n    const { $, parentWin, TavernHelper } = dependencies;\n    TavernAPI = {\n        createLorebook: (0,_utils__WEBPACK_IMPORTED_MODULE_2__.errorCatched)(async (name) => await TavernHelper.createLorebook(name), 'createLorebook'),\n        deleteLorebook: (0,_utils__WEBPACK_IMPORTED_MODULE_2__.errorCatched)(async (name) => await TavernHelper.deleteLorebook(name), 'deleteLorebook'),\n        getLorebooks: (0,_utils__WEBPACK_IMPORTED_MODULE_2__.errorCatched)(async () => await TavernHelper.getLorebooks(), 'getLorebooks'),\n        setLorebookSettings: (0,_utils__WEBPACK_IMPORTED_MODULE_2__.errorCatched)(async (settings) => await TavernHelper.setLorebookSettings(settings), 'setLorebookSettings'),\n        getCharData: (0,_utils__WEBPACK_IMPORTED_MODULE_2__.errorCatched)(async () => await TavernHelper.getCharData(), 'getCharData'),\n        Character: TavernHelper.Character || null,\n        getRegexes: (0,_utils__WEBPACK_IMPORTED_MODULE_2__.errorCatched)(async () => await TavernHelper.getTavernRegexes({ scope: 'all' }), 'getRegexes'),\n        replaceRegexes: (0,_utils__WEBPACK_IMPORTED_MODULE_2__.errorCatched)(async (regexes) => await TavernHelper.replaceTavernRegexes(regexes, { scope: 'all' }), 'replaceRegexes'),\n        createLorebookEntries: (0,_utils__WEBPACK_IMPORTED_MODULE_2__.errorCatched)(async (bookName, entries) => await TavernHelper.createLorebookEntries(bookName, entries), 'createLorebookEntries'),\n        setLorebookEntries: (0,_utils__WEBPACK_IMPORTED_MODULE_2__.errorCatched)(async (bookName, entries) => await TavernHelper.setLorebookEntries(bookName, entries), 'setLorebookEntries'),\n        deleteLorebookEntry: (0,_utils__WEBPACK_IMPORTED_MODULE_2__.errorCatched)(async (bookName, uid) => await TavernHelper.deleteLorebookEntry(bookName, uid), 'deleteLorebookEntry'),\n        getLorebookSettings: (0,_utils__WEBPACK_IMPORTED_MODULE_2__.errorCatched)(async () => await TavernHelper.getLorebookSettings(), 'getLorebookSettings'),\n        getCurrentCharLorebooks: (0,_utils__WEBPACK_IMPORTED_MODULE_2__.errorCatched)(async () => await TavernHelper.getCurrentCharLorebooks(), 'getCurrentCharLorebooks'),\n        getChatLorebook: (0,_utils__WEBPACK_IMPORTED_MODULE_2__.errorCatched)(async () => await TavernHelper.getChatLorebook(), 'getChatLorebook'),\n    };\n};\n/**\n * 更新进度显示\n */\n/**\n * 加载所有数据\n */\nconst loadAllData = (0,_utils__WEBPACK_IMPORTED_MODULE_2__.errorCatched)(async (dependencies) => {\n    const { $, parentWin } = dependencies;\n    const $content = $(`#${_constants__WEBPACK_IMPORTED_MODULE_0__.PANEL_ID}-content`, parentWin.document);\n    $content.html(`\r\n    <div class=\"wio-loading-container\">\r\n      <div class=\"wio-loading-title\">数据同步中...</div>\r\n      <div class=\"wio-loading-progress-bar-container\">\r\n        <div id=\"wio-loading-bar\" class=\"wio-loading-progress-bar\" style=\"width: 0%;\"></div>\r\n      </div>\r\n      <div id=\"wio-loading-status\" class=\"wio-loading-status-text\">${_constants__WEBPACK_IMPORTED_MODULE_0__.MESSAGES.LOADING.INITIALIZING}</div>\r\n    </div>\r\n  `);\n    try {\n        if (!parentWin.SillyTavern || !parentWin.SillyTavern.getContext) {\n            console.warn('[WorldInfoOptimizer] SillyTavern API not available, initializing with empty data');\n            return {\n                globalRegex: [],\n                characterRegex: [],\n                allLorebooks: [],\n                characterLorebooks: [],\n                chatLorebook: null,\n                lorebookEntries: new Map(),\n            };\n        }\n        const context = parentWin.SillyTavern.getContext() || {};\n        const hasActiveCharacter = context.characterId !== undefined && context.characterId !== null;\n        const hasActiveChat = context.chatId !== undefined && context.chatId !== null;\n        const promises = [\n            TavernAPI.getRegexes().catch(() => []),\n            TavernAPI.getLorebookSettings().catch(() => ({})),\n            TavernAPI.getLorebooks().catch(() => []),\n            hasActiveCharacter ? TavernAPI.getCharData().catch(() => null) : Promise.resolve(null),\n            hasActiveCharacter ? TavernAPI.getCurrentCharLorebooks().catch(() => []) : Promise.resolve([]),\n            hasActiveChat ? TavernAPI.getChatLorebook().catch(() => null) : Promise.resolve(null),\n        ];\n        const results = await Promise.allSettled(promises);\n        const allUirRegexes = results[0].status === 'fulfilled' ? results[0].value : [];\n        const globalSettings = results[1].status === 'fulfilled' ? results[1].value : {};\n        const allBookFileNames = results[2].status === 'fulfilled' ? results[2].value : [];\n        const charData = results[3].status === 'fulfilled' ? results[3].value : null;\n        const charLinkedBooks = results[4].status === 'fulfilled' ? results[4].value : [];\n        const chatLorebook = results[5].status === 'fulfilled' ? results[5].value : null;\n        const globalRegex = allUirRegexes.filter(r => r.scope === 'global');\n        const characterRegex = allUirRegexes.filter(r => r.scope === 'character');\n        const allLorebooks = allBookFileNames.map(name => ({\n            name,\n            enabled: globalSettings.world_info_include?.includes(name) || false,\n        }));\n        const lorebookEntries = await loadLorebookEntries(allBookFileNames, parentWin);\n        const payload = {\n            globalRegex,\n            characterRegex,\n            allLorebooks,\n            characterLorebooks: charLinkedBooks,\n            chatLorebook,\n            lorebookEntries,\n        };\n        return payload;\n    }\n    catch (error) {\n        console.error('[WorldInfoOptimizer] Error loading data:', error);\n        setTimeout(() => {\n            $content.html('<p class=\"wio-error-text\">数据加载失败，请点击刷新按钮重试。</p>');\n        }, 1000);\n        throw error;\n    }\n}, 'loadAllData');\n/**\n * 加载世界书条目\n */\nconst loadLorebookEntries = async (bookNames, parentWin) => {\n    const entriesMap = new Map();\n    if (!Array.isArray(bookNames) || bookNames.length === 0) {\n        console.log('[WorldInfoOptimizer] No lorebooks to load entries for');\n        return entriesMap;\n    }\n    const batchSize = 5;\n    const totalBooks = bookNames.length;\n    let processedBooks = 0;\n    for (let i = 0; i < bookNames.length; i += batchSize) {\n        const batch = bookNames.slice(i, i + batchSize);\n        const batchPromises = batch.map(async (bookName) => {\n            try {\n                const entries = await TavernHelper.getLorebookEntries(bookName);\n                if (Array.isArray(entries)) {\n                    entriesMap.set(bookName, entries);\n                }\n            }\n            catch (error) {\n                console.warn(`[WorldInfoOptimizer] Failed to load entries for ${bookName}:`, error);\n            }\n            finally {\n                processedBooks++;\n                const progress = 50 + (processedBooks / totalBooks) * 40;\n            }\n        });\n        await Promise.allSettled(batchPromises);\n        if (i + batchSize < bookNames.length) {\n            await new Promise(resolve => setTimeout(resolve, 100));\n        }\n    }\n    console.log(`[WorldInfoOptimizer] Loaded entries for ${processedBooks} lorebooks`);\n    return entriesMap;\n};\n/**\n * 刷新数据\n */\nconst refreshData = async (renderContent) => {\n    console.log('[WorldInfoOptimizer] Refreshing data...');\n    (0,_state__WEBPACK_IMPORTED_MODULE_1__.setDataLoaded)(false);\n    const data = await loadAllData({ $: window.$, parentWin: window.parent });\n    //  hydrateAppState(data); // This will be added in the next step\n    renderContent();\n};\n/**\n * 创建世界书\n */\nconst createLorebook = async (name) => {\n    try {\n        await TavernAPI.createLorebook(name);\n        return true;\n    }\n    catch (error) {\n        console.error('[WorldInfoOptimizer] Failed to create lorebook:', error);\n        return false;\n    }\n};\n/**\n * 删除世界书\n */\nconst deleteLorebook = async (name) => {\n    try {\n        await TavernAPI.deleteLorebook(name);\n        return true;\n    }\n    catch (error) {\n        console.error('[WorldInfoOptimizer] Failed to delete lorebook:', error);\n        return false;\n    }\n};\n/**\n * 创建世界书条目\n */\nconst createLorebookEntry = async (bookName, entryData) => {\n    try {\n        await TavernAPI.createLorebookEntries(bookName, [entryData]);\n        return true;\n    }\n    catch (error) {\n        console.error('[WorldInfoOptimizer] Failed to create lorebook entry:', error);\n        return false;\n    }\n};\n/**\n * 删除世界书条目\n */\nconst deleteLorebookEntry = async (bookName, uid) => {\n    try {\n        await TavernAPI.deleteLorebookEntry(bookName, uid);\n        return true;\n    }\n    catch (error) {\n        console.error('[WorldInfoOptimizer] Failed to delete lorebook entry:', error);\n        return false;\n    }\n};\n/**\n * 批量删除世界书条目\n */\nconst deleteLorebookEntries = async (bookName, uids) => {\n    try {\n        // TavernAPI可能没有批量删除的端点，所以我们并行执行单个删除\n        const deletePromises = uids.map(uid => TavernAPI.deleteLorebookEntry(bookName, uid));\n        await Promise.all(deletePromises);\n        return true;\n    }\n    catch (error) {\n        console.error(`[WorldInfoOptimizer] Failed to delete lorebook entries from book \"${bookName}\":`, error);\n        return false;\n    }\n};\n/**\n * 更新世界书条目\n */\nconst updateLorebookEntries = async (bookName, entries) => {\n    try {\n        await TavernAPI.setLorebookEntries(bookName, entries);\n        return true;\n    }\n    catch (error) {\n        console.error('[WorldInfoOptimizer] Failed to update lorebook entries:', error);\n        return false;\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/WorldInfoOptimizer/api.ts\n\n}");
  },
  "./src/WorldInfoOptimizer/constants.ts": 
  /*!*********************************************!*\
  !*** ./src/WorldInfoOptimizer/constants.ts ***!
  \*********************************************/ (__unused_webpack_module, __webpack_exports__, __webpack_require__) => {
    eval("{__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ANIMATION_DURATION: () => (/* binding */ ANIMATION_DURATION),\n/* harmony export */   API_TIMEOUT: () => (/* binding */ API_TIMEOUT),\n/* harmony export */   BUTTON_ICON_URL: () => (/* binding */ BUTTON_ICON_URL),\n/* harmony export */   BUTTON_ID: () => (/* binding */ BUTTON_ID),\n/* harmony export */   BUTTON_TEXT_IN_MENU: () => (/* binding */ BUTTON_TEXT_IN_MENU),\n/* harmony export */   BUTTON_TOOLTIP: () => (/* binding */ BUTTON_TOOLTIP),\n/* harmony export */   COLLAPSE_ALL_BTN_ID: () => (/* binding */ COLLAPSE_ALL_BTN_ID),\n/* harmony export */   COLLAPSE_CURRENT_BTN_ID: () => (/* binding */ COLLAPSE_CURRENT_BTN_ID),\n/* harmony export */   CREATE_LOREBOOK_BTN_ID: () => (/* binding */ CREATE_LOREBOOK_BTN_ID),\n/* harmony export */   CSS_CLASSES: () => (/* binding */ CSS_CLASSES),\n/* harmony export */   DEBOUNCE_DELAY: () => (/* binding */ DEBOUNCE_DELAY),\n/* harmony export */   DEFAULT_SEARCH_FILTERS: () => (/* binding */ DEFAULT_SEARCH_FILTERS),\n/* harmony export */   DEFAULT_TAB: () => (/* binding */ DEFAULT_TAB),\n/* harmony export */   ItemTypePrefix: () => (/* binding */ ItemTypePrefix),\n/* harmony export */   LIMITS: () => (/* binding */ LIMITS),\n/* harmony export */   LOREBOOK_OPTIONS: () => (/* binding */ LOREBOOK_OPTIONS),\n/* harmony export */   MESSAGES: () => (/* binding */ MESSAGES),\n/* harmony export */   PANEL_ID: () => (/* binding */ PANEL_ID),\n/* harmony export */   REFRESH_BTN_ID: () => (/* binding */ REFRESH_BTN_ID),\n/* harmony export */   REGEX_PATTERNS: () => (/* binding */ REGEX_PATTERNS),\n/* harmony export */   SCRIPT_VERSION_TAG: () => (/* binding */ SCRIPT_VERSION_TAG),\n/* harmony export */   SEARCH_INPUT_ID: () => (/* binding */ SEARCH_INPUT_ID),\n/* harmony export */   TOAST_DURATION: () => (/* binding */ TOAST_DURATION)\n/* harmony export */ });\n/**\n * 世界书优化器常量定义\n * 包含所有配置常量、ID定义、选项配置等\n */\n// --- 脚本基础配置 ---\nconst SCRIPT_VERSION_TAG = 'v1_0_0';\n// --- UI 元素 ID 定义 ---\nconst PANEL_ID = 'world-info-optimizer-panel';\nconst BUTTON_ID = 'world-info-optimizer-button';\nconst SEARCH_INPUT_ID = 'wio-search-input';\nconst REFRESH_BTN_ID = 'wio-refresh-btn';\nconst COLLAPSE_CURRENT_BTN_ID = 'wio-collapse-current-btn';\nconst COLLAPSE_ALL_BTN_ID = 'wio-collapse-all-btn';\nconst CREATE_LOREBOOK_BTN_ID = 'wio-create-lorebook-btn';\n// --- 按钮配置 ---\nconst BUTTON_ICON_URL = 'https://i.postimg.cc/bY23wb9Y/IMG-20250626-000247.png';\nconst BUTTON_TOOLTIP = '世界书优化器';\nconst BUTTON_TEXT_IN_MENU = '世界书优化器';\n// --- 世界书选项配置 ---\nconst LOREBOOK_OPTIONS = {\n    position: {\n        before_character_definition: '角色定义前',\n        after_character_definition: '角色定义后',\n        before_example_messages: '聊天示例前',\n        after_example_messages: '聊天示例后',\n        before_author_note: '作者笔记前',\n        after_author_note: '作者笔记后',\n        at_depth_as_system: '@D ⚙ 系统',\n        at_depth_as_assistant: '@D 🗨️ 角色',\n        at_depth_as_user: '@D 👤 用户',\n    },\n    logic: {\n        and_any: '任一 AND',\n        and_all: '所有 AND',\n        not_any: '任一 NOT',\n        not_all: '所有 NOT',\n    },\n};\n// --- 项目类型前缀枚举 ---\nvar ItemTypePrefix;\n(function (ItemTypePrefix) {\n    ItemTypePrefix[\"BOOK\"] = \"book\";\n    ItemTypePrefix[\"ENTRY\"] = \"entry\";\n    ItemTypePrefix[\"REGEX\"] = \"regex\";\n})(ItemTypePrefix || (ItemTypePrefix = {}));\n// --- 默认配置 ---\nconst DEFAULT_SEARCH_FILTERS = {\n    bookName: true,\n    entryName: true,\n    keywords: true,\n    content: true,\n};\nconst DEFAULT_TAB = 'global-lore';\n// --- 操作超时配置 ---\nconst API_TIMEOUT = 30000; // 30秒\nconst DEBOUNCE_DELAY = 300; // 300毫秒\n// --- UI 动画配置 ---\nconst ANIMATION_DURATION = 200; // 200毫秒\nconst TOAST_DURATION = 1500; // 1.5秒\n// --- 样式类名 ---\nconst CSS_CLASSES = {\n    PANEL: 'wio-panel',\n    PANEL_HEADER: 'wio-panel-header',\n    PANEL_BODY: 'wio-panel-body',\n    PANEL_CLOSE: 'wio-panel-close',\n    TAB_BTN: 'wio-tab-btn',\n    TAB_CONTENT: 'wio-tab-content',\n    ITEM_CONTAINER: 'wio-item-container',\n    ITEM_HEADER: 'wio-item-header',\n    ITEM_NAME: 'wio-item-name',\n    ITEM_CONTROLS: 'wio-item-controls',\n    BOOK_GROUP: 'wio-book-group',\n    COLLAPSIBLE_CONTENT: 'wio-collapsible-content',\n    LOADING_CONTAINER: 'wio-loading-container',\n    MODAL_OVERLAY: 'wio-modal-overlay',\n    MODAL_CONTENT: 'wio-modal-content',\n    TOAST_NOTIFICATION: 'wio-toast-notification',\n    PROGRESS_TOAST: 'wio-progress-toast',\n    HIGHLIGHT: 'wio-highlight',\n    SELECTED: 'selected',\n    ACTIVE: 'active',\n    COLLAPSED: 'collapsed',\n    RENAMING: 'renaming',\n    EDITING_ENTRIES: 'editing-entries',\n};\n// --- 消息文本 ---\nconst MESSAGES = {\n    LOADING: {\n        INITIALIZING: '正在初始化...',\n        CONNECTING_API: '正在连接 SillyTavern API...',\n        FETCHING_CORE_DATA: '正在获取核心设置...',\n        ANALYZING_DATA: '核心数据已获取，正在分析...',\n        LOADING_LOREBOOKS: '正在加载世界书...',\n        PROCESSING_ENTRIES: '正在处理条目...',\n        FINALIZING: '正在完成最后的处理...',\n        COMPLETE: '数据加载完成！',\n    },\n    SUCCESS: {\n        OPERATION_SUCCESS: '操作成功',\n        DATA_REFRESHED: '数据已刷新',\n        ENTRY_CREATED: '条目创建成功',\n        ENTRY_DELETED: '条目删除成功',\n        BOOK_CREATED: '世界书创建成功',\n        BOOK_DELETED: '世界书删除成功',\n        BOOK_RENAMED: '世界书重命名成功',\n        BATCH_ENABLED: '批量启用完成',\n        BATCH_DISABLED: '批量禁用完成',\n        REPLACE_COMPLETE: '替换操作完成',\n    },\n    ERROR: {\n        OPERATION_FAILED: '操作失败',\n        API_ERROR: 'API调用失败',\n        INVALID_INPUT: '输入无效',\n        NO_SELECTION: '请先选择项目',\n        DUPLICATE_NAME: '名称已存在',\n        EMPTY_NAME: '名称不能为空',\n    },\n    CONFIRM: {\n        DELETE_ENTRY: '确定要删除这个条目吗？',\n        DELETE_BOOK: '确定要删除这个世界书吗？这将删除其中的所有条目。',\n        BATCH_DELETE: '确定要删除选中的项目吗？',\n        REPLACE_CONFIRM: '确定要执行替换操作吗？',\n    },\n};\n// --- 正则表达式 ---\nconst REGEX_PATTERNS = {\n    SAFE_ID: /[+/=]/g,\n    HTML_ESCAPE: /[&<>\"']/g,\n    SEARCH_HIGHLIGHT: /<\\/?mark\\s+class=\"wio-highlight\">/gi,\n};\n// --- 限制配置 ---\nconst LIMITS = {\n    MAX_RETRIES: 100,\n    MAX_ENTRY_NAME_LENGTH: 100,\n    MAX_BOOK_NAME_LENGTH: 50,\n    MAX_SEARCH_RESULTS: 1000,\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/WorldInfoOptimizer/constants.ts\n\n}");
  },
  "./src/WorldInfoOptimizer/events.ts": 
  /*!******************************************!*\
  !*** ./src/WorldInfoOptimizer/events.ts ***!
  \******************************************/ (__unused_webpack_module, __webpack_exports__, __webpack_require__) => {
    eval("{__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   bindEventHandlers: () => (/* binding */ bindEventHandlers),\n/* harmony export */   handleBatchDelete: () => (/* binding */ handleBatchDelete),\n/* harmony export */   setGlobalDependencies: () => (/* binding */ setGlobalDependencies)\n/* harmony export */ });\n/* harmony import */ var _api__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./api */ \"./src/WorldInfoOptimizer/api.ts\");\n/* harmony import */ var _state__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./state */ \"./src/WorldInfoOptimizer/state.ts\");\n/* harmony import */ var _ui__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ui */ \"./src/WorldInfoOptimizer/ui.ts\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./utils */ \"./src/WorldInfoOptimizer/utils.ts\");\n/* harmony import */ var _keyManager__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./keyManager */ \"./src/WorldInfoOptimizer/keyManager.ts\");\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./constants */ \"./src/WorldInfoOptimizer/constants.ts\");\n/**\n * 世界书优化器事件处理模块\n * 包含所有事件处理函数和事件绑定逻辑\n */\n\n\n\n\n\n\n// --- 策略地图 ---\n/**\n * 批量启用操作策略地图\n */\nconst batchEnableOperations = {\n    [_constants__WEBPACK_IMPORTED_MODULE_5__.ItemTypePrefix.BOOK]: async (parsedKey) => {\n        if (!parsedKey.bookName)\n            return false;\n        // 更新本地状态\n        const book = _state__WEBPACK_IMPORTED_MODULE_1__.appState.allLorebooks.find(b => b.name === parsedKey.bookName);\n        if (book) {\n            book.enabled = true;\n        }\n        // 更新全局设置并调用API\n        try {\n            const globalSettings = await _api__WEBPACK_IMPORTED_MODULE_0__.TavernAPI.getLorebookSettings();\n            if (!globalSettings.world_info_include) {\n                globalSettings.world_info_include = [];\n            }\n            if (!globalSettings.world_info_include.includes(parsedKey.bookName)) {\n                globalSettings.world_info_include.push(parsedKey.bookName);\n                await _api__WEBPACK_IMPORTED_MODULE_0__.TavernAPI.setLorebookSettings(globalSettings);\n            }\n            return true;\n        }\n        catch (error) {\n            console.error('[WorldInfoOptimizer] Error enabling book:', error);\n            return false;\n        }\n    },\n    [_constants__WEBPACK_IMPORTED_MODULE_5__.ItemTypePrefix.ENTRY]: async (parsedKey) => {\n        if (!parsedKey.bookName || !parsedKey.entryId)\n            return false;\n        const entries = (0,_state__WEBPACK_IMPORTED_MODULE_1__.safeGetLorebookEntries)(parsedKey.bookName);\n        const entry = entries.find(e => e.uid === parsedKey.entryId);\n        if (entry) {\n            entry.enabled = true;\n            try {\n                await _api__WEBPACK_IMPORTED_MODULE_0__.TavernAPI.setLorebookEntries(parsedKey.bookName, [{ uid: parsedKey.entryId, enabled: true }]);\n                return true;\n            }\n            catch (error) {\n                console.error('[WorldInfoOptimizer] Error enabling entry:', error);\n                return false;\n            }\n        }\n        return false;\n    },\n    [_constants__WEBPACK_IMPORTED_MODULE_5__.ItemTypePrefix.REGEX]: async (_parsedKey) => {\n        // 正则表达式不支持启用/禁用操作\n        return false;\n    },\n};\n/**\n * 批量禁用操作策略地图\n */\nconst batchDisableOperations = {\n    [_constants__WEBPACK_IMPORTED_MODULE_5__.ItemTypePrefix.BOOK]: async (parsedKey) => {\n        if (!parsedKey.bookName)\n            return false;\n        // 更新本地状态\n        const book = _state__WEBPACK_IMPORTED_MODULE_1__.appState.allLorebooks.find(b => b.name === parsedKey.bookName);\n        if (book) {\n            book.enabled = false;\n        }\n        // 更新全局设置并调用API\n        try {\n            const globalSettings = await _api__WEBPACK_IMPORTED_MODULE_0__.TavernAPI.getLorebookSettings();\n            if (globalSettings.world_info_include) {\n                globalSettings.world_info_include = globalSettings.world_info_include.filter((name) => name !== parsedKey.bookName);\n                await _api__WEBPACK_IMPORTED_MODULE_0__.TavernAPI.setLorebookSettings(globalSettings);\n            }\n            return true;\n        }\n        catch (error) {\n            console.error('[WorldInfoOptimizer] Error disabling book:', error);\n            return false;\n        }\n    },\n    [_constants__WEBPACK_IMPORTED_MODULE_5__.ItemTypePrefix.ENTRY]: async (parsedKey) => {\n        if (!parsedKey.bookName || !parsedKey.entryId)\n            return false;\n        const entries = (0,_state__WEBPACK_IMPORTED_MODULE_1__.safeGetLorebookEntries)(parsedKey.bookName);\n        const entry = entries.find(e => e.uid === parsedKey.entryId);\n        if (entry) {\n            entry.enabled = false;\n            try {\n                await _api__WEBPACK_IMPORTED_MODULE_0__.TavernAPI.setLorebookEntries(parsedKey.bookName, [{ uid: parsedKey.entryId, enabled: false }]);\n                return true;\n            }\n            catch (error) {\n                console.error('[WorldInfoOptimizer] Error disabling entry:', error);\n                return false;\n            }\n        }\n        return false;\n    },\n    [_constants__WEBPACK_IMPORTED_MODULE_5__.ItemTypePrefix.REGEX]: async (_parsedKey) => {\n        // 正则表达式不支持启用/禁用操作\n        return false;\n    },\n};\n/**\n * 批量删除操作策略地图\n */\nconst batchDeleteOperations = {\n    [_constants__WEBPACK_IMPORTED_MODULE_5__.ItemTypePrefix.BOOK]: async (parsedKey) => {\n        if (!parsedKey.bookName)\n            return false;\n        try {\n            await _api__WEBPACK_IMPORTED_MODULE_0__.TavernAPI.deleteLorebook(parsedKey.bookName);\n            // 从本地状态移除\n            _state__WEBPACK_IMPORTED_MODULE_1__.appState.allLorebooks = _state__WEBPACK_IMPORTED_MODULE_1__.appState.allLorebooks.filter(b => b.name !== parsedKey.bookName);\n            _state__WEBPACK_IMPORTED_MODULE_1__.appState.lorebooks.character = _state__WEBPACK_IMPORTED_MODULE_1__.appState.lorebooks.character.filter(b => b !== parsedKey.bookName);\n            if (_state__WEBPACK_IMPORTED_MODULE_1__.appState.chatLorebook === parsedKey.bookName) {\n                _state__WEBPACK_IMPORTED_MODULE_1__.appState.chatLorebook = null;\n            }\n            return true;\n        }\n        catch (error) {\n            console.error('[WorldInfoOptimizer] Error deleting book:', error);\n            return false;\n        }\n    },\n    [_constants__WEBPACK_IMPORTED_MODULE_5__.ItemTypePrefix.ENTRY]: async (parsedKey) => {\n        if (!parsedKey.bookName || !parsedKey.entryId)\n            return false;\n        try {\n            await _api__WEBPACK_IMPORTED_MODULE_0__.TavernAPI.deleteLorebookEntry(parsedKey.bookName, parseInt(parsedKey.entryId));\n            // 从本地状态移除\n            const entries = (0,_state__WEBPACK_IMPORTED_MODULE_1__.safeGetLorebookEntries)(parsedKey.bookName);\n            const index = entries.findIndex(e => e.uid === parsedKey.entryId);\n            if (index > -1) {\n                entries.splice(index, 1);\n            }\n            return true;\n        }\n        catch (error) {\n            console.error('[WorldInfoOptimizer] Error deleting entry:', error);\n            return false;\n        }\n    },\n    [_constants__WEBPACK_IMPORTED_MODULE_5__.ItemTypePrefix.REGEX]: async (_parsedKey) => {\n        // 正则表达式不支持批量删除操作（出于保护考虑）\n        return false;\n    },\n};\n// --- 全局变量 ---\nlet parentWin;\nlet $;\n/**\n * 设置全局依赖\n */\nconst setGlobalDependencies = (jquery, parentWindow) => {\n    $ = jquery;\n    parentWin = parentWindow;\n};\n// --- 辅助函数 ---\n/**\n * 获取项目键\n */\nconst getItemKey = ($item) => {\n    const isGlobalLoreTab = _state__WEBPACK_IMPORTED_MODULE_1__.appState.activeTab === 'global-lore';\n    const isBookHeader = $item.hasClass('wio-book-group');\n    if (isGlobalLoreTab && isBookHeader) {\n        const isEditingEntries = $item.hasClass('editing-entries');\n        if (isEditingEntries)\n            return null;\n        const bookName = $item.data('book-name');\n        return bookName ? (0,_keyManager__WEBPACK_IMPORTED_MODULE_4__.createBookItemKey)(bookName) : null;\n    }\n    const type = $item.data('type');\n    const id = $item.data('id');\n    const bookName = $item.data('book-name');\n    if (type === 'lore' && id && bookName) {\n        return (0,_keyManager__WEBPACK_IMPORTED_MODULE_4__.createEntryItemKey)(bookName, id);\n    }\n    else if (type === 'regex' && id) {\n        const scope = _state__WEBPACK_IMPORTED_MODULE_1__.appState.activeTab === 'global-regex' ? 'global' : 'character';\n        return (0,_keyManager__WEBPACK_IMPORTED_MODULE_4__.createRegexItemKey)(scope, id);\n    }\n    return null;\n};\n/**\n * 检查项目是否可选择\n */\nconst canSelectItem = ($item) => {\n    const isGlobalLoreTab = _state__WEBPACK_IMPORTED_MODULE_1__.appState.activeTab === 'global-lore';\n    const isBookHeader = $item.hasClass('wio-book-group');\n    if (isGlobalLoreTab && isBookHeader) {\n        return !$item.hasClass('editing-entries');\n    }\n    return true;\n};\n// --- 核心事件处理函数 ---\n/**\n * 处理头部点击事件\n */\nconst handleHeaderClick = (0,_utils__WEBPACK_IMPORTED_MODULE_3__.errorCatched)(async (event) => {\n    const $target = $(event.target);\n    const $container = $(event.currentTarget).closest('.wio-item-container, .wio-book-group');\n    // 如果点击的是按钮等可交互控件，则不执行后续逻辑\n    if ($target.closest('.wio-item-controls, .wio-rename-ui').length > 0) {\n        return;\n    }\n    // 多选模式下的选择逻辑\n    if (_state__WEBPACK_IMPORTED_MODULE_1__.appState.multiSelectMode) {\n        const itemKey = getItemKey($container);\n        if (itemKey && canSelectItem($container)) {\n            const isSelected = (0,_state__WEBPACK_IMPORTED_MODULE_1__.toggleSelectedItem)(itemKey);\n            (0,_ui__WEBPACK_IMPORTED_MODULE_2__.updateItemSelectionView)(itemKey, isSelected);\n            (0,_ui__WEBPACK_IMPORTED_MODULE_2__.updateSelectionCount)(_state__WEBPACK_IMPORTED_MODULE_1__.appState.selectedItems.size);\n        }\n        return;\n    }\n    // 普通模式下的展开/折叠逻辑\n    const $content = $container.find('.wio-collapsible-content').first();\n    if ($content.length > 0) {\n        const isCollapsed = $container.hasClass('collapsed');\n        if (isCollapsed) {\n            $content.slideDown(200);\n            $container.removeClass('collapsed');\n        }\n        else {\n            $content.slideUp(200);\n            $container.addClass('collapsed');\n        }\n    }\n}, 'handleHeaderClick');\n/**\n * 处理状态切换事件\n */\nconst handleToggleState = (0,_utils__WEBPACK_IMPORTED_MODULE_3__.errorCatched)(async (event) => {\n    event.stopPropagation();\n    const $button = $(event.currentTarget);\n    const $elementToSort = $button.closest('.wio-book-group, .wio-item-container');\n    if ($elementToSort.hasClass('renaming'))\n        return;\n    const type = $elementToSort.data('type') || 'book';\n    const id = $elementToSort.data('id');\n    const bookName = $elementToSort.data('book-name');\n    try {\n        if (type === 'book') {\n            // 切换世界书状态\n            const book = _state__WEBPACK_IMPORTED_MODULE_1__.appState.allLorebooks.find(b => b.name === bookName);\n            if (book) {\n                const newState = !book.enabled;\n                book.enabled = newState;\n                // 更新UI\n                const $icon = $button.find('i');\n                $icon.removeClass('fa-eye fa-eye-slash');\n                $icon.addClass(newState ? 'fa-eye' : 'fa-eye-slash');\n                $button.attr('title', newState ? '禁用' : '启用');\n                (0,_ui__WEBPACK_IMPORTED_MODULE_2__.showSuccessTick)(`世界书已${newState ? '启用' : '禁用'}`);\n            }\n        }\n        else if (type === 'lore') {\n            // 切换条目状态\n            const entries = (0,_state__WEBPACK_IMPORTED_MODULE_1__.safeGetLorebookEntries)(bookName);\n            const entry = entries.find(e => e.uid === id);\n            if (entry) {\n                const newState = !entry.enabled;\n                entry.enabled = newState;\n                // 更新到服务器\n                await _api__WEBPACK_IMPORTED_MODULE_0__.TavernAPI.setLorebookEntries(bookName, [{ uid: id, enabled: newState }]);\n                // 更新UI\n                const $icon = $button.find('i');\n                $icon.removeClass('fa-eye fa-eye-slash');\n                $icon.addClass(newState ? 'fa-eye' : 'fa-eye-slash');\n                $button.attr('title', newState ? '禁用' : '启用');\n                (0,_ui__WEBPACK_IMPORTED_MODULE_2__.showSuccessTick)(`条目已${newState ? '启用' : '禁用'}`);\n            }\n        }\n    }\n    catch (error) {\n        console.error('[WorldInfoOptimizer] Error toggling state:', error);\n        (0,_ui__WEBPACK_IMPORTED_MODULE_2__.showModal)({ type: 'alert', title: '错误', text: '状态切换失败，请重试。' });\n    }\n}, 'handleToggleState');\n/**\n * 绑定所有事件处理器\n */\nconst bindEventHandlers = () => {\n    const parentDoc = parentWin.document;\n    // 扩展菜单按钮点击事件\n    $(parentDoc).on('click', `#world-info-optimizer-button`, async () => {\n        const $panel = $(`#world-info-optimizer-panel`, parentDoc);\n        if ($panel.is(':visible')) {\n            (0,_ui__WEBPACK_IMPORTED_MODULE_2__.hidePanel)();\n        }\n        else {\n            await (0,_ui__WEBPACK_IMPORTED_MODULE_2__.showPanel)(async () => {\n                try {\n                    const data = await (0,_api__WEBPACK_IMPORTED_MODULE_0__.loadAllData)({ $: $, parentWin: parentWin });\n                    (0,_state__WEBPACK_IMPORTED_MODULE_1__.hydrateAppState)(data);\n                    (0,_ui__WEBPACK_IMPORTED_MODULE_2__.renderContent)();\n                }\n                catch (error) {\n                    console.error('[WorldInfoOptimizer] Failed to load data on panel show:', error);\n                }\n            });\n        }\n    });\n    // 面板关闭按钮\n    $(parentDoc).on('click', '#wio-close-btn', () => {\n        (0,_ui__WEBPACK_IMPORTED_MODULE_2__.hidePanel)();\n    });\n    // 搜索输入框事件\n    const debouncedSearch = (0,_utils__WEBPACK_IMPORTED_MODULE_3__.debounce)(() => {\n        (0,_ui__WEBPACK_IMPORTED_MODULE_2__.renderContent)();\n    }, 300);\n    $(parentDoc).on('input', `#wio-search-input`, debouncedSearch);\n    $(parentDoc).on('click', '#wio-clear-search-btn', () => {\n        $(`#wio-search-input`, parentDoc).val('').trigger('input');\n    });\n    // 搜索过滤器复选框\n    $(parentDoc).on('change', '#wio-filter-book-name', (e) => {\n        _state__WEBPACK_IMPORTED_MODULE_1__.appState.searchFilters.bookName = e.target.checked;\n        (0,_ui__WEBPACK_IMPORTED_MODULE_2__.renderContent)();\n    });\n    $(parentDoc).on('change', '#wio-filter-entry-name', (e) => {\n        _state__WEBPACK_IMPORTED_MODULE_1__.appState.searchFilters.entryName = e.target.checked;\n        (0,_ui__WEBPACK_IMPORTED_MODULE_2__.renderContent)();\n    });\n    $(parentDoc).on('change', '#wio-filter-keywords', (e) => {\n        _state__WEBPACK_IMPORTED_MODULE_1__.appState.searchFilters.keywords = e.target.checked;\n        (0,_ui__WEBPACK_IMPORTED_MODULE_2__.renderContent)();\n    });\n    $(parentDoc).on('change', '#wio-filter-content', (e) => {\n        _state__WEBPACK_IMPORTED_MODULE_1__.appState.searchFilters.content = e.target.checked;\n        (0,_ui__WEBPACK_IMPORTED_MODULE_2__.renderContent)();\n    });\n    // 刷新按钮\n    $(parentDoc).on('click', `#wio-refresh-btn`, () => {\n        (0,_api__WEBPACK_IMPORTED_MODULE_0__.loadAllData)({ $: $, parentWin: parentWin })\n            .then(data => {\n            (0,_state__WEBPACK_IMPORTED_MODULE_1__.hydrateAppState)(data);\n            (0,_ui__WEBPACK_IMPORTED_MODULE_2__.renderContent)();\n        })\n            .catch(error => {\n            console.error('[WorldInfoOptimizer] Failed to refresh data:', error);\n        });\n    });\n    // 标签页切换\n    $(parentDoc).on('click', `.wio-tab-btn`, (event) => {\n        const $this = $(event.currentTarget);\n        const tabId = $this.data('tab');\n        $(`.wio-tab-btn`, parentDoc).removeClass('active');\n        $this.addClass('active');\n        _state__WEBPACK_IMPORTED_MODULE_1__.appState.activeTab = tabId;\n        (0,_ui__WEBPACK_IMPORTED_MODULE_2__.renderContent)();\n    });\n    // 多选模式切换\n    $(parentDoc).on('click', '#wio-multi-select-toggle', () => {\n        const isEnabled = (0,_state__WEBPACK_IMPORTED_MODULE_1__.toggleMultiSelectMode)();\n        (0,_ui__WEBPACK_IMPORTED_MODULE_2__.updateMultiSelectModeView)(isEnabled);\n    });\n    // 批量操作按钮\n    $(parentDoc).on('click', '#wio-select-all-btn', handleSelectAll);\n    $(parentDoc).on('click', '#wio-deselect-all-btn', handleDeselectAll);\n    $(parentDoc).on('click', '#wio-batch-enable-btn', handleBatchEnable);\n    $(parentDoc).on('click', '#wio-batch-disable-btn', handleBatchDisable);\n    $(parentDoc).on('click', '#wio-batch-delete-btn', handleBatchDelete);\n    // 折叠按钮\n    $(parentDoc).on('click', `#wio-collapse-all-btn`, handleCollapseAll);\n    $(parentDoc).on('click', `#wio-collapse-current-btn`, handleCollapseCurrent);\n    // 动态事件绑定\n    $(parentDoc).on('click', '.wio-item-header', handleHeaderClick);\n    $(parentDoc).on('click', '.wio-toggle-state', handleToggleState);\n};\n// --- 批量操作处理函数 ---\n/**\n * 处理全选\n */\nconst handleSelectAll = (0,_utils__WEBPACK_IMPORTED_MODULE_3__.errorCatched)(async () => {\n    const parentDoc = parentWin.document;\n    const $visibleItems = $(`#world-info-optimizer-panel .wio-item-container:visible, #world-info-optimizer-panel .wio-book-group:visible`, parentDoc);\n    $visibleItems.each((_, element) => {\n        const $item = $(element);\n        const itemKey = getItemKey($item);\n        if (itemKey && canSelectItem($item)) {\n            _state__WEBPACK_IMPORTED_MODULE_1__.appState.selectedItems.add(itemKey);\n            $item.addClass('selected');\n        }\n    });\n    (0,_ui__WEBPACK_IMPORTED_MODULE_2__.updateSelectionCount)(_state__WEBPACK_IMPORTED_MODULE_1__.appState.selectedItems.size);\n    (0,_ui__WEBPACK_IMPORTED_MODULE_2__.showSuccessTick)('已全选可见项目');\n}, 'handleSelectAll');\n/**\n * 处理取消全选\n */\nconst handleDeselectAll = (0,_utils__WEBPACK_IMPORTED_MODULE_3__.errorCatched)(async () => {\n    const parentDoc = parentWin.document;\n    _state__WEBPACK_IMPORTED_MODULE_1__.appState.selectedItems.clear();\n    $(`#world-info-optimizer-panel .selected`, parentDoc).removeClass('selected');\n    (0,_ui__WEBPACK_IMPORTED_MODULE_2__.updateSelectionCount)(_state__WEBPACK_IMPORTED_MODULE_1__.appState.selectedItems.size);\n    (0,_ui__WEBPACK_IMPORTED_MODULE_2__.showSuccessTick)('已取消全选');\n}, 'handleDeselectAll');\n/**\n * 处理批量启用\n */\nconst handleBatchEnable = (0,_utils__WEBPACK_IMPORTED_MODULE_3__.errorCatched)(async () => {\n    const selectedItems = Array.from(_state__WEBPACK_IMPORTED_MODULE_1__.appState.selectedItems);\n    if (selectedItems.length === 0) {\n        await (0,_ui__WEBPACK_IMPORTED_MODULE_2__.showModal)({ type: 'alert', title: '提示', text: '请先选择要启用的项目。' });\n        return;\n    }\n    const progressToast = (0,_ui__WEBPACK_IMPORTED_MODULE_2__.showProgressToast)('正在批量启用...');\n    let successCount = 0;\n    try {\n        for (const itemKey of selectedItems) {\n            try {\n                const parsedKey = (0,_keyManager__WEBPACK_IMPORTED_MODULE_4__.parseItemKey)(itemKey);\n                const operation = batchEnableOperations[parsedKey.type];\n                if (operation) {\n                    const success = await operation(parsedKey);\n                    if (success)\n                        successCount++;\n                }\n            }\n            catch (error) {\n                console.error('[WorldInfoOptimizer] Error parsing item key in batch enable:', error, itemKey);\n            }\n        }\n        progressToast.remove();\n        (0,_ui__WEBPACK_IMPORTED_MODULE_2__.showSuccessTick)(`成功启用 ${successCount} 个项目`);\n        _state__WEBPACK_IMPORTED_MODULE_1__.appState.selectedItems.clear();\n        (0,_ui__WEBPACK_IMPORTED_MODULE_2__.renderContent)();\n    }\n    catch (error) {\n        progressToast.remove();\n        console.error('[WorldInfoOptimizer] Error in batch enable:', error);\n        (0,_ui__WEBPACK_IMPORTED_MODULE_2__.showModal)({ type: 'alert', title: '错误', text: '批量启用操作失败。' });\n    }\n}, 'handleBatchEnable');\n/**\n * 处理批量禁用\n */\nconst handleBatchDisable = (0,_utils__WEBPACK_IMPORTED_MODULE_3__.errorCatched)(async () => {\n    const selectedItems = Array.from(_state__WEBPACK_IMPORTED_MODULE_1__.appState.selectedItems);\n    if (selectedItems.length === 0) {\n        await (0,_ui__WEBPACK_IMPORTED_MODULE_2__.showModal)({ type: 'alert', title: '提示', text: '请先选择要禁用的项目。' });\n        return;\n    }\n    const progressToast = (0,_ui__WEBPACK_IMPORTED_MODULE_2__.showProgressToast)('正在批量禁用...');\n    let successCount = 0;\n    try {\n        for (const itemKey of selectedItems) {\n            try {\n                const parsedKey = (0,_keyManager__WEBPACK_IMPORTED_MODULE_4__.parseItemKey)(itemKey);\n                const operation = batchDisableOperations[parsedKey.type];\n                if (operation) {\n                    const success = await operation(parsedKey);\n                    if (success)\n                        successCount++;\n                }\n            }\n            catch (error) {\n                console.error('[WorldInfoOptimizer] Error parsing item key in batch disable:', error, itemKey);\n            }\n        }\n        progressToast.remove();\n        (0,_ui__WEBPACK_IMPORTED_MODULE_2__.showSuccessTick)(`成功禁用 ${successCount} 个项目`);\n        _state__WEBPACK_IMPORTED_MODULE_1__.appState.selectedItems.clear();\n        (0,_ui__WEBPACK_IMPORTED_MODULE_2__.renderContent)();\n    }\n    catch (error) {\n        progressToast.remove();\n        console.error('[WorldInfoOptimizer] Error in batch disable:', error);\n        (0,_ui__WEBPACK_IMPORTED_MODULE_2__.showModal)({ type: 'alert', title: '错误', text: '批量禁用操作失败。' });\n    }\n}, 'handleBatchDisable');\n/**\n * 处理批量删除\n */\nconst handleBatchDelete = (0,_utils__WEBPACK_IMPORTED_MODULE_3__.errorCatched)(async () => {\n    const selectedItems = Array.from(_state__WEBPACK_IMPORTED_MODULE_1__.appState.selectedItems);\n    if (selectedItems.length === 0) {\n        await (0,_ui__WEBPACK_IMPORTED_MODULE_2__.showModal)({ type: 'alert', title: '提示', text: '请先选择要删除的项目。' });\n        return;\n    }\n    // 显示确认对话框\n    try {\n        await (0,_ui__WEBPACK_IMPORTED_MODULE_2__.showModal)({\n            type: 'confirm',\n            title: '确认批量删除',\n            text: `确定要删除 ${selectedItems.length} 个选中的项目吗？此操作无法撤销。`,\n        });\n        const progressToast = (0,_ui__WEBPACK_IMPORTED_MODULE_2__.showProgressToast)('正在批量删除...');\n        let successCount = 0;\n        // 按类型对项目进行分组\n        const booksToDelete = [];\n        const entriesToDelete = new Map();\n        for (const itemKey of selectedItems) {\n            try {\n                const parsedKey = (0,_keyManager__WEBPACK_IMPORTED_MODULE_4__.parseItemKey)(itemKey);\n                if (parsedKey.type === _constants__WEBPACK_IMPORTED_MODULE_5__.ItemTypePrefix.BOOK && parsedKey.bookName) {\n                    booksToDelete.push(parsedKey.bookName);\n                }\n                else if (parsedKey.type === _constants__WEBPACK_IMPORTED_MODULE_5__.ItemTypePrefix.ENTRY && parsedKey.bookName && parsedKey.entryId) {\n                    if (!entriesToDelete.has(parsedKey.bookName)) {\n                        entriesToDelete.set(parsedKey.bookName, []);\n                    }\n                    entriesToDelete.get(parsedKey.bookName).push(parseInt(parsedKey.entryId));\n                }\n            }\n            catch (error) {\n                console.error('[WorldInfoOptimizer] Error parsing item key in batch delete:', error, itemKey);\n            }\n        }\n        // 批量删除书籍\n        for (const bookName of booksToDelete) {\n            try {\n                const operation = batchDeleteOperations[_constants__WEBPACK_IMPORTED_MODULE_5__.ItemTypePrefix.BOOK];\n                if (await operation({ type: _constants__WEBPACK_IMPORTED_MODULE_5__.ItemTypePrefix.BOOK, bookName })) {\n                    successCount++;\n                }\n            }\n            catch (error) {\n                console.error(`[WorldInfoOptimizer] Failed to delete book ${bookName}:`, error);\n            }\n        }\n        // 批量删除条目\n        for (const [bookName, uids] of entriesToDelete.entries()) {\n            try {\n                await _api__WEBPACK_IMPORTED_MODULE_0__.TavernAPI.deleteLorebookEntries(bookName, uids);\n                // 更新本地状态\n                const entries = (0,_state__WEBPACK_IMPORTED_MODULE_1__.safeGetLorebookEntries)(bookName);\n                const uidSet = new Set(uids);\n                const remainingEntries = entries.filter(e => !uidSet.has(Number(e.uid)));\n                _state__WEBPACK_IMPORTED_MODULE_1__.appState.lorebookEntries.set(bookName, remainingEntries);\n                successCount += uids.length;\n            }\n            catch (error) {\n                console.error(`[WorldInfoOptimizer] Failed to delete entries from ${bookName}:`, error);\n            }\n        }\n        progressToast.remove();\n        (0,_ui__WEBPACK_IMPORTED_MODULE_2__.showSuccessTick)(`成功删除 ${successCount} 个项目`);\n        _state__WEBPACK_IMPORTED_MODULE_1__.appState.selectedItems.clear();\n        (0,_ui__WEBPACK_IMPORTED_MODULE_2__.renderContent)();\n    }\n    catch {\n        // 用户取消操作\n    }\n}, 'handleBatchDelete');\n/**\n * 处理全部折叠\n */\nconst handleCollapseAll = (0,_utils__WEBPACK_IMPORTED_MODULE_3__.errorCatched)(async () => {\n    const parentDoc = parentWin.document;\n    const $allCollapsible = $(`#world-info-optimizer-panel .wio-collapsible-content`, parentDoc);\n    $allCollapsible.slideUp(200);\n    $allCollapsible.closest('.wio-item-container, .wio-book-group').addClass('collapsed');\n    (0,_ui__WEBPACK_IMPORTED_MODULE_2__.showSuccessTick)('已折叠所有项目');\n}, 'handleCollapseAll');\n/**\n * 处理当前标签页折叠\n */\nconst handleCollapseCurrent = (0,_utils__WEBPACK_IMPORTED_MODULE_3__.errorCatched)(async () => {\n    const parentDoc = parentWin.document;\n    const activeTab = _state__WEBPACK_IMPORTED_MODULE_1__.appState.activeTab;\n    const $currentTabContent = $(`#world-info-optimizer-panel .wio-tab-content[data-tab=\"${activeTab}\"] .wio-collapsible-content`, parentDoc);\n    $currentTabContent.slideUp(200);\n    $currentTabContent.closest('.wio-item-container, .wio-book-group').addClass('collapsed');\n    (0,_ui__WEBPACK_IMPORTED_MODULE_2__.showSuccessTick)('已折叠当前标签页项目');\n}, 'handleCollapseCurrent');\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/WorldInfoOptimizer/events.ts\n\n}");
  },
  "./src/WorldInfoOptimizer/index.ts": 
  /*!*****************************************!*\
  !*** ./src/WorldInfoOptimizer/index.ts ***!
  \*****************************************/ (__unused_webpack_module, __webpack_exports__, __webpack_require__) => {
    eval("{__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _api__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./api */ \"./src/WorldInfoOptimizer/api.ts\");\n/* harmony import */ var _events__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./events */ \"./src/WorldInfoOptimizer/events.ts\");\n/* harmony import */ var _state__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./state */ \"./src/WorldInfoOptimizer/state.ts\");\n/* harmony import */ var _ui__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ui */ \"./src/WorldInfoOptimizer/ui.ts\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./utils */ \"./src/WorldInfoOptimizer/utils.ts\");\n// ==UserScript==\n// @name         世界书优化器 (World Info Optimizer)\n// @namespace    SillyTavern.WorldInfoOptimizer.v1_0_0\n// @match        */*\n// @version      1.0.0\n// @description  【世界书管理专家】基于 Linus 哲学的简洁高效世界书管理工具。提供搜索、替换、批量操作等核心功能。\n// <AUTHOR> & AI Assistant\n// @grant        none\n// @inject-into  content\n// ==/UserScript==\n\n\n\n\n\n\n// 使用IIFE封装，避免全局污染\n(() => {\n    console.log('[WorldInfoOptimizer] Script execution started.');\n    /**\n     * 等待DOM和API就绪\n     * @param {Function} callback - 准备就绪后执行的回调函数\n     */\n    function onReady(callback) {\n        const domSelector = '#extensions_settings';\n        const timeout = 30000; // 30秒超时\n        console.log(`[WorldInfoOptimizer] Starting readiness check. Watching for DOM element \"${domSelector}\" with a ${timeout / 1000}s timeout.`);\n        let observer = null;\n        let timeoutId = null;\n        const cleanup = () => {\n            if (observer) {\n                observer.disconnect();\n                observer = null;\n                console.log('[WorldInfoOptimizer] MutationObserver disconnected.');\n            }\n            if (timeoutId) {\n                clearTimeout(timeoutId);\n                timeoutId = null;\n            }\n        };\n        const checkForReadyState = () => {\n            const parentWin = window.parent;\n            const parentDoc = parentWin.document;\n            const domReady = parentDoc.querySelector(domSelector) !== null;\n            const apiReady = parentWin.TavernHelper && typeof parentWin.TavernHelper.getCharData === 'function' && parentWin.jQuery;\n            if (domReady && apiReady) {\n                cleanup();\n                console.log(`[WorldInfoOptimizer] SUCCESS: Both DOM and Core APIs are ready. Initializing script.`);\n                try {\n                    callback(parentWin.jQuery, parentWin.TavernHelper);\n                }\n                catch (e) {\n                    console.error('[WorldInfoOptimizer] FATAL: Error during main callback execution.', e);\n                }\n                return true;\n            }\n            return false;\n        };\n        // 初始检查，可能已经就绪\n        if (checkForReadyState()) {\n            return;\n        }\n        observer = new MutationObserver(mutations => {\n            // 优化：只在有节点添加时才执行检查\n            const hasAddedNodes = mutations.some(m => m.addedNodes.length > 0);\n            if (hasAddedNodes && checkForReadyState()) {\n                // 成功后，checkForReadyState内部会调用cleanup\n            }\n        });\n        observer.observe(window.parent.document.body, {\n            childList: true,\n            subtree: true,\n        });\n        timeoutId = window.setTimeout(() => {\n            const parentWin = window.parent;\n            const parentDoc = parentWin.document;\n            const domReady = parentDoc.querySelector(domSelector) !== null;\n            const apiReady = parentWin.TavernHelper && typeof parentWin.TavernHelper.getCharData === 'function' && parentWin.jQuery;\n            cleanup(); // 停止所有监听\n            console.error(`[WorldInfoOptimizer] FATAL: Readiness check timed out after ${timeout / 1000} seconds.`);\n            if (!domReady)\n                console.error(`[WorldInfoOptimizer] -> Failure: DOM element \"${domSelector}\" not found.`);\n            if (!apiReady)\n                console.error(`[WorldInfoOptimizer] -> Failure: Core APIs not available.`);\n        }, timeout);\n    }\n    /**\n     * 主初始化函数\n     * @param {any} jquery - jQuery对象\n     * @param {any} tavernHelper - TavernHelper对象\n     */\n    const main = (0,_utils__WEBPACK_IMPORTED_MODULE_4__.errorCatched)((jquery, tavernHelper) => {\n        // 1. 设置全局依赖\n        const dependencies = {\n            $: jquery,\n            TavernHelper: tavernHelper,\n            parentWin: window.parent,\n        };\n        (0,_api__WEBPACK_IMPORTED_MODULE_0__.initializeTavernAPI)(dependencies); // 初始化API模块\n        // 设置UI和事件模块的全局依赖\n        (0,_ui__WEBPACK_IMPORTED_MODULE_3__.setGlobalDependencies)(jquery, window.parent);\n        (0,_events__WEBPACK_IMPORTED_MODULE_1__.setGlobalDependencies)(jquery, window.parent);\n        // 2. 注入UI和样式\n        (0,_ui__WEBPACK_IMPORTED_MODULE_3__.addBasicStyles)();\n        (0,_ui__WEBPACK_IMPORTED_MODULE_3__.createMainPanel)();\n        (0,_ui__WEBPACK_IMPORTED_MODULE_3__.createExtensionButton)();\n        // 3. 绑定核心事件处理器\n        (0,_events__WEBPACK_IMPORTED_MODULE_1__.bindEventHandlers)();\n        // 4. 初始数据加载\n        // loadAllData 会触发 hydrateAppState 和 renderContent\n        (0,_api__WEBPACK_IMPORTED_MODULE_0__.loadAllData)(dependencies)\n            .then(data => {\n            (0,_state__WEBPACK_IMPORTED_MODULE_2__.hydrateAppState)(data); // 使用返回的数据更新状态\n            (0,_ui__WEBPACK_IMPORTED_MODULE_3__.renderContent)(); // 初始渲染\n            console.log('[WorldInfoOptimizer] Initial data load, state hydration, and render complete.');\n        })\n            .catch(error => {\n            console.error('[WorldInfoOptimizer] Failed to complete initial data orchestration:', error);\n        });\n        console.log('[WorldInfoOptimizer] Initialization sequence complete.');\n    }, 'WorldInfoOptimizer-Main');\n    // --- 脚本启动 ---\n    onReady(main);\n})();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/WorldInfoOptimizer/index.ts\n\n}");
  },
  "./src/WorldInfoOptimizer/keyManager.ts": 
  /*!**********************************************!*\
  !*** ./src/WorldInfoOptimizer/keyManager.ts ***!
  \**********************************************/ (__unused_webpack_module, __webpack_exports__, __webpack_require__) => {
    eval("{__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createBookItemKey: () => (/* binding */ createBookItemKey),\n/* harmony export */   createEntryItemKey: () => (/* binding */ createEntryItemKey),\n/* harmony export */   createItemKey: () => (/* binding */ createItemKey),\n/* harmony export */   createRegexItemKey: () => (/* binding */ createRegexItemKey),\n/* harmony export */   parseItemKey: () => (/* binding */ parseItemKey)\n/* harmony export */ });\n/**\n * 世界书优化器键管理模块\n * 使用 Base64 编码管理复合 itemKey，确保特殊字符处理的安全性\n */\n/**\n * 创建项目键\n * 将复合ID对象序列化为JSON并进行Base64编码\n */\nconst createItemKey = (compositeId) => {\n    try {\n        const jsonString = JSON.stringify(compositeId);\n        // 处理 Unicode 字符，确保 Base64 编码的兼容性\n        const encoded = btoa(unescape(encodeURIComponent(jsonString)));\n        return encoded;\n    }\n    catch (error) {\n        console.error('[WorldInfoOptimizer] Error creating item key:', error);\n        throw new Error(`Failed to create item key: ${error}`);\n    }\n};\n/**\n * 解析项目键\n * 将Base64编码的字符串解码并反序列化为复合ID对象\n */\nconst parseItemKey = (encodedKey) => {\n    try {\n        // 处理 Unicode 字符的 Base64 解码\n        const decoded = decodeURIComponent(escape(atob(encodedKey)));\n        const compositeId = JSON.parse(decoded);\n        return compositeId;\n    }\n    catch (error) {\n        console.error('[WorldInfoOptimizer] Error parsing item key:', error, encodedKey);\n        throw new Error(`Failed to parse item key: ${error}`);\n    }\n};\n/**\n * 便捷方法：创建世界书项目键\n */\nconst createBookItemKey = (bookName) => {\n    return createItemKey({\n        type: 'book',\n        bookName,\n    });\n};\n/**\n * 便捷方法：创建条目项目键\n */\nconst createEntryItemKey = (bookName, entryId) => {\n    return createItemKey({\n        type: 'entry',\n        bookName,\n        entryId,\n    });\n};\n/**\n * 便捷方法：创建正则表达式项目键\n */\nconst createRegexItemKey = (scope, regexId) => {\n    return createItemKey({\n        type: 'regex',\n        scope,\n        regexId,\n    });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvV29ybGRJbmZvT3B0aW1pemVyL2tleU1hbmFnZXIudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBQTs7O0dBR0c7QUFJSDs7O0dBR0c7QUFDSSxNQUFNLGFBQWEsR0FBRyxDQUFDLFdBQXlCLEVBQVUsRUFBRTtJQUNqRSxJQUFJLENBQUM7UUFDSCxNQUFNLFVBQVUsR0FBRyxJQUFJLENBQUMsU0FBUyxDQUFDLFdBQVcsQ0FBQyxDQUFDO1FBQy9DLGlDQUFpQztRQUNqQyxNQUFNLE9BQU8sR0FBRyxJQUFJLENBQUMsUUFBUSxDQUFDLGtCQUFrQixDQUFDLFVBQVUsQ0FBQyxDQUFDLENBQUMsQ0FBQztRQUMvRCxPQUFPLE9BQU8sQ0FBQztJQUNqQixDQUFDO0lBQUMsT0FBTyxLQUFLLEVBQUUsQ0FBQztRQUNmLE9BQU8sQ0FBQyxLQUFLLENBQUMsK0NBQStDLEVBQUUsS0FBSyxDQUFDLENBQUM7UUFDdEUsTUFBTSxJQUFJLEtBQUssQ0FBQyw4QkFBOEIsS0FBSyxFQUFFLENBQUMsQ0FBQztJQUN6RCxDQUFDO0FBQ0gsQ0FBQyxDQUFDO0FBRUY7OztHQUdHO0FBQ0ksTUFBTSxZQUFZLEdBQUcsQ0FBQyxVQUFrQixFQUFnQixFQUFFO0lBQy9ELElBQUksQ0FBQztRQUNILDJCQUEyQjtRQUMzQixNQUFNLE9BQU8sR0FBRyxrQkFBa0IsQ0FBQyxNQUFNLENBQUMsSUFBSSxDQUFDLFVBQVUsQ0FBQyxDQUFDLENBQUMsQ0FBQztRQUM3RCxNQUFNLFdBQVcsR0FBaUIsSUFBSSxDQUFDLEtBQUssQ0FBQyxPQUFPLENBQUMsQ0FBQztRQUN0RCxPQUFPLFdBQVcsQ0FBQztJQUNyQixDQUFDO0lBQUMsT0FBTyxLQUFLLEVBQUUsQ0FBQztRQUNmLE9BQU8sQ0FBQyxLQUFLLENBQUMsOENBQThDLEVBQUUsS0FBSyxFQUFFLFVBQVUsQ0FBQyxDQUFDO1FBQ2pGLE1BQU0sSUFBSSxLQUFLLENBQUMsNkJBQTZCLEtBQUssRUFBRSxDQUFDLENBQUM7SUFDeEQsQ0FBQztBQUNILENBQUMsQ0FBQztBQUVGOztHQUVHO0FBQ0ksTUFBTSxpQkFBaUIsR0FBRyxDQUFDLFFBQWdCLEVBQVUsRUFBRTtJQUM1RCxPQUFPLGFBQWEsQ0FBQztRQUNuQixJQUFJLEVBQUUsTUFBTTtRQUNaLFFBQVE7S0FDVCxDQUFDLENBQUM7QUFDTCxDQUFDLENBQUM7QUFFRjs7R0FFRztBQUNJLE1BQU0sa0JBQWtCLEdBQUcsQ0FBQyxRQUFnQixFQUFFLE9BQWUsRUFBVSxFQUFFO0lBQzlFLE9BQU8sYUFBYSxDQUFDO1FBQ25CLElBQUksRUFBRSxPQUFPO1FBQ2IsUUFBUTtRQUNSLE9BQU87S0FDUixDQUFDLENBQUM7QUFDTCxDQUFDLENBQUM7QUFFRjs7R0FFRztBQUNJLE1BQU0sa0JBQWtCLEdBQUcsQ0FBQyxLQUFhLEVBQUUsT0FBZSxFQUFVLEVBQUU7SUFDM0UsT0FBTyxhQUFhLENBQUM7UUFDbkIsSUFBSSxFQUFFLE9BQU87UUFDYixLQUFLO1FBQ0wsT0FBTztLQUNSLENBQUMsQ0FBQztBQUNMLENBQUMsQ0FBQyIsInNvdXJjZXMiOlsid2VicGFjazovL3RhdmVybl9oZWxwZXJfdGVtcGxhdGUvLi9zcmMvV29ybGRJbmZvT3B0aW1pemVyL2tleU1hbmFnZXIudHM/Il0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICog5LiW55WM5Lmm5LyY5YyW5Zmo6ZSu566h55CG5qih5Z2XXG4gKiDkvb/nlKggQmFzZTY0IOe8lueggeeuoeeQhuWkjeWQiCBpdGVtS2V577yM56Gu5L+d54m55q6K5a2X56ym5aSE55CG55qE5a6J5YWo5oCnXG4gKi9cblxuaW1wb3J0IHR5cGUgeyBJQm9va0VudHJ5SWQgfSBmcm9tICcuL3R5cGVzJztcblxuLyoqXG4gKiDliJvlu7rpobnnm67plK5cbiAqIOWwhuWkjeWQiElE5a+56LGh5bqP5YiX5YyW5Li6SlNPTuW5tui/m+ihjEJhc2U2NOe8lueggVxuICovXG5leHBvcnQgY29uc3QgY3JlYXRlSXRlbUtleSA9IChjb21wb3NpdGVJZDogSUJvb2tFbnRyeUlkKTogc3RyaW5nID0+IHtcbiAgdHJ5IHtcbiAgICBjb25zdCBqc29uU3RyaW5nID0gSlNPTi5zdHJpbmdpZnkoY29tcG9zaXRlSWQpO1xuICAgIC8vIOWkhOeQhiBVbmljb2RlIOWtl+espu+8jOehruS/nSBCYXNlNjQg57yW56CB55qE5YW85a655oCnXG4gICAgY29uc3QgZW5jb2RlZCA9IGJ0b2EodW5lc2NhcGUoZW5jb2RlVVJJQ29tcG9uZW50KGpzb25TdHJpbmcpKSk7XG4gICAgcmV0dXJuIGVuY29kZWQ7XG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcignW1dvcmxkSW5mb09wdGltaXplcl0gRXJyb3IgY3JlYXRpbmcgaXRlbSBrZXk6JywgZXJyb3IpO1xuICAgIHRocm93IG5ldyBFcnJvcihgRmFpbGVkIHRvIGNyZWF0ZSBpdGVtIGtleTogJHtlcnJvcn1gKTtcbiAgfVxufTtcblxuLyoqXG4gKiDop6PmnpDpobnnm67plK5cbiAqIOWwhkJhc2U2NOe8lueggeeahOWtl+espuS4suino+eggeW5tuWPjeW6j+WIl+WMluS4uuWkjeWQiElE5a+56LGhXG4gKi9cbmV4cG9ydCBjb25zdCBwYXJzZUl0ZW1LZXkgPSAoZW5jb2RlZEtleTogc3RyaW5nKTogSUJvb2tFbnRyeUlkID0+IHtcbiAgdHJ5IHtcbiAgICAvLyDlpITnkIYgVW5pY29kZSDlrZfnrKbnmoQgQmFzZTY0IOino+eggVxuICAgIGNvbnN0IGRlY29kZWQgPSBkZWNvZGVVUklDb21wb25lbnQoZXNjYXBlKGF0b2IoZW5jb2RlZEtleSkpKTtcbiAgICBjb25zdCBjb21wb3NpdGVJZDogSUJvb2tFbnRyeUlkID0gSlNPTi5wYXJzZShkZWNvZGVkKTtcbiAgICByZXR1cm4gY29tcG9zaXRlSWQ7XG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcignW1dvcmxkSW5mb09wdGltaXplcl0gRXJyb3IgcGFyc2luZyBpdGVtIGtleTonLCBlcnJvciwgZW5jb2RlZEtleSk7XG4gICAgdGhyb3cgbmV3IEVycm9yKGBGYWlsZWQgdG8gcGFyc2UgaXRlbSBrZXk6ICR7ZXJyb3J9YCk7XG4gIH1cbn07XG5cbi8qKlxuICog5L6/5o235pa55rOV77ya5Yib5bu65LiW55WM5Lmm6aG555uu6ZSuXG4gKi9cbmV4cG9ydCBjb25zdCBjcmVhdGVCb29rSXRlbUtleSA9IChib29rTmFtZTogc3RyaW5nKTogc3RyaW5nID0+IHtcbiAgcmV0dXJuIGNyZWF0ZUl0ZW1LZXkoe1xuICAgIHR5cGU6ICdib29rJyxcbiAgICBib29rTmFtZSxcbiAgfSk7XG59O1xuXG4vKipcbiAqIOS+v+aNt+aWueazle+8muWIm+W7uuadoeebrumhueebrumUrlxuICovXG5leHBvcnQgY29uc3QgY3JlYXRlRW50cnlJdGVtS2V5ID0gKGJvb2tOYW1lOiBzdHJpbmcsIGVudHJ5SWQ6IHN0cmluZyk6IHN0cmluZyA9PiB7XG4gIHJldHVybiBjcmVhdGVJdGVtS2V5KHtcbiAgICB0eXBlOiAnZW50cnknLFxuICAgIGJvb2tOYW1lLFxuICAgIGVudHJ5SWQsXG4gIH0pO1xufTtcblxuLyoqXG4gKiDkvr/mjbfmlrnms5XvvJrliJvlu7rmraPliJnooajovr7lvI/pobnnm67plK5cbiAqL1xuZXhwb3J0IGNvbnN0IGNyZWF0ZVJlZ2V4SXRlbUtleSA9IChzY29wZTogc3RyaW5nLCByZWdleElkOiBzdHJpbmcpOiBzdHJpbmcgPT4ge1xuICByZXR1cm4gY3JlYXRlSXRlbUtleSh7XG4gICAgdHlwZTogJ3JlZ2V4JyxcbiAgICBzY29wZSxcbiAgICByZWdleElkLFxuICB9KTtcbn07Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./src/WorldInfoOptimizer/keyManager.ts\n\n}");
  },
  "./src/WorldInfoOptimizer/state.ts": 
  /*!*****************************************!*\
  !*** ./src/WorldInfoOptimizer/state.ts ***!
  \*****************************************/ (__unused_webpack_module, __webpack_exports__, __webpack_require__) => {
    eval("{__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addLorebookToState: () => (/* binding */ addLorebookToState),\n/* harmony export */   addSelectedItem: () => (/* binding */ addSelectedItem),\n/* harmony export */   appState: () => (/* binding */ appState),\n/* harmony export */   clearSelectedItems: () => (/* binding */ clearSelectedItems),\n/* harmony export */   hydrateAppState: () => (/* binding */ hydrateAppState),\n/* harmony export */   isItemSelected: () => (/* binding */ isItemSelected),\n/* harmony export */   removeLorebookFromState: () => (/* binding */ removeLorebookFromState),\n/* harmony export */   removeSelectedItem: () => (/* binding */ removeSelectedItem),\n/* harmony export */   renameLorebookInState: () => (/* binding */ renameLorebookInState),\n/* harmony export */   resetSearchFilters: () => (/* binding */ resetSearchFilters),\n/* harmony export */   safeClearLorebookEntries: () => (/* binding */ safeClearLorebookEntries),\n/* harmony export */   safeDeleteLorebookEntries: () => (/* binding */ safeDeleteLorebookEntries),\n/* harmony export */   safeGetLorebookEntries: () => (/* binding */ safeGetLorebookEntries),\n/* harmony export */   safeHasLorebookEntries: () => (/* binding */ safeHasLorebookEntries),\n/* harmony export */   safeSetLorebookEntries: () => (/* binding */ safeSetLorebookEntries),\n/* harmony export */   setActiveTab: () => (/* binding */ setActiveTab),\n/* harmony export */   setDataLoaded: () => (/* binding */ setDataLoaded),\n/* harmony export */   toggleMultiSelectMode: () => (/* binding */ toggleMultiSelectMode),\n/* harmony export */   toggleSelectedItem: () => (/* binding */ toggleSelectedItem),\n/* harmony export */   updateCharacterRegexes: () => (/* binding */ updateCharacterRegexes),\n/* harmony export */   updateLorebookState: () => (/* binding */ updateLorebookState),\n/* harmony export */   updateSearchFilter: () => (/* binding */ updateSearchFilter)\n/* harmony export */ });\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./constants */ \"./src/WorldInfoOptimizer/constants.ts\");\n/**\n * 世界书优化器状态管理\n * 包含 appState 定义和所有状态操作函数（safe* 系列函数）\n */\n\n// --- 应用程序状态 ---\nconst appState = {\n    globalRegex: [],\n    characterRegex: [],\n    lorebooks: { character: [] },\n    chatLorebook: null,\n    allLorebooks: [],\n    lorebookEntries: new Map(),\n    lorebookUsage: new Map(),\n    activeTab: _constants__WEBPACK_IMPORTED_MODULE_0__.DEFAULT_TAB,\n    isDataLoaded: false,\n    searchFilters: { ..._constants__WEBPACK_IMPORTED_MODULE_0__.DEFAULT_SEARCH_FILTERS },\n    multiSelectMode: false,\n    selectedItems: new Set(),\n};\n// --- 安全访问 lorebookEntries 的函数 ---\n/**\n * 安全获取世界书条目\n */\nconst safeGetLorebookEntries = (bookName) => {\n    try {\n        if (!appState.lorebookEntries || !(appState.lorebookEntries instanceof Map)) {\n            console.warn('[WorldInfoOptimizer] appState.lorebookEntries is not a Map, reinitializing...');\n            appState.lorebookEntries = new Map();\n        }\n        const entries = appState.lorebookEntries.get(bookName);\n        if (!entries) {\n            console.log(`[WorldInfoOptimizer] No entries found for book: ${bookName}`);\n            return [];\n        }\n        if (!Array.isArray(entries)) {\n            console.warn(`[WorldInfoOptimizer] Entries for book ${bookName} is not an array, returning empty array`);\n            return [];\n        }\n        return entries;\n    }\n    catch (error) {\n        console.error('[WorldInfoOptimizer] Error in safeGetLorebookEntries:', error);\n        return [];\n    }\n};\n/**\n * 安全设置世界书条目\n */\nconst safeSetLorebookEntries = (bookName, entries) => {\n    try {\n        if (!appState.lorebookEntries || !(appState.lorebookEntries instanceof Map)) {\n            console.warn('[WorldInfoOptimizer] appState.lorebookEntries is not a Map, reinitializing...');\n            appState.lorebookEntries = new Map();\n        }\n        if (!Array.isArray(entries)) {\n            console.warn(`[WorldInfoOptimizer] Entries for book ${bookName} is not an array, converting...`);\n            appState.lorebookEntries.set(bookName, []);\n            return;\n        }\n        appState.lorebookEntries.set(bookName, entries);\n        console.log(`[WorldInfoOptimizer] Set ${entries.length} entries for book: ${bookName}`);\n    }\n    catch (error) {\n        console.error('[WorldInfoOptimizer] Error in safeSetLorebookEntries:', error);\n    }\n};\n/**\n * 安全删除世界书条目\n */\nconst safeDeleteLorebookEntries = (bookName) => {\n    try {\n        if (!appState.lorebookEntries || !(appState.lorebookEntries instanceof Map)) {\n            console.warn('[WorldInfoOptimizer] appState.lorebookEntries is not a Map, reinitializing...');\n            appState.lorebookEntries = new Map();\n            return;\n        }\n        const deleted = appState.lorebookEntries.delete(bookName);\n        if (deleted) {\n            console.log(`[WorldInfoOptimizer] Deleted entries for book: ${bookName}`);\n        }\n        else {\n            console.log(`[WorldInfoOptimizer] No entries found to delete for book: ${bookName}`);\n        }\n    }\n    catch (error) {\n        console.error('[WorldInfoOptimizer] Error in safeDeleteLorebookEntries:', error);\n    }\n};\n/**\n * 安全清空所有世界书条目\n */\nconst safeClearLorebookEntries = () => {\n    try {\n        if (!appState.lorebookEntries || !(appState.lorebookEntries instanceof Map)) {\n            console.warn('[WorldInfoOptimizer] appState.lorebookEntries is not a Map, reinitializing...');\n            appState.lorebookEntries = new Map();\n            return;\n        }\n        const size = appState.lorebookEntries.size;\n        appState.lorebookEntries.clear();\n        console.log(`[WorldInfoOptimizer] Cleared ${size} lorebook entries from state`);\n    }\n    catch (error) {\n        console.error('[WorldInfoOptimizer] Error in safeClearLorebookEntries:', error);\n        // 强制重新初始化\n        appState.lorebookEntries = new Map();\n    }\n};\n/**\n * 安全检查世界书是否有条目\n */\nconst safeHasLorebookEntries = (bookName) => {\n    try {\n        if (!appState.lorebookEntries || !(appState.lorebookEntries instanceof Map)) {\n            console.warn('[WorldInfoOptimizer] appState.lorebookEntries is not a Map, reinitializing...');\n            appState.lorebookEntries = new Map();\n            return false;\n        }\n        return appState.lorebookEntries.has(bookName);\n    }\n    catch (error) {\n        console.error('[WorldInfoOptimizer] Error in safeHasLorebookEntries:', error);\n        return false;\n    }\n};\n// --- 状态更新函数 ---\n/**\n * 设置数据加载状态\n */\nconst setDataLoaded = (loaded) => {\n    appState.isDataLoaded = loaded;\n};\n/**\n * 设置活动标签页\n */\nconst setActiveTab = (tab) => {\n    appState.activeTab = tab;\n};\n/**\n * 更新角色正则表达式\n */\nconst updateCharacterRegexes = (allUIRegexes, charData) => {\n    const characterUIRegexes = Array.isArray(allUIRegexes)\n        ? allUIRegexes.filter((r) => r.scope === 'character')\n        : [];\n    // 从角色卡片获取正则表达式\n    const cardRegexes = charData?.data?.extensions?.regex_scripts || [];\n    // 创建一个Set来跟踪UI中已有的正则表达式\n    const uiRegexIdentifiers = new Set(characterUIRegexes.map((r) => `${r.script_name}::${r.find_regex}::${r.replace_string}`));\n    // 过滤出卡片中独有的正则表达式\n    const uniqueCardRegexes = cardRegexes.filter((r) => {\n        const identifier = `${r.script_name}::${r.find_regex}::${r.replace_string}`;\n        return !uiRegexIdentifiers.has(identifier);\n    });\n    appState.characterRegex = [...characterUIRegexes, ...uniqueCardRegexes];\n};\n// --- 多选模式管理 ---\n/**\n * 切换多选模式\n */\nconst toggleMultiSelectMode = () => {\n    appState.multiSelectMode = !appState.multiSelectMode;\n    if (!appState.multiSelectMode) {\n        clearSelectedItems();\n    }\n    return appState.multiSelectMode;\n};\n/**\n * 切换选中项目\n */\nconst toggleSelectedItem = (itemKey) => {\n    if (appState.selectedItems.has(itemKey)) {\n        appState.selectedItems.delete(itemKey);\n        return false;\n    }\n    else {\n        appState.selectedItems.add(itemKey);\n        return true;\n    }\n};\n/**\n * 清空选中项目\n */\nconst clearSelectedItems = () => {\n    appState.selectedItems.clear();\n};\n/**\n * 添加选中项目\n */\nconst addSelectedItem = (itemKey) => {\n    appState.selectedItems.add(itemKey);\n};\n/**\n * 移除选中项目\n */\nconst removeSelectedItem = (itemKey) => {\n    appState.selectedItems.delete(itemKey);\n};\n/**\n * 检查项目是否被选中\n */\nconst isItemSelected = (itemKey) => {\n    return appState.selectedItems.has(itemKey);\n};\n// --- 搜索过滤器管理 ---\n/**\n * 更新搜索过滤器\n */\nconst updateSearchFilter = (filterName, enabled) => {\n    appState.searchFilters[filterName] = enabled;\n};\n/**\n * 重置搜索过滤器\n */\nconst resetSearchFilters = () => {\n    appState.searchFilters = { ..._constants__WEBPACK_IMPORTED_MODULE_0__.DEFAULT_SEARCH_FILTERS };\n};\n// --- 世界书管理 ---\n/**\n * 添加世界书到状态\n */\nconst addLorebookToState = (bookName, enabled = true) => {\n    const existingBook = appState.allLorebooks.find(book => book.name === bookName);\n    if (!existingBook) {\n        appState.allLorebooks.push({ name: bookName, enabled });\n    }\n};\n/**\n * 从状态中移除世界书\n */\nconst removeLorebookFromState = (bookName) => {\n    const index = appState.allLorebooks.findIndex(book => book.name === bookName);\n    if (index !== -1) {\n        appState.allLorebooks.splice(index, 1);\n    }\n    safeDeleteLorebookEntries(bookName);\n};\n/**\n * 更新世界书状态\n */\nconst updateLorebookState = (bookName, enabled) => {\n    const book = appState.allLorebooks.find(book => book.name === bookName);\n    if (book) {\n        book.enabled = enabled;\n    }\n};\n/**\n * 使用初始数据负载填充应用程序状态\n */\nconst hydrateAppState = (data) => {\n    appState.globalRegex = data.globalRegex ?? [];\n    appState.characterRegex = data.characterRegex ?? [];\n    appState.allLorebooks = data.allLorebooks;\n    appState.lorebooks.character = data.characterLorebooks;\n    appState.chatLorebook = data.chatLorebook;\n    appState.lorebookEntries = data.lorebookEntries;\n    setDataLoaded(true);\n};\n/**\n * 重命名世界书\n */\nconst renameLorebookInState = (oldName, newName) => {\n    const book = appState.allLorebooks.find(book => book.name === oldName);\n    if (book) {\n        book.name = newName;\n    }\n    // 更新条目映射\n    const entries = safeGetLorebookEntries(oldName);\n    if (entries.length > 0) {\n        safeSetLorebookEntries(newName, entries);\n        safeDeleteLorebookEntries(oldName);\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvV29ybGRJbmZvT3B0aW1pemVyL3N0YXRlLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQUE7OztHQUdHO0FBRStEO0FBR2xFLGlCQUFpQjtBQUNWLE1BQU0sUUFBUSxHQUFhO0lBQ2hDLFdBQVcsRUFBRSxFQUFFO0lBQ2YsY0FBYyxFQUFFLEVBQUU7SUFDbEIsU0FBUyxFQUFFLEVBQUUsU0FBUyxFQUFFLEVBQUUsRUFBRTtJQUM1QixZQUFZLEVBQUUsSUFBSTtJQUNsQixZQUFZLEVBQUUsRUFBRTtJQUNoQixlQUFlLEVBQUUsSUFBSSxHQUFHLEVBQUU7SUFDMUIsYUFBYSxFQUFFLElBQUksR0FBRyxFQUFFO0lBQ3hCLFNBQVMsRUFBRSxtREFBVztJQUN0QixZQUFZLEVBQUUsS0FBSztJQUNuQixhQUFhLEVBQUUsRUFBRSxHQUFHLDhEQUFzQixFQUFFO0lBQzVDLGVBQWUsRUFBRSxLQUFLO0lBQ3RCLGFBQWEsRUFBRSxJQUFJLEdBQUcsRUFBRTtDQUN6QixDQUFDO0FBRUYsbUNBQW1DO0FBRW5DOztHQUVHO0FBQ0ksTUFBTSxzQkFBc0IsR0FBRyxDQUFDLFFBQWdCLEVBQW1CLEVBQUU7SUFDMUUsSUFBSSxDQUFDO1FBQ0gsSUFBSSxDQUFDLFFBQVEsQ0FBQyxlQUFlLElBQUksQ0FBQyxDQUFDLFFBQVEsQ0FBQyxlQUFlLFlBQVksR0FBRyxDQUFDLEVBQUUsQ0FBQztZQUM1RSxPQUFPLENBQUMsSUFBSSxDQUFDLCtFQUErRSxDQUFDLENBQUM7WUFDOUYsUUFBUSxDQUFDLGVBQWUsR0FBRyxJQUFJLEdBQUcsRUFBRSxDQUFDO1FBQ3ZDLENBQUM7UUFFRCxNQUFNLE9BQU8sR0FBRyxRQUFRLENBQUMsZUFBZSxDQUFDLEdBQUcsQ0FBQyxRQUFRLENBQUMsQ0FBQztRQUN2RCxJQUFJLENBQUMsT0FBTyxFQUFFLENBQUM7WUFDYixPQUFPLENBQUMsR0FBRyxDQUFDLG1EQUFtRCxRQUFRLEVBQUUsQ0FBQyxDQUFDO1lBQzNFLE9BQU8sRUFBRSxDQUFDO1FBQ1osQ0FBQztRQUVELElBQUksQ0FBQyxLQUFLLENBQUMsT0FBTyxDQUFDLE9BQU8sQ0FBQyxFQUFFLENBQUM7WUFDNUIsT0FBTyxDQUFDLElBQUksQ0FBQyx5Q0FBeUMsUUFBUSx5Q0FBeUMsQ0FBQyxDQUFDO1lBQ3pHLE9BQU8sRUFBRSxDQUFDO1FBQ1osQ0FBQztRQUVELE9BQU8sT0FBTyxDQUFDO0lBQ2pCLENBQUM7SUFBQyxPQUFPLEtBQUssRUFBRSxDQUFDO1FBQ2YsT0FBTyxDQUFDLEtBQUssQ0FBQyx1REFBdUQsRUFBRSxLQUFLLENBQUMsQ0FBQztRQUM5RSxPQUFPLEVBQUUsQ0FBQztJQUNaLENBQUM7QUFDSCxDQUFDLENBQUM7QUFFRjs7R0FFRztBQUNJLE1BQU0sc0JBQXNCLEdBQUcsQ0FBQyxRQUFnQixFQUFFLE9BQXdCLEVBQVEsRUFBRTtJQUN6RixJQUFJLENBQUM7UUFDSCxJQUFJLENBQUMsUUFBUSxDQUFDLGVBQWUsSUFBSSxDQUFDLENBQUMsUUFBUSxDQUFDLGVBQWUsWUFBWSxHQUFHLENBQUMsRUFBRSxDQUFDO1lBQzVFLE9BQU8sQ0FBQyxJQUFJLENBQUMsK0VBQStFLENBQUMsQ0FBQztZQUM5RixRQUFRLENBQUMsZUFBZSxHQUFHLElBQUksR0FBRyxFQUFFLENBQUM7UUFDdkMsQ0FBQztRQUVELElBQUksQ0FBQyxLQUFLLENBQUMsT0FBTyxDQUFDLE9BQU8sQ0FBQyxFQUFFLENBQUM7WUFDNUIsT0FBTyxDQUFDLElBQUksQ0FBQyx5Q0FBeUMsUUFBUSxpQ0FBaUMsQ0FBQyxDQUFDO1lBQ2pHLFFBQVEsQ0FBQyxlQUFlLENBQUMsR0FBRyxDQUFDLFFBQVEsRUFBRSxFQUFFLENBQUMsQ0FBQztZQUMzQyxPQUFPO1FBQ1QsQ0FBQztRQUVELFFBQVEsQ0FBQyxlQUFlLENBQUMsR0FBRyxDQUFDLFFBQVEsRUFBRSxPQUFPLENBQUMsQ0FBQztRQUNoRCxPQUFPLENBQUMsR0FBRyxDQUFDLDRCQUE0QixPQUFPLENBQUMsTUFBTSxzQkFBc0IsUUFBUSxFQUFFLENBQUMsQ0FBQztJQUMxRixDQUFDO0lBQUMsT0FBTyxLQUFLLEVBQUUsQ0FBQztRQUNmLE9BQU8sQ0FBQyxLQUFLLENBQUMsdURBQXVELEVBQUUsS0FBSyxDQUFDLENBQUM7SUFDaEYsQ0FBQztBQUNILENBQUMsQ0FBQztBQUVGOztHQUVHO0FBQ0ksTUFBTSx5QkFBeUIsR0FBRyxDQUFDLFFBQWdCLEVBQVEsRUFBRTtJQUNsRSxJQUFJLENBQUM7UUFDSCxJQUFJLENBQUMsUUFBUSxDQUFDLGVBQWUsSUFBSSxDQUFDLENBQUMsUUFBUSxDQUFDLGVBQWUsWUFBWSxHQUFHLENBQUMsRUFBRSxDQUFDO1lBQzVFLE9BQU8sQ0FBQyxJQUFJLENBQUMsK0VBQStFLENBQUMsQ0FBQztZQUM5RixRQUFRLENBQUMsZUFBZSxHQUFHLElBQUksR0FBRyxFQUFFLENBQUM7WUFDckMsT0FBTztRQUNULENBQUM7UUFFRCxNQUFNLE9BQU8sR0FBRyxRQUFRLENBQUMsZUFBZSxDQUFDLE1BQU0sQ0FBQyxRQUFRLENBQUMsQ0FBQztRQUMxRCxJQUFJLE9BQU8sRUFBRSxDQUFDO1lBQ1osT0FBTyxDQUFDLEdBQUcsQ0FBQyxrREFBa0QsUUFBUSxFQUFFLENBQUMsQ0FBQztRQUM1RSxDQUFDO2FBQU0sQ0FBQztZQUNOLE9BQU8sQ0FBQyxHQUFHLENBQUMsNkRBQTZELFFBQVEsRUFBRSxDQUFDLENBQUM7UUFDdkYsQ0FBQztJQUNILENBQUM7SUFBQyxPQUFPLEtBQUssRUFBRSxDQUFDO1FBQ2YsT0FBTyxDQUFDLEtBQUssQ0FBQywwREFBMEQsRUFBRSxLQUFLLENBQUMsQ0FBQztJQUNuRixDQUFDO0FBQ0gsQ0FBQyxDQUFDO0FBRUY7O0dBRUc7QUFDSSxNQUFNLHdCQUF3QixHQUFHLEdBQVMsRUFBRTtJQUNqRCxJQUFJLENBQUM7UUFDSCxJQUFJLENBQUMsUUFBUSxDQUFDLGVBQWUsSUFBSSxDQUFDLENBQUMsUUFBUSxDQUFDLGVBQWUsWUFBWSxHQUFHLENBQUMsRUFBRSxDQUFDO1lBQzVFLE9BQU8sQ0FBQyxJQUFJLENBQUMsK0VBQStFLENBQUMsQ0FBQztZQUM5RixRQUFRLENBQUMsZUFBZSxHQUFHLElBQUksR0FBRyxFQUFFLENBQUM7WUFDckMsT0FBTztRQUNULENBQUM7UUFFRCxNQUFNLElBQUksR0FBRyxRQUFRLENBQUMsZUFBZSxDQUFDLElBQUksQ0FBQztRQUMzQyxRQUFRLENBQUMsZUFBZSxDQUFDLEtBQUssRUFBRSxDQUFDO1FBQ2pDLE9BQU8sQ0FBQyxHQUFHLENBQUMsZ0NBQWdDLElBQUksOEJBQThCLENBQUMsQ0FBQztJQUNsRixDQUFDO0lBQUMsT0FBTyxLQUFLLEVBQUUsQ0FBQztRQUNmLE9BQU8sQ0FBQyxLQUFLLENBQUMseURBQXlELEVBQUUsS0FBSyxDQUFDLENBQUM7UUFDaEYsVUFBVTtRQUNWLFFBQVEsQ0FBQyxlQUFlLEdBQUcsSUFBSSxHQUFHLEVBQUUsQ0FBQztJQUN2QyxDQUFDO0FBQ0gsQ0FBQyxDQUFDO0FBRUY7O0dBRUc7QUFDSSxNQUFNLHNCQUFzQixHQUFHLENBQUMsUUFBZ0IsRUFBVyxFQUFFO0lBQ2xFLElBQUksQ0FBQztRQUNILElBQUksQ0FBQyxRQUFRLENBQUMsZUFBZSxJQUFJLENBQUMsQ0FBQyxRQUFRLENBQUMsZUFBZSxZQUFZLEdBQUcsQ0FBQyxFQUFFLENBQUM7WUFDNUUsT0FBTyxDQUFDLElBQUksQ0FBQywrRUFBK0UsQ0FBQyxDQUFDO1lBQzlGLFFBQVEsQ0FBQyxlQUFlLEdBQUcsSUFBSSxHQUFHLEVBQUUsQ0FBQztZQUNyQyxPQUFPLEtBQUssQ0FBQztRQUNmLENBQUM7UUFFRCxPQUFPLFFBQVEsQ0FBQyxlQUFlLENBQUMsR0FBRyxDQUFDLFFBQVEsQ0FBQyxDQUFDO0lBQ2hELENBQUM7SUFBQyxPQUFPLEtBQUssRUFBRSxDQUFDO1FBQ2YsT0FBTyxDQUFDLEtBQUssQ0FBQyx1REFBdUQsRUFBRSxLQUFLLENBQUMsQ0FBQztRQUM5RSxPQUFPLEtBQUssQ0FBQztJQUNmLENBQUM7QUFDSCxDQUFDLENBQUM7QUFFRixpQkFBaUI7QUFFakI7O0dBRUc7QUFDSSxNQUFNLGFBQWEsR0FBRyxDQUFDLE1BQWUsRUFBUSxFQUFFO0lBQ3JELFFBQVEsQ0FBQyxZQUFZLEdBQUcsTUFBTSxDQUFDO0FBQ2pDLENBQUMsQ0FBQztBQUVGOztHQUVHO0FBQ0ksTUFBTSxZQUFZLEdBQUcsQ0FBQyxHQUFXLEVBQVEsRUFBRTtJQUNoRCxRQUFRLENBQUMsU0FBUyxHQUFHLEdBQUcsQ0FBQztBQUMzQixDQUFDLENBQUM7QUFFRjs7R0FFRztBQUNJLE1BQU0sc0JBQXNCLEdBQUcsQ0FBQyxZQUFtQixFQUFFLFFBQWEsRUFBUSxFQUFFO0lBQ2pGLE1BQU0sa0JBQWtCLEdBQUcsS0FBSyxDQUFDLE9BQU8sQ0FBQyxZQUFZLENBQUM7UUFDcEQsQ0FBQyxDQUFDLFlBQVksQ0FBQyxNQUFNLENBQUMsQ0FBQyxDQUFNLEVBQUUsRUFBRSxDQUFDLENBQUMsQ0FBQyxLQUFLLEtBQUssV0FBVyxDQUFDO1FBQzFELENBQUMsQ0FBQyxFQUFFLENBQUM7SUFFUCxlQUFlO0lBQ2YsTUFBTSxXQUFXLEdBQUcsUUFBUSxFQUFFLElBQUksRUFBRSxVQUFVLEVBQUUsYUFBYSxJQUFJLEVBQUUsQ0FBQztJQUVwRSx3QkFBd0I7SUFDeEIsTUFBTSxrQkFBa0IsR0FBRyxJQUFJLEdBQUcsQ0FDaEMsa0JBQWtCLENBQUMsR0FBRyxDQUFDLENBQUMsQ0FBTSxFQUFFLEVBQUUsQ0FBQyxHQUFHLENBQUMsQ0FBQyxXQUFXLEtBQUssQ0FBQyxDQUFDLFVBQVUsS0FBSyxDQUFDLENBQUMsY0FBYyxFQUFFLENBQUMsQ0FDN0YsQ0FBQztJQUVGLGlCQUFpQjtJQUNqQixNQUFNLGlCQUFpQixHQUFHLFdBQVcsQ0FBQyxNQUFNLENBQUMsQ0FBQyxDQUFNLEVBQUUsRUFBRTtRQUN0RCxNQUFNLFVBQVUsR0FBRyxHQUFHLENBQUMsQ0FBQyxXQUFXLEtBQUssQ0FBQyxDQUFDLFVBQVUsS0FBSyxDQUFDLENBQUMsY0FBYyxFQUFFLENBQUM7UUFDNUUsT0FBTyxDQUFDLGtCQUFrQixDQUFDLEdBQUcsQ0FBQyxVQUFVLENBQUMsQ0FBQztJQUM3QyxDQUFDLENBQUMsQ0FBQztJQUVILFFBQVEsQ0FBQyxjQUFjLEdBQUcsQ0FBQyxHQUFHLGtCQUFrQixFQUFFLEdBQUcsaUJBQWlCLENBQUMsQ0FBQztBQUMxRSxDQUFDLENBQUM7QUFFRixpQkFBaUI7QUFFakI7O0dBRUc7QUFDSSxNQUFNLHFCQUFxQixHQUFHLEdBQVksRUFBRTtJQUNqRCxRQUFRLENBQUMsZUFBZSxHQUFHLENBQUMsUUFBUSxDQUFDLGVBQWUsQ0FBQztJQUNyRCxJQUFJLENBQUMsUUFBUSxDQUFDLGVBQWUsRUFBRSxDQUFDO1FBQzlCLGtCQUFrQixFQUFFLENBQUM7SUFDdkIsQ0FBQztJQUNELE9BQU8sUUFBUSxDQUFDLGVBQWUsQ0FBQztBQUNsQyxDQUFDLENBQUM7QUFFRjs7R0FFRztBQUNJLE1BQU0sa0JBQWtCLEdBQUcsQ0FBQyxPQUFlLEVBQVcsRUFBRTtJQUM3RCxJQUFJLFFBQVEsQ0FBQyxhQUFhLENBQUMsR0FBRyxDQUFDLE9BQU8sQ0FBQyxFQUFFLENBQUM7UUFDeEMsUUFBUSxDQUFDLGFBQWEsQ0FBQyxNQUFNLENBQUMsT0FBTyxDQUFDLENBQUM7UUFDdkMsT0FBTyxLQUFLLENBQUM7SUFDZixDQUFDO1NBQU0sQ0FBQztRQUNOLFFBQVEsQ0FBQyxhQUFhLENBQUMsR0FBRyxDQUFDLE9BQU8sQ0FBQyxDQUFDO1FBQ3BDLE9BQU8sSUFBSSxDQUFDO0lBQ2QsQ0FBQztBQUNILENBQUMsQ0FBQztBQUVGOztHQUVHO0FBQ0ksTUFBTSxrQkFBa0IsR0FBRyxHQUFTLEVBQUU7SUFDM0MsUUFBUSxDQUFDLGFBQWEsQ0FBQyxLQUFLLEVBQUUsQ0FBQztBQUNqQyxDQUFDLENBQUM7QUFFRjs7R0FFRztBQUNJLE1BQU0sZUFBZSxHQUFHLENBQUMsT0FBZSxFQUFRLEVBQUU7SUFDdkQsUUFBUSxDQUFDLGFBQWEsQ0FBQyxHQUFHLENBQUMsT0FBTyxDQUFDLENBQUM7QUFDdEMsQ0FBQyxDQUFDO0FBRUY7O0dBRUc7QUFDSSxNQUFNLGtCQUFrQixHQUFHLENBQUMsT0FBZSxFQUFRLEVBQUU7SUFDMUQsUUFBUSxDQUFDLGFBQWEsQ0FBQyxNQUFNLENBQUMsT0FBTyxDQUFDLENBQUM7QUFDekMsQ0FBQyxDQUFDO0FBRUY7O0dBRUc7QUFDSSxNQUFNLGNBQWMsR0FBRyxDQUFDLE9BQWUsRUFBVyxFQUFFO0lBQ3pELE9BQU8sUUFBUSxDQUFDLGFBQWEsQ0FBQyxHQUFHLENBQUMsT0FBTyxDQUFDLENBQUM7QUFDN0MsQ0FBQyxDQUFDO0FBRUYsa0JBQWtCO0FBRWxCOztHQUVHO0FBQ0ksTUFBTSxrQkFBa0IsR0FBRyxDQUFDLFVBQStDLEVBQUUsT0FBZ0IsRUFBUSxFQUFFO0lBQzVHLFFBQVEsQ0FBQyxhQUFhLENBQUMsVUFBVSxDQUFDLEdBQUcsT0FBTyxDQUFDO0FBQy9DLENBQUMsQ0FBQztBQUVGOztHQUVHO0FBQ0ksTUFBTSxrQkFBa0IsR0FBRyxHQUFTLEVBQUU7SUFDM0MsUUFBUSxDQUFDLGFBQWEsR0FBRyxFQUFFLEdBQUcsOERBQXNCLEVBQUUsQ0FBQztBQUN6RCxDQUFDLENBQUM7QUFFRixnQkFBZ0I7QUFFaEI7O0dBRUc7QUFDSSxNQUFNLGtCQUFrQixHQUFHLENBQUMsUUFBZ0IsRUFBRSxVQUFtQixJQUFJLEVBQVEsRUFBRTtJQUNwRixNQUFNLFlBQVksR0FBRyxRQUFRLENBQUMsWUFBWSxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsRUFBRSxDQUFDLElBQUksQ0FBQyxJQUFJLEtBQUssUUFBUSxDQUFDLENBQUM7SUFDaEYsSUFBSSxDQUFDLFlBQVksRUFBRSxDQUFDO1FBQ2xCLFFBQVEsQ0FBQyxZQUFZLENBQUMsSUFBSSxDQUFDLEVBQUUsSUFBSSxFQUFFLFFBQVEsRUFBRSxPQUFPLEVBQUUsQ0FBQyxDQUFDO0lBQzFELENBQUM7QUFDSCxDQUFDLENBQUM7QUFFRjs7R0FFRztBQUNJLE1BQU0sdUJBQXVCLEdBQUcsQ0FBQyxRQUFnQixFQUFRLEVBQUU7SUFDaEUsTUFBTSxLQUFLLEdBQUcsUUFBUSxDQUFDLFlBQVksQ0FBQyxTQUFTLENBQUMsSUFBSSxDQUFDLEVBQUUsQ0FBQyxJQUFJLENBQUMsSUFBSSxLQUFLLFFBQVEsQ0FBQyxDQUFDO0lBQzlFLElBQUksS0FBSyxLQUFLLENBQUMsQ0FBQyxFQUFFLENBQUM7UUFDakIsUUFBUSxDQUFDLFlBQVksQ0FBQyxNQUFNLENBQUMsS0FBSyxFQUFFLENBQUMsQ0FBQyxDQUFDO0lBQ3pDLENBQUM7SUFDRCx5QkFBeUIsQ0FBQyxRQUFRLENBQUMsQ0FBQztBQUN0QyxDQUFDLENBQUM7QUFFRjs7R0FFRztBQUNJLE1BQU0sbUJBQW1CLEdBQUcsQ0FBQyxRQUFnQixFQUFFLE9BQWdCLEVBQVEsRUFBRTtJQUM5RSxNQUFNLElBQUksR0FBRyxRQUFRLENBQUMsWUFBWSxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsRUFBRSxDQUFDLElBQUksQ0FBQyxJQUFJLEtBQUssUUFBUSxDQUFDLENBQUM7SUFDeEUsSUFBSSxJQUFJLEVBQUUsQ0FBQztRQUNULElBQUksQ0FBQyxPQUFPLEdBQUcsT0FBTyxDQUFDO0lBQ3pCLENBQUM7QUFDSCxDQUFDLENBQUM7QUFFRjs7R0FFRztBQUNJLE1BQU0sZUFBZSxHQUFHLENBQUMsSUFBd0IsRUFBUSxFQUFFO0lBQ2hFLFFBQVEsQ0FBQyxXQUFXLEdBQUcsSUFBSSxDQUFDLFdBQVcsSUFBSSxFQUFFLENBQUM7SUFDOUMsUUFBUSxDQUFDLGNBQWMsR0FBRyxJQUFJLENBQUMsY0FBYyxJQUFJLEVBQUUsQ0FBQztJQUNwRCxRQUFRLENBQUMsWUFBWSxHQUFHLElBQUksQ0FBQyxZQUFZLENBQUM7SUFDMUMsUUFBUSxDQUFDLFNBQVMsQ0FBQyxTQUFTLEdBQUcsSUFBSSxDQUFDLGtCQUFrQixDQUFDO0lBQ3ZELFFBQVEsQ0FBQyxZQUFZLEdBQUcsSUFBSSxDQUFDLFlBQVksQ0FBQztJQUMxQyxRQUFRLENBQUMsZUFBZSxHQUFHLElBQUksQ0FBQyxlQUFlLENBQUM7SUFDaEQsYUFBYSxDQUFDLElBQUksQ0FBQyxDQUFDO0FBQ3RCLENBQUMsQ0FBQztBQUVGOztHQUVHO0FBQ0ksTUFBTSxxQkFBcUIsR0FBRyxDQUFDLE9BQWUsRUFBRSxPQUFlLEVBQVEsRUFBRTtJQUM5RSxNQUFNLElBQUksR0FBRyxRQUFRLENBQUMsWUFBWSxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsRUFBRSxDQUFDLElBQUksQ0FBQyxJQUFJLEtBQUssT0FBTyxDQUFDLENBQUM7SUFDdkUsSUFBSSxJQUFJLEVBQUUsQ0FBQztRQUNULElBQUksQ0FBQyxJQUFJLEdBQUcsT0FBTyxDQUFDO0lBQ3RCLENBQUM7SUFFRCxTQUFTO0lBQ1QsTUFBTSxPQUFPLEdBQUcsc0JBQXNCLENBQUMsT0FBTyxDQUFDLENBQUM7SUFDaEQsSUFBSSxPQUFPLENBQUMsTUFBTSxHQUFHLENBQUMsRUFBRSxDQUFDO1FBQ3ZCLHNCQUFzQixDQUFDLE9BQU8sRUFBRSxPQUFPLENBQUMsQ0FBQztRQUN6Qyx5QkFBeUIsQ0FBQyxPQUFPLENBQUMsQ0FBQztJQUNyQyxDQUFDO0FBQ0gsQ0FBQyxDQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vdGF2ZXJuX2hlbHBlcl90ZW1wbGF0ZS8uL3NyYy9Xb3JsZEluZm9PcHRpbWl6ZXIvc3RhdGUudHM/Il0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICog5LiW55WM5Lmm5LyY5YyW5Zmo54q25oCB566h55CGXG4gKiDljIXlkKsgYXBwU3RhdGUg5a6a5LmJ5ZKM5omA5pyJ54q25oCB5pON5L2c5Ye95pWw77yIc2FmZSog57O75YiX5Ye95pWw77yJXG4gKi9cblxuaW1wb3J0IHsgREVGQVVMVF9TRUFSQ0hfRklMVEVSUywgREVGQVVMVF9UQUIgfSBmcm9tICcuL2NvbnN0YW50cyc7XG5pbXBvcnQgdHlwZSB7IEFwcFN0YXRlLCBJbml0aWFsRGF0YVBheWxvYWQsIExvcmVib29rRW50cnkgfSBmcm9tICcuL3R5cGVzJztcblxuLy8gLS0tIOW6lOeUqOeoi+W6j+eKtuaAgSAtLS1cbmV4cG9ydCBjb25zdCBhcHBTdGF0ZTogQXBwU3RhdGUgPSB7XG4gIGdsb2JhbFJlZ2V4OiBbXSxcbiAgY2hhcmFjdGVyUmVnZXg6IFtdLFxuICBsb3JlYm9va3M6IHsgY2hhcmFjdGVyOiBbXSB9LFxuICBjaGF0TG9yZWJvb2s6IG51bGwsXG4gIGFsbExvcmVib29rczogW10sXG4gIGxvcmVib29rRW50cmllczogbmV3IE1hcCgpLFxuICBsb3JlYm9va1VzYWdlOiBuZXcgTWFwKCksXG4gIGFjdGl2ZVRhYjogREVGQVVMVF9UQUIsXG4gIGlzRGF0YUxvYWRlZDogZmFsc2UsXG4gIHNlYXJjaEZpbHRlcnM6IHsgLi4uREVGQVVMVF9TRUFSQ0hfRklMVEVSUyB9LFxuICBtdWx0aVNlbGVjdE1vZGU6IGZhbHNlLFxuICBzZWxlY3RlZEl0ZW1zOiBuZXcgU2V0KCksXG59O1xuXG4vLyAtLS0g5a6J5YWo6K6/6ZeuIGxvcmVib29rRW50cmllcyDnmoTlh73mlbAgLS0tXG5cbi8qKlxuICog5a6J5YWo6I635Y+W5LiW55WM5Lmm5p2h55uuXG4gKi9cbmV4cG9ydCBjb25zdCBzYWZlR2V0TG9yZWJvb2tFbnRyaWVzID0gKGJvb2tOYW1lOiBzdHJpbmcpOiBMb3JlYm9va0VudHJ5W10gPT4ge1xuICB0cnkge1xuICAgIGlmICghYXBwU3RhdGUubG9yZWJvb2tFbnRyaWVzIHx8ICEoYXBwU3RhdGUubG9yZWJvb2tFbnRyaWVzIGluc3RhbmNlb2YgTWFwKSkge1xuICAgICAgY29uc29sZS53YXJuKCdbV29ybGRJbmZvT3B0aW1pemVyXSBhcHBTdGF0ZS5sb3JlYm9va0VudHJpZXMgaXMgbm90IGEgTWFwLCByZWluaXRpYWxpemluZy4uLicpO1xuICAgICAgYXBwU3RhdGUubG9yZWJvb2tFbnRyaWVzID0gbmV3IE1hcCgpO1xuICAgIH1cblxuICAgIGNvbnN0IGVudHJpZXMgPSBhcHBTdGF0ZS5sb3JlYm9va0VudHJpZXMuZ2V0KGJvb2tOYW1lKTtcbiAgICBpZiAoIWVudHJpZXMpIHtcbiAgICAgIGNvbnNvbGUubG9nKGBbV29ybGRJbmZvT3B0aW1pemVyXSBObyBlbnRyaWVzIGZvdW5kIGZvciBib29rOiAke2Jvb2tOYW1lfWApO1xuICAgICAgcmV0dXJuIFtdO1xuICAgIH1cblxuICAgIGlmICghQXJyYXkuaXNBcnJheShlbnRyaWVzKSkge1xuICAgICAgY29uc29sZS53YXJuKGBbV29ybGRJbmZvT3B0aW1pemVyXSBFbnRyaWVzIGZvciBib29rICR7Ym9va05hbWV9IGlzIG5vdCBhbiBhcnJheSwgcmV0dXJuaW5nIGVtcHR5IGFycmF5YCk7XG4gICAgICByZXR1cm4gW107XG4gICAgfVxuXG4gICAgcmV0dXJuIGVudHJpZXM7XG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcignW1dvcmxkSW5mb09wdGltaXplcl0gRXJyb3IgaW4gc2FmZUdldExvcmVib29rRW50cmllczonLCBlcnJvcik7XG4gICAgcmV0dXJuIFtdO1xuICB9XG59O1xuXG4vKipcbiAqIOWuieWFqOiuvue9ruS4lueVjOS5puadoeebrlxuICovXG5leHBvcnQgY29uc3Qgc2FmZVNldExvcmVib29rRW50cmllcyA9IChib29rTmFtZTogc3RyaW5nLCBlbnRyaWVzOiBMb3JlYm9va0VudHJ5W10pOiB2b2lkID0+IHtcbiAgdHJ5IHtcbiAgICBpZiAoIWFwcFN0YXRlLmxvcmVib29rRW50cmllcyB8fCAhKGFwcFN0YXRlLmxvcmVib29rRW50cmllcyBpbnN0YW5jZW9mIE1hcCkpIHtcbiAgICAgIGNvbnNvbGUud2FybignW1dvcmxkSW5mb09wdGltaXplcl0gYXBwU3RhdGUubG9yZWJvb2tFbnRyaWVzIGlzIG5vdCBhIE1hcCwgcmVpbml0aWFsaXppbmcuLi4nKTtcbiAgICAgIGFwcFN0YXRlLmxvcmVib29rRW50cmllcyA9IG5ldyBNYXAoKTtcbiAgICB9XG5cbiAgICBpZiAoIUFycmF5LmlzQXJyYXkoZW50cmllcykpIHtcbiAgICAgIGNvbnNvbGUud2FybihgW1dvcmxkSW5mb09wdGltaXplcl0gRW50cmllcyBmb3IgYm9vayAke2Jvb2tOYW1lfSBpcyBub3QgYW4gYXJyYXksIGNvbnZlcnRpbmcuLi5gKTtcbiAgICAgIGFwcFN0YXRlLmxvcmVib29rRW50cmllcy5zZXQoYm9va05hbWUsIFtdKTtcbiAgICAgIHJldHVybjtcbiAgICB9XG5cbiAgICBhcHBTdGF0ZS5sb3JlYm9va0VudHJpZXMuc2V0KGJvb2tOYW1lLCBlbnRyaWVzKTtcbiAgICBjb25zb2xlLmxvZyhgW1dvcmxkSW5mb09wdGltaXplcl0gU2V0ICR7ZW50cmllcy5sZW5ndGh9IGVudHJpZXMgZm9yIGJvb2s6ICR7Ym9va05hbWV9YCk7XG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcignW1dvcmxkSW5mb09wdGltaXplcl0gRXJyb3IgaW4gc2FmZVNldExvcmVib29rRW50cmllczonLCBlcnJvcik7XG4gIH1cbn07XG5cbi8qKlxuICog5a6J5YWo5Yig6Zmk5LiW55WM5Lmm5p2h55uuXG4gKi9cbmV4cG9ydCBjb25zdCBzYWZlRGVsZXRlTG9yZWJvb2tFbnRyaWVzID0gKGJvb2tOYW1lOiBzdHJpbmcpOiB2b2lkID0+IHtcbiAgdHJ5IHtcbiAgICBpZiAoIWFwcFN0YXRlLmxvcmVib29rRW50cmllcyB8fCAhKGFwcFN0YXRlLmxvcmVib29rRW50cmllcyBpbnN0YW5jZW9mIE1hcCkpIHtcbiAgICAgIGNvbnNvbGUud2FybignW1dvcmxkSW5mb09wdGltaXplcl0gYXBwU3RhdGUubG9yZWJvb2tFbnRyaWVzIGlzIG5vdCBhIE1hcCwgcmVpbml0aWFsaXppbmcuLi4nKTtcbiAgICAgIGFwcFN0YXRlLmxvcmVib29rRW50cmllcyA9IG5ldyBNYXAoKTtcbiAgICAgIHJldHVybjtcbiAgICB9XG5cbiAgICBjb25zdCBkZWxldGVkID0gYXBwU3RhdGUubG9yZWJvb2tFbnRyaWVzLmRlbGV0ZShib29rTmFtZSk7XG4gICAgaWYgKGRlbGV0ZWQpIHtcbiAgICAgIGNvbnNvbGUubG9nKGBbV29ybGRJbmZvT3B0aW1pemVyXSBEZWxldGVkIGVudHJpZXMgZm9yIGJvb2s6ICR7Ym9va05hbWV9YCk7XG4gICAgfSBlbHNlIHtcbiAgICAgIGNvbnNvbGUubG9nKGBbV29ybGRJbmZvT3B0aW1pemVyXSBObyBlbnRyaWVzIGZvdW5kIHRvIGRlbGV0ZSBmb3IgYm9vazogJHtib29rTmFtZX1gKTtcbiAgICB9XG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcignW1dvcmxkSW5mb09wdGltaXplcl0gRXJyb3IgaW4gc2FmZURlbGV0ZUxvcmVib29rRW50cmllczonLCBlcnJvcik7XG4gIH1cbn07XG5cbi8qKlxuICog5a6J5YWo5riF56m65omA5pyJ5LiW55WM5Lmm5p2h55uuXG4gKi9cbmV4cG9ydCBjb25zdCBzYWZlQ2xlYXJMb3JlYm9va0VudHJpZXMgPSAoKTogdm9pZCA9PiB7XG4gIHRyeSB7XG4gICAgaWYgKCFhcHBTdGF0ZS5sb3JlYm9va0VudHJpZXMgfHwgIShhcHBTdGF0ZS5sb3JlYm9va0VudHJpZXMgaW5zdGFuY2VvZiBNYXApKSB7XG4gICAgICBjb25zb2xlLndhcm4oJ1tXb3JsZEluZm9PcHRpbWl6ZXJdIGFwcFN0YXRlLmxvcmVib29rRW50cmllcyBpcyBub3QgYSBNYXAsIHJlaW5pdGlhbGl6aW5nLi4uJyk7XG4gICAgICBhcHBTdGF0ZS5sb3JlYm9va0VudHJpZXMgPSBuZXcgTWFwKCk7XG4gICAgICByZXR1cm47XG4gICAgfVxuXG4gICAgY29uc3Qgc2l6ZSA9IGFwcFN0YXRlLmxvcmVib29rRW50cmllcy5zaXplO1xuICAgIGFwcFN0YXRlLmxvcmVib29rRW50cmllcy5jbGVhcigpO1xuICAgIGNvbnNvbGUubG9nKGBbV29ybGRJbmZvT3B0aW1pemVyXSBDbGVhcmVkICR7c2l6ZX0gbG9yZWJvb2sgZW50cmllcyBmcm9tIHN0YXRlYCk7XG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcignW1dvcmxkSW5mb09wdGltaXplcl0gRXJyb3IgaW4gc2FmZUNsZWFyTG9yZWJvb2tFbnRyaWVzOicsIGVycm9yKTtcbiAgICAvLyDlvLrliLbph43mlrDliJ3lp4vljJZcbiAgICBhcHBTdGF0ZS5sb3JlYm9va0VudHJpZXMgPSBuZXcgTWFwKCk7XG4gIH1cbn07XG5cbi8qKlxuICog5a6J5YWo5qOA5p+l5LiW55WM5Lmm5piv5ZCm5pyJ5p2h55uuXG4gKi9cbmV4cG9ydCBjb25zdCBzYWZlSGFzTG9yZWJvb2tFbnRyaWVzID0gKGJvb2tOYW1lOiBzdHJpbmcpOiBib29sZWFuID0+IHtcbiAgdHJ5IHtcbiAgICBpZiAoIWFwcFN0YXRlLmxvcmVib29rRW50cmllcyB8fCAhKGFwcFN0YXRlLmxvcmVib29rRW50cmllcyBpbnN0YW5jZW9mIE1hcCkpIHtcbiAgICAgIGNvbnNvbGUud2FybignW1dvcmxkSW5mb09wdGltaXplcl0gYXBwU3RhdGUubG9yZWJvb2tFbnRyaWVzIGlzIG5vdCBhIE1hcCwgcmVpbml0aWFsaXppbmcuLi4nKTtcbiAgICAgIGFwcFN0YXRlLmxvcmVib29rRW50cmllcyA9IG5ldyBNYXAoKTtcbiAgICAgIHJldHVybiBmYWxzZTtcbiAgICB9XG5cbiAgICByZXR1cm4gYXBwU3RhdGUubG9yZWJvb2tFbnRyaWVzLmhhcyhib29rTmFtZSk7XG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcignW1dvcmxkSW5mb09wdGltaXplcl0gRXJyb3IgaW4gc2FmZUhhc0xvcmVib29rRW50cmllczonLCBlcnJvcik7XG4gICAgcmV0dXJuIGZhbHNlO1xuICB9XG59O1xuXG4vLyAtLS0g54q25oCB5pu05paw5Ye95pWwIC0tLVxuXG4vKipcbiAqIOiuvue9ruaVsOaNruWKoOi9veeKtuaAgVxuICovXG5leHBvcnQgY29uc3Qgc2V0RGF0YUxvYWRlZCA9IChsb2FkZWQ6IGJvb2xlYW4pOiB2b2lkID0+IHtcbiAgYXBwU3RhdGUuaXNEYXRhTG9hZGVkID0gbG9hZGVkO1xufTtcblxuLyoqXG4gKiDorr7nva7mtLvliqjmoIfnrb7pobVcbiAqL1xuZXhwb3J0IGNvbnN0IHNldEFjdGl2ZVRhYiA9ICh0YWI6IHN0cmluZyk6IHZvaWQgPT4ge1xuICBhcHBTdGF0ZS5hY3RpdmVUYWIgPSB0YWI7XG59O1xuXG4vKipcbiAqIOabtOaWsOinkuiJsuato+WImeihqOi+vuW8j1xuICovXG5leHBvcnQgY29uc3QgdXBkYXRlQ2hhcmFjdGVyUmVnZXhlcyA9IChhbGxVSVJlZ2V4ZXM6IGFueVtdLCBjaGFyRGF0YTogYW55KTogdm9pZCA9PiB7XG4gIGNvbnN0IGNoYXJhY3RlclVJUmVnZXhlcyA9IEFycmF5LmlzQXJyYXkoYWxsVUlSZWdleGVzKVxuICAgID8gYWxsVUlSZWdleGVzLmZpbHRlcigocjogYW55KSA9PiByLnNjb3BlID09PSAnY2hhcmFjdGVyJylcbiAgICA6IFtdO1xuXG4gIC8vIOS7juinkuiJsuWNoeeJh+iOt+WPluato+WImeihqOi+vuW8j1xuICBjb25zdCBjYXJkUmVnZXhlcyA9IGNoYXJEYXRhPy5kYXRhPy5leHRlbnNpb25zPy5yZWdleF9zY3JpcHRzIHx8IFtdO1xuICBcbiAgLy8g5Yib5bu65LiA5LiqU2V05p2l6Lef6LiqVUnkuK3lt7LmnInnmoTmraPliJnooajovr7lvI9cbiAgY29uc3QgdWlSZWdleElkZW50aWZpZXJzID0gbmV3IFNldChcbiAgICBjaGFyYWN0ZXJVSVJlZ2V4ZXMubWFwKChyOiBhbnkpID0+IGAke3Iuc2NyaXB0X25hbWV9Ojoke3IuZmluZF9yZWdleH06OiR7ci5yZXBsYWNlX3N0cmluZ31gKVxuICApO1xuICBcbiAgLy8g6L+H5ruk5Ye65Y2h54mH5Lit54us5pyJ55qE5q2j5YiZ6KGo6L6+5byPXG4gIGNvbnN0IHVuaXF1ZUNhcmRSZWdleGVzID0gY2FyZFJlZ2V4ZXMuZmlsdGVyKChyOiBhbnkpID0+IHtcbiAgICBjb25zdCBpZGVudGlmaWVyID0gYCR7ci5zY3JpcHRfbmFtZX06OiR7ci5maW5kX3JlZ2V4fTo6JHtyLnJlcGxhY2Vfc3RyaW5nfWA7XG4gICAgcmV0dXJuICF1aVJlZ2V4SWRlbnRpZmllcnMuaGFzKGlkZW50aWZpZXIpO1xuICB9KTtcbiAgXG4gIGFwcFN0YXRlLmNoYXJhY3RlclJlZ2V4ID0gWy4uLmNoYXJhY3RlclVJUmVnZXhlcywgLi4udW5pcXVlQ2FyZFJlZ2V4ZXNdO1xufTtcblxuLy8gLS0tIOWkmumAieaooeW8j+euoeeQhiAtLS1cblxuLyoqXG4gKiDliIfmjaLlpJrpgInmqKHlvI9cbiAqL1xuZXhwb3J0IGNvbnN0IHRvZ2dsZU11bHRpU2VsZWN0TW9kZSA9ICgpOiBib29sZWFuID0+IHtcbiAgYXBwU3RhdGUubXVsdGlTZWxlY3RNb2RlID0gIWFwcFN0YXRlLm11bHRpU2VsZWN0TW9kZTtcbiAgaWYgKCFhcHBTdGF0ZS5tdWx0aVNlbGVjdE1vZGUpIHtcbiAgICBjbGVhclNlbGVjdGVkSXRlbXMoKTtcbiAgfVxuICByZXR1cm4gYXBwU3RhdGUubXVsdGlTZWxlY3RNb2RlO1xufTtcblxuLyoqXG4gKiDliIfmjaLpgInkuK3pobnnm65cbiAqL1xuZXhwb3J0IGNvbnN0IHRvZ2dsZVNlbGVjdGVkSXRlbSA9IChpdGVtS2V5OiBzdHJpbmcpOiBib29sZWFuID0+IHtcbiAgaWYgKGFwcFN0YXRlLnNlbGVjdGVkSXRlbXMuaGFzKGl0ZW1LZXkpKSB7XG4gICAgYXBwU3RhdGUuc2VsZWN0ZWRJdGVtcy5kZWxldGUoaXRlbUtleSk7XG4gICAgcmV0dXJuIGZhbHNlO1xuICB9IGVsc2Uge1xuICAgIGFwcFN0YXRlLnNlbGVjdGVkSXRlbXMuYWRkKGl0ZW1LZXkpO1xuICAgIHJldHVybiB0cnVlO1xuICB9XG59O1xuXG4vKipcbiAqIOa4heepuumAieS4remhueebrlxuICovXG5leHBvcnQgY29uc3QgY2xlYXJTZWxlY3RlZEl0ZW1zID0gKCk6IHZvaWQgPT4ge1xuICBhcHBTdGF0ZS5zZWxlY3RlZEl0ZW1zLmNsZWFyKCk7XG59O1xuXG4vKipcbiAqIOa3u+WKoOmAieS4remhueebrlxuICovXG5leHBvcnQgY29uc3QgYWRkU2VsZWN0ZWRJdGVtID0gKGl0ZW1LZXk6IHN0cmluZyk6IHZvaWQgPT4ge1xuICBhcHBTdGF0ZS5zZWxlY3RlZEl0ZW1zLmFkZChpdGVtS2V5KTtcbn07XG5cbi8qKlxuICog56e76Zmk6YCJ5Lit6aG555uuXG4gKi9cbmV4cG9ydCBjb25zdCByZW1vdmVTZWxlY3RlZEl0ZW0gPSAoaXRlbUtleTogc3RyaW5nKTogdm9pZCA9PiB7XG4gIGFwcFN0YXRlLnNlbGVjdGVkSXRlbXMuZGVsZXRlKGl0ZW1LZXkpO1xufTtcblxuLyoqXG4gKiDmo4Dmn6Xpobnnm67mmK/lkKbooqvpgInkuK1cbiAqL1xuZXhwb3J0IGNvbnN0IGlzSXRlbVNlbGVjdGVkID0gKGl0ZW1LZXk6IHN0cmluZyk6IGJvb2xlYW4gPT4ge1xuICByZXR1cm4gYXBwU3RhdGUuc2VsZWN0ZWRJdGVtcy5oYXMoaXRlbUtleSk7XG59O1xuXG4vLyAtLS0g5pCc57Si6L+H5ruk5Zmo566h55CGIC0tLVxuXG4vKipcbiAqIOabtOaWsOaQnOe0oui/h+a7pOWZqFxuICovXG5leHBvcnQgY29uc3QgdXBkYXRlU2VhcmNoRmlsdGVyID0gKGZpbHRlck5hbWU6IGtleW9mIHR5cGVvZiBhcHBTdGF0ZS5zZWFyY2hGaWx0ZXJzLCBlbmFibGVkOiBib29sZWFuKTogdm9pZCA9PiB7XG4gIGFwcFN0YXRlLnNlYXJjaEZpbHRlcnNbZmlsdGVyTmFtZV0gPSBlbmFibGVkO1xufTtcblxuLyoqXG4gKiDph43nva7mkJzntKLov4fmu6TlmahcbiAqL1xuZXhwb3J0IGNvbnN0IHJlc2V0U2VhcmNoRmlsdGVycyA9ICgpOiB2b2lkID0+IHtcbiAgYXBwU3RhdGUuc2VhcmNoRmlsdGVycyA9IHsgLi4uREVGQVVMVF9TRUFSQ0hfRklMVEVSUyB9O1xufTtcblxuLy8gLS0tIOS4lueVjOS5pueuoeeQhiAtLS1cblxuLyoqXG4gKiDmt7vliqDkuJbnlYzkuabliLDnirbmgIFcbiAqL1xuZXhwb3J0IGNvbnN0IGFkZExvcmVib29rVG9TdGF0ZSA9IChib29rTmFtZTogc3RyaW5nLCBlbmFibGVkOiBib29sZWFuID0gdHJ1ZSk6IHZvaWQgPT4ge1xuICBjb25zdCBleGlzdGluZ0Jvb2sgPSBhcHBTdGF0ZS5hbGxMb3JlYm9va3MuZmluZChib29rID0+IGJvb2submFtZSA9PT0gYm9va05hbWUpO1xuICBpZiAoIWV4aXN0aW5nQm9vaykge1xuICAgIGFwcFN0YXRlLmFsbExvcmVib29rcy5wdXNoKHsgbmFtZTogYm9va05hbWUsIGVuYWJsZWQgfSk7XG4gIH1cbn07XG5cbi8qKlxuICog5LuO54q25oCB5Lit56e76Zmk5LiW55WM5LmmXG4gKi9cbmV4cG9ydCBjb25zdCByZW1vdmVMb3JlYm9va0Zyb21TdGF0ZSA9IChib29rTmFtZTogc3RyaW5nKTogdm9pZCA9PiB7XG4gIGNvbnN0IGluZGV4ID0gYXBwU3RhdGUuYWxsTG9yZWJvb2tzLmZpbmRJbmRleChib29rID0+IGJvb2submFtZSA9PT0gYm9va05hbWUpO1xuICBpZiAoaW5kZXggIT09IC0xKSB7XG4gICAgYXBwU3RhdGUuYWxsTG9yZWJvb2tzLnNwbGljZShpbmRleCwgMSk7XG4gIH1cbiAgc2FmZURlbGV0ZUxvcmVib29rRW50cmllcyhib29rTmFtZSk7XG59O1xuXG4vKipcbiAqIOabtOaWsOS4lueVjOS5pueKtuaAgVxuICovXG5leHBvcnQgY29uc3QgdXBkYXRlTG9yZWJvb2tTdGF0ZSA9IChib29rTmFtZTogc3RyaW5nLCBlbmFibGVkOiBib29sZWFuKTogdm9pZCA9PiB7XG4gIGNvbnN0IGJvb2sgPSBhcHBTdGF0ZS5hbGxMb3JlYm9va3MuZmluZChib29rID0+IGJvb2submFtZSA9PT0gYm9va05hbWUpO1xuICBpZiAoYm9vaykge1xuICAgIGJvb2suZW5hYmxlZCA9IGVuYWJsZWQ7XG4gIH1cbn07XG5cbi8qKlxuICog5L2/55So5Yid5aeL5pWw5o2u6LSf6L295aGr5YWF5bqU55So56iL5bqP54q25oCBXG4gKi9cbmV4cG9ydCBjb25zdCBoeWRyYXRlQXBwU3RhdGUgPSAoZGF0YTogSW5pdGlhbERhdGFQYXlsb2FkKTogdm9pZCA9PiB7XG4gIGFwcFN0YXRlLmdsb2JhbFJlZ2V4ID0gZGF0YS5nbG9iYWxSZWdleCA/PyBbXTtcbiAgYXBwU3RhdGUuY2hhcmFjdGVyUmVnZXggPSBkYXRhLmNoYXJhY3RlclJlZ2V4ID8/IFtdO1xuICBhcHBTdGF0ZS5hbGxMb3JlYm9va3MgPSBkYXRhLmFsbExvcmVib29rcztcbiAgYXBwU3RhdGUubG9yZWJvb2tzLmNoYXJhY3RlciA9IGRhdGEuY2hhcmFjdGVyTG9yZWJvb2tzO1xuICBhcHBTdGF0ZS5jaGF0TG9yZWJvb2sgPSBkYXRhLmNoYXRMb3JlYm9vaztcbiAgYXBwU3RhdGUubG9yZWJvb2tFbnRyaWVzID0gZGF0YS5sb3JlYm9va0VudHJpZXM7XG4gIHNldERhdGFMb2FkZWQodHJ1ZSk7XG59O1xuXG4vKipcbiAqIOmHjeWRveWQjeS4lueVjOS5plxuICovXG5leHBvcnQgY29uc3QgcmVuYW1lTG9yZWJvb2tJblN0YXRlID0gKG9sZE5hbWU6IHN0cmluZywgbmV3TmFtZTogc3RyaW5nKTogdm9pZCA9PiB7XG4gIGNvbnN0IGJvb2sgPSBhcHBTdGF0ZS5hbGxMb3JlYm9va3MuZmluZChib29rID0+IGJvb2submFtZSA9PT0gb2xkTmFtZSk7XG4gIGlmIChib29rKSB7XG4gICAgYm9vay5uYW1lID0gbmV3TmFtZTtcbiAgfVxuICBcbiAgLy8g5pu05paw5p2h55uu5pig5bCEXG4gIGNvbnN0IGVudHJpZXMgPSBzYWZlR2V0TG9yZWJvb2tFbnRyaWVzKG9sZE5hbWUpO1xuICBpZiAoZW50cmllcy5sZW5ndGggPiAwKSB7XG4gICAgc2FmZVNldExvcmVib29rRW50cmllcyhuZXdOYW1lLCBlbnRyaWVzKTtcbiAgICBzYWZlRGVsZXRlTG9yZWJvb2tFbnRyaWVzKG9sZE5hbWUpO1xuICB9XG59O1xuXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./src/WorldInfoOptimizer/state.ts\n\n}");
  },
  "./src/WorldInfoOptimizer/ui.ts": 
  /*!**************************************!*\
  !*** ./src/WorldInfoOptimizer/ui.ts ***!
  \**************************************/ (__unused_webpack_module, __webpack_exports__, __webpack_require__) => {
    eval('{__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addBasicStyles: () => (/* binding */ addBasicStyles),\n/* harmony export */   createExtensionButton: () => (/* binding */ createExtensionButton),\n/* harmony export */   createMainPanel: () => (/* binding */ createMainPanel),\n/* harmony export */   hidePanel: () => (/* binding */ hidePanel),\n/* harmony export */   renderContent: () => (/* binding */ renderContent),\n/* harmony export */   setGlobalDependencies: () => (/* binding */ setGlobalDependencies),\n/* harmony export */   showModal: () => (/* binding */ showModal),\n/* harmony export */   showPanel: () => (/* binding */ showPanel),\n/* harmony export */   showProgressToast: () => (/* binding */ showProgressToast),\n/* harmony export */   showSuccessTick: () => (/* binding */ showSuccessTick),\n/* harmony export */   updateItemSelectionView: () => (/* binding */ updateItemSelectionView),\n/* harmony export */   updateMultiSelectModeView: () => (/* binding */ updateMultiSelectModeView),\n/* harmony export */   updateSelectionCount: () => (/* binding */ updateSelectionCount)\n/* harmony export */ });\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./constants */ "./src/WorldInfoOptimizer/constants.ts");\n/* harmony import */ var _state__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./state */ "./src/WorldInfoOptimizer/state.ts");\n/* harmony import */ var _keyManager__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./keyManager */ "./src/WorldInfoOptimizer/keyManager.ts");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./utils */ "./src/WorldInfoOptimizer/utils.ts");\n/**\n * 世界书优化器 UI 渲染模块\n * 包含所有DOM操作、组件创建和渲染函数\n */\n\n\n\n\n// --- 全局变量 ---\nlet parentWin;\nlet $;\n/**\n * 设置全局依赖\n */\nconst setGlobalDependencies = (jquery, parentWindow) => {\n    $ = jquery;\n    parentWin = parentWindow;\n};\n// --- 通知和模态框函数 ---\n/**\n * 显示成功提示\n */\nconst showSuccessTick = (message = _constants__WEBPACK_IMPORTED_MODULE_0__.MESSAGES.SUCCESS.OPERATION_SUCCESS, duration = _constants__WEBPACK_IMPORTED_MODULE_0__.TOAST_DURATION) => {\n    const $panel = $(`#${_constants__WEBPACK_IMPORTED_MODULE_0__.PANEL_ID}`, parentWin.document);\n    if ($panel.length === 0)\n        return;\n    $panel.find(`.${_constants__WEBPACK_IMPORTED_MODULE_0__.CSS_CLASSES.TOAST_NOTIFICATION}`).remove();\n    const $toast = $(`\n    <div class="${_constants__WEBPACK_IMPORTED_MODULE_0__.CSS_CLASSES.TOAST_NOTIFICATION}">\n      <i class="fa-solid fa-check-circle"></i>\n      <span>${(0,_utils__WEBPACK_IMPORTED_MODULE_3__.escapeHtml)(message)}</span>\n    </div>\n  `);\n    $panel.append($toast);\n    setTimeout(() => $toast.addClass(\'visible\'), 10);\n    setTimeout(() => {\n        $toast.removeClass(\'visible\');\n        setTimeout(() => $toast.remove(), 300);\n    }, duration);\n};\n/**\n * 显示进度提示\n */\nconst showProgressToast = (initialMessage = \'正在处理...\') => {\n    const $panel = $(`#${_constants__WEBPACK_IMPORTED_MODULE_0__.PANEL_ID}`, parentWin.document);\n    if ($panel.length === 0)\n        return { update: () => { }, remove: () => { } };\n    $panel.find(`.${_constants__WEBPACK_IMPORTED_MODULE_0__.CSS_CLASSES.PROGRESS_TOAST}`).remove();\n    const $toast = $(`\n    <div class="${_constants__WEBPACK_IMPORTED_MODULE_0__.CSS_CLASSES.PROGRESS_TOAST}">\n      <i class="fa-solid fa-spinner fa-spin"></i>\n      <span class="wio-progress-text">${(0,_utils__WEBPACK_IMPORTED_MODULE_3__.escapeHtml)(initialMessage)}</span>\n    </div>\n  `);\n    $panel.append($toast);\n    setTimeout(() => $toast.addClass(\'visible\'), 10);\n    const update = (newMessage) => {\n        $toast.find(\'.wio-progress-text\').html((0,_utils__WEBPACK_IMPORTED_MODULE_3__.escapeHtml)(newMessage));\n    };\n    const remove = () => {\n        $toast.removeClass(\'visible\');\n        setTimeout(() => {\n            $toast.remove();\n        }, 300);\n    };\n    return { update, remove };\n};\n/**\n * 显示模态框\n */\nconst showModal = (options) => {\n    return new Promise((resolve, reject) => {\n        const { type = \'alert\', title = \'通知\', text = \'\', placeholder = \'\', value = \'\' } = options;\n        let buttonsHtml = \'\';\n        if (type === \'alert\')\n            buttonsHtml = \'<button class="wio-modal-btn wio-modal-ok">确定</button>\';\n        else if (type === \'confirm\')\n            buttonsHtml =\n                \'<button class="wio-modal-btn wio-modal-cancel">取消</button><button class="wio-modal-btn wio-modal-ok">确定</button>\';\n        else if (type === \'prompt\')\n            buttonsHtml =\n                \'<button class="wio-modal-btn wio-modal-cancel">取消</button><button class="wio-modal-btn wio-modal-ok">确定</button>\';\n        const inputHtml = type === \'prompt\'\n            ? `<input type="text" class="wio-modal-input" placeholder="${(0,_utils__WEBPACK_IMPORTED_MODULE_3__.escapeHtml)(placeholder)}" value="${(0,_utils__WEBPACK_IMPORTED_MODULE_3__.escapeHtml)(value)}">`\n            : \'\';\n        const modalHtml = `<div class="${_constants__WEBPACK_IMPORTED_MODULE_0__.CSS_CLASSES.MODAL_OVERLAY}"><div class="${_constants__WEBPACK_IMPORTED_MODULE_0__.CSS_CLASSES.MODAL_CONTENT}"><div class="wio-modal-header">${(0,_utils__WEBPACK_IMPORTED_MODULE_3__.escapeHtml)(title)}</div><div class="wio-modal-body"><p>${(0,_utils__WEBPACK_IMPORTED_MODULE_3__.escapeHtmlWithNewlines)(text)}</p>${inputHtml}</div><div class="wio-modal-footer">${buttonsHtml}</div></div></div>`;\n        const $modal = $(modalHtml).hide();\n        const $panel = $(`#${_constants__WEBPACK_IMPORTED_MODULE_0__.PANEL_ID}`, parentWin.document);\n        if ($panel.length > 0) {\n            $panel.append($modal);\n        }\n        else {\n            $(\'body\', parentWin.document).append($modal);\n        }\n        $modal.fadeIn(200);\n        const $input = $modal.find(\'.wio-modal-input\');\n        if (type === \'prompt\')\n            $input.focus().select();\n        const closeModal = (isSuccess, val) => {\n            $modal.fadeOut(200, () => {\n                $modal.remove();\n                if (isSuccess)\n                    resolve(val);\n                else\n                    reject();\n            });\n        };\n        $modal.on(\'click\', \'.wio-modal-ok\', () => {\n            if (type === \'prompt\') {\n                const inputVal = $input.val();\n                closeModal(true, inputVal);\n            }\n            else {\n                closeModal(true);\n            }\n        });\n        $modal.on(\'click\', \'.wio-modal-cancel\', () => closeModal(false));\n        $modal.on(\'click\', (e) => {\n            if (e.target === $modal[0])\n                closeModal(false);\n        });\n    });\n};\n// --- UI 创建函数 ---\n/**\n * 创建主面板\n */\nconst createMainPanel = () => {\n    const parentDoc = parentWin.document;\n    // 检查面板是否已存在\n    if ($(`#${_constants__WEBPACK_IMPORTED_MODULE_0__.PANEL_ID}`, parentDoc).length > 0) {\n        console.log(\'[WorldInfoOptimizer] Panel already exists, skipping creation.\');\n        return;\n    }\n    const panelHtml = `\n    <div id="${_constants__WEBPACK_IMPORTED_MODULE_0__.PANEL_ID}" class="${_constants__WEBPACK_IMPORTED_MODULE_0__.CSS_CLASSES.PANEL}" style="display: none;">\n      <div class="${_constants__WEBPACK_IMPORTED_MODULE_0__.CSS_CLASSES.PANEL_HEADER}">\n        <h3 class="wio-panel-title">\n          <i class="fa-solid fa-book"></i> 世界书优化器\n        </h3>\n        <div class="wio-panel-controls">\n          <button id="${_constants__WEBPACK_IMPORTED_MODULE_0__.REFRESH_BTN_ID}" class="wio-btn wio-btn-icon" title="刷新数据">\n            <i class="fa-solid fa-sync-alt"></i>\n          </button>\n          <button id="wio-close-btn" class="wio-btn wio-btn-icon ${_constants__WEBPACK_IMPORTED_MODULE_0__.CSS_CLASSES.PANEL_CLOSE}" title="关闭">\n            <i class="fa-solid fa-times"></i>\n          </button>\n        </div>\n      </div>\n      <div class="${_constants__WEBPACK_IMPORTED_MODULE_0__.CSS_CLASSES.PANEL_BODY}">\n        <div class="wio-tabs">\n          <button class="${_constants__WEBPACK_IMPORTED_MODULE_0__.CSS_CLASSES.TAB_BTN} ${_constants__WEBPACK_IMPORTED_MODULE_0__.CSS_CLASSES.ACTIVE}" data-tab="global-lore">全局世界书</button>\n          <button class="${_constants__WEBPACK_IMPORTED_MODULE_0__.CSS_CLASSES.TAB_BTN}" data-tab="char-lore">角色世界书</button>\n          <button class="${_constants__WEBPACK_IMPORTED_MODULE_0__.CSS_CLASSES.TAB_BTN}" data-tab="chat-lore">聊天世界书</button>\n          <button class="${_constants__WEBPACK_IMPORTED_MODULE_0__.CSS_CLASSES.TAB_BTN}" data-tab="global-regex">全局正则</button>\n          <button class="${_constants__WEBPACK_IMPORTED_MODULE_0__.CSS_CLASSES.TAB_BTN}" data-tab="char-regex">角色正则</button>\n        </div>\n        <div class="wio-search-container">\n          <div class="wio-search-input-wrapper">\n            <input type="text" id="${_constants__WEBPACK_IMPORTED_MODULE_0__.SEARCH_INPUT_ID}" class="wio-search-input" placeholder="搜索世界书、条目或正则表达式...">\n            <button id="wio-clear-search-btn" class="wio-btn wio-btn-icon wio-clear-search" title="清空搜索">\n              <i class="fa-solid fa-times"></i>\n            </button>\n          </div>\n          <div class="wio-search-filters">\n            <label><input type="checkbox" id="wio-filter-book-name" checked> 世界书名</label>\n            <label><input type="checkbox" id="wio-filter-entry-name" checked> 条目名</label>\n            <label><input type="checkbox" id="wio-filter-keywords" checked> 关键词</label>\n            <label><input type="checkbox" id="wio-filter-content" checked> 内容</label>\n          </div>\n        </div>\n        <div class="wio-toolbar">\n          <div class="wio-toolbar-left">\n            <button id="wio-multi-select-toggle" class="wio-btn wio-multi-select-toggle">\n              <i class="fa-solid fa-check-square"></i> 多选模式\n            </button>\n            <span id="wio-selection-count" class="wio-selection-count" style="display: none;">已选择: 0</span>\n          </div>\n          <div class="wio-toolbar-right">\n            <button id="wio-collapse-current-btn" class="wio-btn wio-btn-secondary" title="折叠当前标签页">\n              <i class="fa-solid fa-compress-alt"></i> 折叠当前\n            </button>\n            <button id="wio-collapse-all-btn" class="wio-btn wio-btn-secondary" title="折叠所有">\n              <i class="fa-solid fa-compress"></i> 全部折叠\n            </button>\n          </div>\n        </div>\n        <div class="wio-multi-select-controls" id="wio-multi-select-controls" style="display: none;">\n          <button id="wio-select-all-btn" class="wio-btn wio-btn-small">全选</button>\n          <button id="wio-deselect-all-btn" class="wio-btn wio-btn-small">取消全选</button>\n          <button id="wio-invert-selection-btn" class="wio-btn wio-btn-small">反选</button>\n          <button id="wio-batch-enable-btn" class="wio-btn wio-btn-small wio-btn-success">批量启用</button>\n          <button id="wio-batch-disable-btn" class="wio-btn wio-btn-small wio-btn-warning">批量禁用</button>\n          <button id="wio-batch-delete-btn" class="wio-btn wio-btn-small wio-btn-danger">批量删除</button>\n        </div>\n        <div class="wio-replace-container" style="display: none;">\n          <div class="wio-replace-inputs">\n            <input type="text" id="wio-replace-input" class="wio-replace-input" placeholder="替换为...">\n            <button id="wio-replace-btn" class="wio-btn wio-btn-primary">替换</button>\n            <button id="wio-replace-close-btn" class="wio-btn wio-btn-secondary">关闭</button>\n          </div>\n        </div>\n        <div id="${_constants__WEBPACK_IMPORTED_MODULE_0__.PANEL_ID}-content" class="wio-content">\n          <p class="wio-info-text">正在加载数据...</p>\n        </div>\n      </div>\n    </div>\n  `;\n    $(\'body\', parentDoc).append(panelHtml);\n    addBasicStyles();\n    console.log(\'[WorldInfoOptimizer] Main panel created successfully.\');\n};\n/**\n * 创建扩展按钮\n */\nconst createExtensionButton = () => {\n    const parentDoc = parentWin.document;\n    // 检查按钮是否已存在\n    if ($(`#${_constants__WEBPACK_IMPORTED_MODULE_0__.BUTTON_ID}`, parentDoc).length > 0) {\n        console.log(\'[WorldInfoOptimizer] Extension button already exists, skipping creation.\');\n        return;\n    }\n    const buttonHtml = `\n    <div id="${_constants__WEBPACK_IMPORTED_MODULE_0__.BUTTON_ID}" class="list-group-item flex-container flexGap5" title="${_constants__WEBPACK_IMPORTED_MODULE_0__.BUTTON_TOOLTIP}">\n      <div class="fa-solid fa-book extensionsMenuExtensionButton" style="background-image: url(\'${_constants__WEBPACK_IMPORTED_MODULE_0__.BUTTON_ICON_URL}\'); background-size: contain; background-repeat: no-repeat; background-position: center;"></div>\n      世界书优化器\n    </div>\n  `;\n    const $extensionsMenu = $(\'#extensionsMenu\', parentDoc);\n    if ($extensionsMenu.length > 0) {\n        $extensionsMenu.append(buttonHtml);\n        console.log(\'[WorldInfoOptimizer] Extension button created successfully.\');\n    }\n    else {\n        console.warn(\'[WorldInfoOptimizer] Extensions menu not found, button not created.\');\n    }\n};\n/**\n * 添加基础样式\n */\nconst addBasicStyles = () => {\n    const parentDoc = parentWin.document;\n    // 检查样式是否已添加\n    if ($(\'#wio-basic-styles\', parentDoc).length > 0) {\n        return;\n    }\n    const styles = `\n    <style id="wio-basic-styles">\n      .${_constants__WEBPACK_IMPORTED_MODULE_0__.CSS_CLASSES.PANEL} {\n        position: fixed;\n        top: 50%;\n        left: 50%;\n        transform: translate(-50%, -50%);\n        width: 90vw;\n        max-width: 1200px;\n        height: 80vh;\n        background: var(--SmartThemeBodyColor);\n        border: 1px solid var(--SmartThemeBorderColor);\n        border-radius: 10px;\n        box-shadow: 0 10px 30px rgba(0,0,0,0.3);\n        z-index: 10000;\n        display: flex;\n        flex-direction: column;\n        font-family: var(--mainFontFamily);\n        color: var(--SmartThemeEmColor);\n      }\n\n      .${_constants__WEBPACK_IMPORTED_MODULE_0__.CSS_CLASSES.PANEL_HEADER} {\n        display: flex;\n        justify-content: space-between;\n        align-items: center;\n        padding: 15px 20px;\n        background: var(--SmartThemeQuoteColor);\n        border-bottom: 1px solid var(--SmartThemeBorderColor);\n        border-radius: 10px 10px 0 0;\n      }\n\n      .wio-panel-title {\n        margin: 0;\n        font-size: 18px;\n        font-weight: 600;\n        display: flex;\n        align-items: center;\n        gap: 8px;\n      }\n\n      .wio-panel-controls {\n        display: flex;\n        gap: 8px;\n      }\n\n      .${_constants__WEBPACK_IMPORTED_MODULE_0__.CSS_CLASSES.PANEL_BODY} {\n        flex: 1;\n        display: flex;\n        flex-direction: column;\n        overflow: hidden;\n      }\n\n      .wio-tabs {\n        display: flex;\n        background: var(--SmartThemeQuoteColor);\n        border-bottom: 1px solid var(--SmartThemeBorderColor);\n      }\n\n      .${_constants__WEBPACK_IMPORTED_MODULE_0__.CSS_CLASSES.TAB_BTN} {\n        flex: 1;\n        padding: 12px 16px;\n        background: transparent;\n        border: none;\n        color: var(--SmartThemeEmColor);\n        cursor: pointer;\n        transition: all 0.2s ease;\n        font-size: 14px;\n        font-weight: 500;\n      }\n\n      .${_constants__WEBPACK_IMPORTED_MODULE_0__.CSS_CLASSES.TAB_BTN}:hover {\n        background: var(--SmartThemeBodyColor);\n      }\n\n      .${_constants__WEBPACK_IMPORTED_MODULE_0__.CSS_CLASSES.TAB_BTN}.${_constants__WEBPACK_IMPORTED_MODULE_0__.CSS_CLASSES.ACTIVE} {\n        background: var(--SmartThemeBodyColor);\n        border-bottom: 2px solid var(--SmartThemeEmColor);\n      }\n\n      .wio-search-container {\n        padding: 15px 20px;\n        background: var(--SmartThemeQuoteColor);\n        border-bottom: 1px solid var(--SmartThemeBorderColor);\n      }\n\n      .wio-search-input-wrapper {\n        position: relative;\n        margin-bottom: 10px;\n      }\n\n      .wio-search-input {\n        width: 100%;\n        padding: 10px 40px 10px 15px;\n        border: 1px solid var(--SmartThemeBorderColor);\n        border-radius: 6px;\n        background: var(--SmartThemeBodyColor);\n        color: var(--SmartThemeEmColor);\n        font-size: 14px;\n      }\n\n      .wio-clear-search {\n        position: absolute;\n        right: 8px;\n        top: 50%;\n        transform: translateY(-50%);\n        background: transparent;\n        border: none;\n        color: var(--SmartThemeEmColor);\n        cursor: pointer;\n        padding: 4px;\n      }\n\n      .wio-search-filters {\n        display: flex;\n        gap: 15px;\n        flex-wrap: wrap;\n      }\n\n      .wio-search-filters label {\n        display: flex;\n        align-items: center;\n        gap: 5px;\n        font-size: 13px;\n        cursor: pointer;\n      }\n\n      .wio-toolbar {\n        display: flex;\n        justify-content: space-between;\n        align-items: center;\n        padding: 10px 20px;\n        background: var(--SmartThemeQuoteColor);\n        border-bottom: 1px solid var(--SmartThemeBorderColor);\n      }\n\n      .wio-toolbar-left, .wio-toolbar-right {\n        display: flex;\n        align-items: center;\n        gap: 10px;\n      }\n\n      .wio-content {\n        flex: 1;\n        overflow-y: auto;\n        padding: 20px;\n      }\n\n      .wio-btn {\n        padding: 8px 16px;\n        border: 1px solid var(--SmartThemeBorderColor);\n        border-radius: 4px;\n        background: var(--SmartThemeBodyColor);\n        color: var(--SmartThemeEmColor);\n        cursor: pointer;\n        font-size: 13px;\n        transition: all 0.2s ease;\n        display: inline-flex;\n        align-items: center;\n        gap: 6px;\n      }\n\n      .wio-btn:hover {\n        background: var(--SmartThemeQuoteColor);\n      }\n\n      .wio-btn-icon {\n        padding: 8px;\n        min-width: 32px;\n        justify-content: center;\n      }\n\n      .${_constants__WEBPACK_IMPORTED_MODULE_0__.CSS_CLASSES.TOAST_NOTIFICATION} {\n        position: absolute;\n        top: 20px;\n        right: 20px;\n        background: #4CAF50;\n        color: white;\n        padding: 12px 16px;\n        border-radius: 6px;\n        display: flex;\n        align-items: center;\n        gap: 8px;\n        opacity: 0;\n        transform: translateX(100%);\n        transition: all 0.3s ease;\n        z-index: 10001;\n      }\n\n      .${_constants__WEBPACK_IMPORTED_MODULE_0__.CSS_CLASSES.TOAST_NOTIFICATION}.visible {\n        opacity: 1;\n        transform: translateX(0);\n      }\n\n      .${_constants__WEBPACK_IMPORTED_MODULE_0__.CSS_CLASSES.PROGRESS_TOAST} {\n        position: absolute;\n        top: 20px;\n        right: 20px;\n        background: var(--SmartThemeBodyColor);\n        border: 1px solid var(--SmartThemeBorderColor);\n        color: var(--SmartThemeEmColor);\n        padding: 12px 16px;\n        border-radius: 6px;\n        display: flex;\n        align-items: center;\n        gap: 8px;\n        opacity: 0;\n        transform: translateX(100%);\n        transition: all 0.3s ease;\n        z-index: 10001;\n      }\n\n      .${_constants__WEBPACK_IMPORTED_MODULE_0__.CSS_CLASSES.PROGRESS_TOAST}.visible {\n        opacity: 1;\n        transform: translateX(0);\n      }\n    </style>\n  `;\n    $(\'head\', parentDoc).append(styles);\n};\n// --- 面板显示/隐藏函数 ---\n/**\n * 隐藏面板\n */\nconst hidePanel = () => {\n    const parentDoc = parentWin.document;\n    const $panel = $(`#${_constants__WEBPACK_IMPORTED_MODULE_0__.PANEL_ID}`, parentDoc);\n    $panel.hide();\n    $(`#${_constants__WEBPACK_IMPORTED_MODULE_0__.BUTTON_ID}`, parentDoc).removeClass(_constants__WEBPACK_IMPORTED_MODULE_0__.CSS_CLASSES.ACTIVE);\n};\n/**\n * 显示面板\n */\nconst showPanel = async (loadDataCallback) => {\n    const parentDoc = parentWin.document;\n    const $panel = $(`#${_constants__WEBPACK_IMPORTED_MODULE_0__.PANEL_ID}`, parentDoc);\n    $panel.css(\'display\', \'flex\');\n    $(`#${_constants__WEBPACK_IMPORTED_MODULE_0__.BUTTON_ID}`, parentDoc).addClass(_constants__WEBPACK_IMPORTED_MODULE_0__.CSS_CLASSES.ACTIVE);\n    // 如果数据未加载，则加载数据\n    if (!_state__WEBPACK_IMPORTED_MODULE_1__.appState.isDataLoaded) {\n        await loadDataCallback();\n    }\n};\n// --- 渲染函数 ---\n/**\n * 渲染内容\n */\nconst renderContent = () => {\n    const $container = $(`#${_constants__WEBPACK_IMPORTED_MODULE_0__.PANEL_ID}-content`, parentWin.document);\n    const searchTerm = $(`#${_constants__WEBPACK_IMPORTED_MODULE_0__.SEARCH_INPUT_ID}`, parentWin.document).val();\n    if (!_state__WEBPACK_IMPORTED_MODULE_1__.appState.isDataLoaded) {\n        $container.html(\'<p class="wio-info-text">正在加载数据...</p>\');\n        return;\n    }\n    // 根据活动标签页渲染不同内容\n    switch (_state__WEBPACK_IMPORTED_MODULE_1__.appState.activeTab) {\n        case \'global-lore\':\n            renderGlobalLorebookView(searchTerm, $container);\n            break;\n        case \'char-lore\':\n            renderCharacterLorebookView(searchTerm, $container);\n            break;\n        case \'chat-lore\':\n            renderChatLorebookView(searchTerm, $container);\n            break;\n        case \'global-regex\':\n            renderRegexView(_state__WEBPACK_IMPORTED_MODULE_1__.appState.globalRegex, searchTerm, $container, \'全局正则\');\n            break;\n        case \'char-regex\':\n            renderRegexView(_state__WEBPACK_IMPORTED_MODULE_1__.appState.characterRegex, searchTerm, $container, \'角色正则\');\n            break;\n        default:\n            $container.html(\'<p class="wio-error-text">未知的标签页类型</p>\');\n    }\n};\n/**\n * 更新选择计数\n */\nconst updateSelectionCount = (count) => {\n    const parentDoc = parentWin.document;\n    $(`#wio-selection-count`, parentDoc).text(`已选择: ${count}`);\n};\n/**\n * 更新多选模式视图\n */\nconst updateMultiSelectModeView = (isEnabled) => {\n    const parentDoc = parentWin.document;\n    const $panel = $(`#${_constants__WEBPACK_IMPORTED_MODULE_0__.PANEL_ID}`, parentDoc);\n    const $toggleButton = $(`#wio-multi-select-toggle`, parentDoc);\n    const $controls = $(`#wio-multi-select-controls`, parentDoc);\n    const $selectionCount = $(`#wio-selection-count`, parentDoc);\n    if (isEnabled) {\n        $panel.addClass(\'wio-multi-select-mode\');\n        $toggleButton.addClass(_constants__WEBPACK_IMPORTED_MODULE_0__.CSS_CLASSES.ACTIVE).html(\'<i class="fa-solid fa-times"></i> 退出多选\');\n        $controls.show();\n        $selectionCount.show();\n    }\n    else {\n        $panel.removeClass(\'wio-multi-select-mode\');\n        $toggleButton.removeClass(_constants__WEBPACK_IMPORTED_MODULE_0__.CSS_CLASSES.ACTIVE).html(\'<i class="fa-solid fa-check-square"></i> 多选模式\');\n        $controls.hide();\n        $selectionCount.hide();\n    }\n};\n/**\n * 更新项目选择视图\n */\nconst updateItemSelectionView = (itemKey, isSelected) => {\n    const parentDoc = parentWin.document;\n    // Use attribute selector for safety, as itemKey can contain special characters\n    const $item = $(`[data-key="${itemKey}"]`, parentDoc);\n    if ($item.length > 0) {\n        $item.toggleClass(\'selected\', isSelected);\n    }\n};\n/**\n * 渲染全局世界书视图\n */\nconst renderGlobalLorebookView = (searchTerm, $container) => {\n    const books = [..._state__WEBPACK_IMPORTED_MODULE_1__.appState.allLorebooks].sort((a, b) => Number(b.enabled) - Number(a.enabled) || a.name.localeCompare(b.name));\n    const filteredData = getFilteredLorebookData(books, searchTerm);\n    if (filteredData.length === 0) {\n        $container.html(\'<p class="wio-info-text">没有找到匹配的世界书。</p>\');\n        return;\n    }\n    let html = \'\';\n    filteredData.forEach(({ book, entries, shouldExpand }) => {\n        const bookHtml = createBookElement(book, entries, shouldExpand, searchTerm);\n        html += bookHtml;\n    });\n    $container.html(html);\n};\n/**\n * 渲染角色世界书视图\n */\nconst renderCharacterLorebookView = (searchTerm, $container) => {\n    const linkedBooks = _state__WEBPACK_IMPORTED_MODULE_1__.appState.lorebooks.character;\n    const context = parentWin.SillyTavern.getContext();\n    const hasActiveCharacter = context.characterId !== undefined && context.characterId !== null;\n    if (!hasActiveCharacter) {\n        $container.html(\'<p class="wio-info-text">没有激活的角色。请先选择一个角色。</p>\');\n        return;\n    }\n    if (!Array.isArray(linkedBooks) || linkedBooks.length === 0) {\n        $container.html(\'<p class="wio-info-text">当前角色没有关联的世界书。</p>\');\n        return;\n    }\n    const books = linkedBooks.map(bookName => ({\n        name: bookName,\n        enabled: _state__WEBPACK_IMPORTED_MODULE_1__.appState.allLorebooks.find(b => b.name === bookName)?.enabled || false,\n    }));\n    const filteredData = getFilteredLorebookData(books, searchTerm);\n    if (filteredData.length === 0) {\n        $container.html(\'<p class="wio-info-text">没有找到匹配的角色世界书。</p>\');\n        return;\n    }\n    let html = \'\';\n    filteredData.forEach(({ book, entries, shouldExpand }) => {\n        const bookHtml = createBookElement(book, entries, shouldExpand, searchTerm);\n        html += bookHtml;\n    });\n    $container.html(html);\n};\n/**\n * 渲染聊天世界书视图\n */\nconst renderChatLorebookView = (searchTerm, $container) => {\n    const bookName = _state__WEBPACK_IMPORTED_MODULE_1__.appState.chatLorebook;\n    const context = parentWin.SillyTavern.getContext();\n    const hasActiveChat = context.chatId !== undefined && context.chatId !== null;\n    if (!hasActiveChat) {\n        $container.html(\'<p class="wio-info-text">没有激活的聊天。请先开始一个聊天。</p>\');\n        return;\n    }\n    if (!bookName) {\n        $container.html(\'<p class="wio-info-text">当前聊天没有关联的世界书。</p>\');\n        return;\n    }\n    const book = { name: bookName, enabled: true };\n    const entries = (0,_state__WEBPACK_IMPORTED_MODULE_1__.safeGetLorebookEntries)(bookName);\n    const filteredEntries = filterEntries(entries, searchTerm);\n    if (filteredEntries.length === 0 && searchTerm) {\n        $container.html(\'<p class="wio-info-text">没有找到匹配的聊天世界书条目。</p>\');\n        return;\n    }\n    const bookHtml = createBookElement(book, filteredEntries, !!searchTerm, searchTerm);\n    $container.html(bookHtml);\n};\n// --- 辅助函数 ---\n/**\n * 获取过滤后的世界书数据\n */\nconst getFilteredLorebookData = (books, searchTerm) => {\n    if (!searchTerm) {\n        return books.map(book => ({\n            book,\n            entries: [...(0,_state__WEBPACK_IMPORTED_MODULE_1__.safeGetLorebookEntries)(book.name)],\n            shouldExpand: false, // 无搜索词时不自动展开\n        }));\n    }\n    return books\n        .map(book => {\n        const entries = (0,_state__WEBPACK_IMPORTED_MODULE_1__.safeGetLorebookEntries)(book.name);\n        const filteredEntries = filterEntries(entries, searchTerm);\n        // 检查书名是否匹配\n        const bookNameMatches = (0,_utils__WEBPACK_IMPORTED_MODULE_3__.isMatch)(book.name, searchTerm);\n        return {\n            book,\n            entries: bookNameMatches ? entries : filteredEntries,\n            shouldExpand: true, // 有搜索词时自动展开\n        };\n    })\n        .filter(({ book, entries }) => (0,_utils__WEBPACK_IMPORTED_MODULE_3__.isMatch)(book.name, searchTerm) || entries.length > 0);\n};\n/**\n * 过滤条目\n */\nconst filterEntries = (entries, searchTerm) => {\n    if (!searchTerm)\n        return entries;\n    return entries.filter(entry => {\n        const { searchFilters } = _state__WEBPACK_IMPORTED_MODULE_1__.appState;\n        if (searchFilters.entryName && (0,_utils__WEBPACK_IMPORTED_MODULE_3__.isMatch)(entry.comment || \'\', searchTerm))\n            return true;\n        if (searchFilters.keywords && entry.keys?.some(key => (0,_utils__WEBPACK_IMPORTED_MODULE_3__.isMatch)(key, searchTerm)))\n            return true;\n        if (searchFilters.content && (0,_utils__WEBPACK_IMPORTED_MODULE_3__.isMatch)(entry.content || \'\', searchTerm))\n            return true;\n        return false;\n    });\n};\n/**\n * 创建世界书元素\n */\nconst createBookElement = (book, entries, shouldExpand, searchTerm) => {\n    const bookKey = (0,_keyManager__WEBPACK_IMPORTED_MODULE_2__.createBookItemKey)(book.name);\n    const entriesHtml = entries.map(entry => createItemElement(entry, \'lore\', book.name, searchTerm)).join(\'\');\n    return `\n    <div class="${_constants__WEBPACK_IMPORTED_MODULE_0__.CSS_CLASSES.BOOK_GROUP} ${shouldExpand ? \'\' : _constants__WEBPACK_IMPORTED_MODULE_0__.CSS_CLASSES.COLLAPSED}" data-book-name="${(0,_utils__WEBPACK_IMPORTED_MODULE_3__.escapeHtml)(book.name)}" data-key="${bookKey}">\n      <div class="${_constants__WEBPACK_IMPORTED_MODULE_0__.CSS_CLASSES.ITEM_HEADER}">\n        <div class="${_constants__WEBPACK_IMPORTED_MODULE_0__.CSS_CLASSES.ITEM_NAME}">\n          ${(0,_utils__WEBPACK_IMPORTED_MODULE_3__.highlightText)((0,_utils__WEBPACK_IMPORTED_MODULE_3__.escapeHtml)(book.name), searchTerm)}\n          <span class="wio-entry-count">(${entries.length})</span>\n        </div>\n        <div class="${_constants__WEBPACK_IMPORTED_MODULE_0__.CSS_CLASSES.ITEM_CONTROLS}">\n          <button class="wio-btn wio-btn-icon wio-toggle-state" title="${book.enabled ? \'禁用\' : \'启用\'}">\n            <i class="fa-solid ${book.enabled ? \'fa-eye\' : \'fa-eye-slash\'}"></i>\n          </button>\n        </div>\n      </div>\n      <div class="${_constants__WEBPACK_IMPORTED_MODULE_0__.CSS_CLASSES.COLLAPSIBLE_CONTENT}" ${shouldExpand ? \'style="display: block;"\' : \'\'}>\n        ${entriesHtml || \'<p class="wio-info-text">此世界书没有条目。</p>\'}\n      </div>\n    </div>\n  `;\n};\n/**\n * 创建项目元素\n */\nconst createItemElement = (item, type, bookName = \'\', searchTerm = \'\') => {\n    const isLore = type === \'lore\';\n    const id = isLore ? item.uid : item.id;\n    const name = isLore ? item.comment || \'无标题条目\' : item.script_name || \'未命名正则\';\n    let itemKey = \'\';\n    if (isLore) {\n        itemKey = (0,_keyManager__WEBPACK_IMPORTED_MODULE_2__.createEntryItemKey)(bookName, id);\n    }\n    else {\n        // Assuming \'regex\' type\n        const scope = _state__WEBPACK_IMPORTED_MODULE_1__.appState.activeTab === \'global-regex\' ? \'global\' : \'character\';\n        itemKey = (0,_keyManager__WEBPACK_IMPORTED_MODULE_2__.createRegexItemKey)(scope, id);\n    }\n    return `\n    <div class="${_constants__WEBPACK_IMPORTED_MODULE_0__.CSS_CLASSES.ITEM_CONTAINER}" data-type="${type}" data-id="${id}" data-book-name="${(0,_utils__WEBPACK_IMPORTED_MODULE_3__.escapeHtml)(bookName)}" data-key="${itemKey}">\n      <div class="${_constants__WEBPACK_IMPORTED_MODULE_0__.CSS_CLASSES.ITEM_HEADER}">\n        <div class="${_constants__WEBPACK_IMPORTED_MODULE_0__.CSS_CLASSES.ITEM_NAME}">\n          ${(0,_utils__WEBPACK_IMPORTED_MODULE_3__.highlightText)((0,_utils__WEBPACK_IMPORTED_MODULE_3__.escapeHtml)(name), searchTerm)}\n        </div>\n        <div class="${_constants__WEBPACK_IMPORTED_MODULE_0__.CSS_CLASSES.ITEM_CONTROLS}">\n          <button class="wio-btn wio-btn-icon wio-toggle-state" title="${item.enabled ? \'禁用\' : \'启用\'}">\n            <i class="fa-solid ${item.enabled ? \'fa-eye\' : \'fa-eye-slash\'}"></i>\n          </button>\n        </div>\n      </div>\n    </div>\n  `;\n};\n/**\n * 渲染正则表达式视图\n */\nconst renderRegexView = (regexes, searchTerm, $container, title) => {\n    if (regexes.length === 0) {\n        $container.html(`<p class="wio-info-text">没有找到${title}。</p>`);\n        return;\n    }\n    const filteredRegexes = regexes.filter(regex => {\n        if (!searchTerm)\n            return true;\n        return ((0,_utils__WEBPACK_IMPORTED_MODULE_3__.isMatch)(regex.script_name || \'\', searchTerm) ||\n            (0,_utils__WEBPACK_IMPORTED_MODULE_3__.isMatch)(regex.find_regex || \'\', searchTerm) ||\n            (0,_utils__WEBPACK_IMPORTED_MODULE_3__.isMatch)(regex.replace_string || \'\', searchTerm));\n    });\n    if (filteredRegexes.length === 0) {\n        $container.html(`<p class="wio-info-text">没有找到匹配的${title}。</p>`);\n        return;\n    }\n    const regexHtml = filteredRegexes.map(regex => createItemElement(regex, \'regex\', \'\', searchTerm)).join(\'\');\n    $container.html(regexHtml);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/WorldInfoOptimizer/ui.ts\n\n}');
  },
  "./src/WorldInfoOptimizer/utils.ts": 
  /*!*****************************************!*\
  !*** ./src/WorldInfoOptimizer/utils.ts ***!
  \*****************************************/ (__unused_webpack_module, __webpack_exports__, __webpack_require__) => {
    eval("{__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   capitalize: () => (/* binding */ capitalize),\n/* harmony export */   createSafeId: () => (/* binding */ createSafeId),\n/* harmony export */   debounce: () => (/* binding */ debounce),\n/* harmony export */   deepClone: () => (/* binding */ deepClone),\n/* harmony export */   errorCatched: () => (/* binding */ errorCatched),\n/* harmony export */   escapeHtml: () => (/* binding */ escapeHtml),\n/* harmony export */   escapeHtmlWithNewlines: () => (/* binding */ escapeHtmlWithNewlines),\n/* harmony export */   escapeRegExp: () => (/* binding */ escapeRegExp),\n/* harmony export */   formatTimestamp: () => (/* binding */ formatTimestamp),\n/* harmony export */   generateUniqueId: () => (/* binding */ generateUniqueId),\n/* harmony export */   getRelativeTime: () => (/* binding */ getRelativeTime),\n/* harmony export */   highlightText: () => (/* binding */ highlightText),\n/* harmony export */   isEmpty: () => (/* binding */ isEmpty),\n/* harmony export */   isMatch: () => (/* binding */ isMatch),\n/* harmony export */   isNumber: () => (/* binding */ isNumber),\n/* harmony export */   isValidArray: () => (/* binding */ isValidArray),\n/* harmony export */   isValidString: () => (/* binding */ isValidString),\n/* harmony export */   throttle: () => (/* binding */ throttle),\n/* harmony export */   truncateText: () => (/* binding */ truncateText),\n/* harmony export */   unescapeHtml: () => (/* binding */ unescapeHtml)\n/* harmony export */ });\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./constants */ \"./src/WorldInfoOptimizer/constants.ts\");\n/**\n * 世界书优化器工具函数\n * 包含 escapeHtml、highlightText、debounce 等纯函数工具\n */\n\n// --- HTML 处理工具 ---\n/**\n * 转义HTML特殊字符\n */\nconst escapeHtml = (text) => {\n    if (typeof text !== 'string')\n        return String(text);\n    const div = document.createElement('div');\n    div.textContent = text;\n    return div.innerHTML;\n};\n/**\n * 处理文本内容并保留换行符\n */\nconst escapeHtmlWithNewlines = (text) => {\n    const escaped = escapeHtml(text);\n    return escaped.replace(/\\n/g, '<br>');\n};\n/**\n * 反转义HTML\n */\nconst unescapeHtml = (html) => {\n    if (typeof html !== 'string')\n        return String(html);\n    const area = document.createElement('textarea');\n    area.innerHTML = html;\n    return area.value;\n};\n// --- 搜索和高亮工具 ---\n/**\n * 高亮搜索文本\n */\nconst highlightText = (text, searchTerm) => {\n    if (!text)\n        return '';\n    // 移除任何现有的高亮标签，防止嵌套\n    const cleanText = text.replace(_constants__WEBPACK_IMPORTED_MODULE_0__.REGEX_PATTERNS.SEARCH_HIGHLIGHT, '');\n    if (!searchTerm.trim())\n        return cleanText;\n    try {\n        // 首先尝试简单的文本包含检查（对JSON更友好）\n        const lowerText = cleanText.toLowerCase();\n        const lowerSearchTerm = searchTerm.toLowerCase();\n        if (lowerText.includes(lowerSearchTerm)) {\n            // 使用简单的字符串替换进行高亮\n            const regex = new RegExp(escapeRegExp(searchTerm), 'gi');\n            return cleanText.replace(regex, '<mark class=\"wio-highlight\">$&</mark>');\n        }\n        return cleanText;\n    }\n    catch (error) {\n        console.warn('[WorldInfoOptimizer] Error highlighting text:', error);\n        return cleanText;\n    }\n};\n/**\n * 检查文本是否匹配搜索词\n */\nconst isMatch = (text, searchTerm) => {\n    if (!searchTerm.trim())\n        return false;\n    if (!text)\n        return false;\n    try {\n        // 首先尝试简单的文本包含检查（对JSON更友好）\n        const lowerText = text.toLowerCase();\n        const lowerSearchTerm = searchTerm.toLowerCase();\n        return lowerText.includes(lowerSearchTerm);\n    }\n    catch (error) {\n        console.warn('[WorldInfoOptimizer] Error in text matching:', error);\n        return false;\n    }\n};\n/**\n * 转义正则表达式特殊字符\n */\nconst escapeRegExp = (string) => {\n    return string.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&');\n};\n// --- 防抖和节流工具 ---\n/**\n * 防抖函数\n */\nconst debounce = (func, delay) => {\n    let timeout;\n    return (...args) => {\n        clearTimeout(timeout);\n        timeout = setTimeout(() => func(...args), delay);\n    };\n};\n/**\n * 节流函数\n */\nconst throttle = (func, limit) => {\n    let inThrottle;\n    return (...args) => {\n        if (!inThrottle) {\n            func.apply(undefined, args);\n            inThrottle = true;\n            setTimeout(() => (inThrottle = false), limit);\n        }\n    };\n};\n// --- ID 生成工具 ---\n/**\n * 创建安全的ID\n */\nconst createSafeId = (name) => {\n    return 'entries-' + btoa(encodeURIComponent(name)).replace(_constants__WEBPACK_IMPORTED_MODULE_0__.REGEX_PATTERNS.SAFE_ID, '');\n};\n/**\n * 生成唯一ID\n */\nconst generateUniqueId = () => {\n    return Date.now().toString(36) + Math.random().toString(36).substr(2);\n};\n// --- 错误处理工具 ---\n/**\n * 错误捕获包装器\n */\nconst errorCatched = (fn, context = 'Unknown') => {\n    return ((...args) => {\n        try {\n            const result = fn(...args);\n            if (result instanceof Promise) {\n                return result.catch((error) => {\n                    console.error(`[WorldInfoOptimizer] Error in ${context}:`, error);\n                    throw error;\n                });\n            }\n            return result;\n        }\n        catch (error) {\n            console.error(`[WorldInfoOptimizer] Error in ${context}:`, error);\n            throw error;\n        }\n    });\n};\n// --- 数组和对象工具 ---\n/**\n * 深拷贝对象\n */\nconst deepClone = (obj) => {\n    if (obj === null || typeof obj !== 'object')\n        return obj;\n    if (obj instanceof Date)\n        return new Date(obj.getTime());\n    if (obj instanceof Array)\n        return obj.map(item => deepClone(item));\n    if (typeof obj === 'object') {\n        const clonedObj = {};\n        for (const key in obj) {\n            if (obj.hasOwnProperty(key)) {\n                clonedObj[key] = deepClone(obj[key]);\n            }\n        }\n        return clonedObj;\n    }\n    return obj;\n};\n/**\n * 检查对象是否为空\n */\nconst isEmpty = (obj) => {\n    if (obj == null)\n        return true;\n    if (Array.isArray(obj) || typeof obj === 'string')\n        return obj.length === 0;\n    if (obj instanceof Map || obj instanceof Set)\n        return obj.size === 0;\n    return Object.keys(obj).length === 0;\n};\n// --- 字符串工具 ---\n/**\n * 截断文本\n */\nconst truncateText = (text, maxLength, suffix = '...') => {\n    if (!text || text.length <= maxLength)\n        return text;\n    return text.substring(0, maxLength - suffix.length) + suffix;\n};\n/**\n * 首字母大写\n */\nconst capitalize = (str) => {\n    if (!str)\n        return str;\n    return str.charAt(0).toUpperCase() + str.slice(1);\n};\n// --- 类型检查工具 ---\n/**\n * 检查是否为有效的字符串\n */\nconst isValidString = (value) => {\n    return typeof value === 'string' && value.trim().length > 0;\n};\n/**\n * 检查是否为数字\n */\nconst isNumber = (value) => {\n    return typeof value === 'number' && !isNaN(value);\n};\n/**\n * 检查是否为有效的数组\n */\nconst isValidArray = (value) => {\n    return Array.isArray(value) && value.length > 0;\n};\n// --- 时间工具 ---\n/**\n * 格式化时间戳\n */\nconst formatTimestamp = (timestamp) => {\n    const date = new Date(timestamp);\n    return date.toLocaleString('zh-CN');\n};\n/**\n * 获取相对时间\n */\nconst getRelativeTime = (timestamp) => {\n    const now = Date.now();\n    const diff = now - timestamp;\n    const seconds = Math.floor(diff / 1000);\n    const minutes = Math.floor(seconds / 60);\n    const hours = Math.floor(minutes / 60);\n    const days = Math.floor(hours / 24);\n    if (days > 0)\n        return `${days}天前`;\n    if (hours > 0)\n        return `${hours}小时前`;\n    if (minutes > 0)\n        return `${minutes}分钟前`;\n    return '刚刚';\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/WorldInfoOptimizer/utils.ts\n\n}");
  }
};

var __webpack_module_cache__ = {};

function __webpack_require__(moduleId) {
  var cachedModule = __webpack_module_cache__[moduleId];
  if (cachedModule !== undefined) {
    return cachedModule.exports;
  }
  var module = __webpack_module_cache__[moduleId] = {
    exports: {}
  };
  __webpack_modules__[moduleId](module, module.exports, __webpack_require__);
  return module.exports;
}

(() => {
  __webpack_require__.d = (exports, definition) => {
    for (var key in definition) {
      if (__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {
        Object.defineProperty(exports, key, {
          enumerable: true,
          get: definition[key]
        });
      }
    }
  };
})();

(() => {
  __webpack_require__.o = (obj, prop) => Object.prototype.hasOwnProperty.call(obj, prop);
})();

(() => {
  __webpack_require__.r = exports => {
    if (typeof Symbol !== "undefined" && Symbol.toStringTag) {
      Object.defineProperty(exports, Symbol.toStringTag, {
        value: "Module"
      });
    }
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
  };
})();

var __webpack_exports__ = __webpack_require__("./src/WorldInfoOptimizer/index.ts");