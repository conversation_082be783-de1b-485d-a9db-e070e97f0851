/**
 * 世界书优化器状态管理
 * 包含 appState 定义和所有状态操作函数（safe* 系列函数）
 */

import { DEFAULT_SEARCH_FILTERS, DEFAULT_TAB } from './constants';
import type { AppState, InitialDataPayload, LorebookEntry } from './types';

// --- 应用程序状态 ---
export const appState: AppState = {
  globalRegex: [],
  characterRegex: [],
  lorebooks: { character: [] },
  chatLorebook: null,
  allLorebooks: [],
  lorebookEntries: new Map(),
  lorebookUsage: new Map(),
  activeTab: DEFAULT_TAB,
  isDataLoaded: false,
  searchFilters: { ...DEFAULT_SEARCH_FILTERS },
  multiSelectMode: false,
  selectedItems: new Set(),
};

// --- 安全访问 lorebookEntries 的函数 ---

/**
 * 安全获取世界书条目
 */
export const safeGetLorebookEntries = (bookName: string): LorebookEntry[] => {
  try {
    if (!appState.lorebookEntries || !(appState.lorebookEntries instanceof Map)) {
      console.warn('[WorldInfoOptimizer] appState.lorebookEntries is not a Map, reinitializing...');
      appState.lorebookEntries = new Map();
    }

    const entries = appState.lorebookEntries.get(bookName);
    if (!entries) {
      console.log(`[WorldInfoOptimizer] No entries found for book: ${bookName}`);
      return [];
    }

    if (!Array.isArray(entries)) {
      console.warn(`[WorldInfoOptimizer] Entries for book ${bookName} is not an array, returning empty array`);
      return [];
    }

    return entries;
  } catch (error) {
    console.error('[WorldInfoOptimizer] Error in safeGetLorebookEntries:', error);
    return [];
  }
};

/**
 * 安全设置世界书条目
 */
export const safeSetLorebookEntries = (bookName: string, entries: LorebookEntry[]): void => {
  try {
    if (!appState.lorebookEntries || !(appState.lorebookEntries instanceof Map)) {
      console.warn('[WorldInfoOptimizer] appState.lorebookEntries is not a Map, reinitializing...');
      appState.lorebookEntries = new Map();
    }

    if (!Array.isArray(entries)) {
      console.warn(`[WorldInfoOptimizer] Entries for book ${bookName} is not an array, converting...`);
      appState.lorebookEntries.set(bookName, []);
      return;
    }

    appState.lorebookEntries.set(bookName, entries);
    console.log(`[WorldInfoOptimizer] Set ${entries.length} entries for book: ${bookName}`);
  } catch (error) {
    console.error('[WorldInfoOptimizer] Error in safeSetLorebookEntries:', error);
  }
};

/**
 * 安全删除世界书条目
 */
export const safeDeleteLorebookEntries = (bookName: string): void => {
  try {
    if (!appState.lorebookEntries || !(appState.lorebookEntries instanceof Map)) {
      console.warn('[WorldInfoOptimizer] appState.lorebookEntries is not a Map, reinitializing...');
      appState.lorebookEntries = new Map();
      return;
    }

    const deleted = appState.lorebookEntries.delete(bookName);
    if (deleted) {
      console.log(`[WorldInfoOptimizer] Deleted entries for book: ${bookName}`);
    } else {
      console.log(`[WorldInfoOptimizer] No entries found to delete for book: ${bookName}`);
    }
  } catch (error) {
    console.error('[WorldInfoOptimizer] Error in safeDeleteLorebookEntries:', error);
  }
};

/**
 * 安全清空所有世界书条目
 */
export const safeClearLorebookEntries = (): void => {
  try {
    if (!appState.lorebookEntries || !(appState.lorebookEntries instanceof Map)) {
      console.warn('[WorldInfoOptimizer] appState.lorebookEntries is not a Map, reinitializing...');
      appState.lorebookEntries = new Map();
      return;
    }

    const size = appState.lorebookEntries.size;
    appState.lorebookEntries.clear();
    console.log(`[WorldInfoOptimizer] Cleared ${size} lorebook entries from state`);
  } catch (error) {
    console.error('[WorldInfoOptimizer] Error in safeClearLorebookEntries:', error);
    // 强制重新初始化
    appState.lorebookEntries = new Map();
  }
};

/**
 * 安全检查世界书是否有条目
 */
export const safeHasLorebookEntries = (bookName: string): boolean => {
  try {
    if (!appState.lorebookEntries || !(appState.lorebookEntries instanceof Map)) {
      console.warn('[WorldInfoOptimizer] appState.lorebookEntries is not a Map, reinitializing...');
      appState.lorebookEntries = new Map();
      return false;
    }

    return appState.lorebookEntries.has(bookName);
  } catch (error) {
    console.error('[WorldInfoOptimizer] Error in safeHasLorebookEntries:', error);
    return false;
  }
};

// --- 状态更新函数 ---

/**
 * 设置数据加载状态
 */
export const setDataLoaded = (loaded: boolean): void => {
  appState.isDataLoaded = loaded;
};

/**
 * 设置活动标签页
 */
export const setActiveTab = (tab: string): void => {
  appState.activeTab = tab;
};

/**
 * 更新角色正则表达式
 */
export const updateCharacterRegexes = (allUIRegexes: any[], charData: any): void => {
  const characterUIRegexes = Array.isArray(allUIRegexes)
    ? allUIRegexes.filter((r: any) => r.scope === 'character')
    : [];

  // 从角色卡片获取正则表达式
  const cardRegexes = charData?.data?.extensions?.regex_scripts || [];
  
  // 创建一个Set来跟踪UI中已有的正则表达式
  const uiRegexIdentifiers = new Set(
    characterUIRegexes.map((r: any) => `${r.script_name}::${r.find_regex}::${r.replace_string}`)
  );
  
  // 过滤出卡片中独有的正则表达式
  const uniqueCardRegexes = cardRegexes.filter((r: any) => {
    const identifier = `${r.script_name}::${r.find_regex}::${r.replace_string}`;
    return !uiRegexIdentifiers.has(identifier);
  });
  
  appState.characterRegex = [...characterUIRegexes, ...uniqueCardRegexes];
};

// --- 多选模式管理 ---

/**
 * 切换多选模式
 */
export const toggleMultiSelectMode = (): boolean => {
  appState.multiSelectMode = !appState.multiSelectMode;
  if (!appState.multiSelectMode) {
    clearSelectedItems();
  }
  return appState.multiSelectMode;
};

/**
 * 切换选中项目
 */
export const toggleSelectedItem = (itemKey: string): boolean => {
  if (appState.selectedItems.has(itemKey)) {
    appState.selectedItems.delete(itemKey);
    return false;
  } else {
    appState.selectedItems.add(itemKey);
    return true;
  }
};

/**
 * 清空选中项目
 */
export const clearSelectedItems = (): void => {
  appState.selectedItems.clear();
};

/**
 * 添加选中项目
 */
export const addSelectedItem = (itemKey: string): void => {
  appState.selectedItems.add(itemKey);
};

/**
 * 移除选中项目
 */
export const removeSelectedItem = (itemKey: string): void => {
  appState.selectedItems.delete(itemKey);
};

/**
 * 检查项目是否被选中
 */
export const isItemSelected = (itemKey: string): boolean => {
  return appState.selectedItems.has(itemKey);
};

// --- 搜索过滤器管理 ---

/**
 * 更新搜索过滤器
 */
export const updateSearchFilter = (filterName: keyof typeof appState.searchFilters, enabled: boolean): void => {
  appState.searchFilters[filterName] = enabled;
};

/**
 * 重置搜索过滤器
 */
export const resetSearchFilters = (): void => {
  appState.searchFilters = { ...DEFAULT_SEARCH_FILTERS };
};

// --- 世界书管理 ---

/**
 * 添加世界书到状态
 */
export const addLorebookToState = (bookName: string, enabled: boolean = true): void => {
  const existingBook = appState.allLorebooks.find(book => book.name === bookName);
  if (!existingBook) {
    appState.allLorebooks.push({ name: bookName, enabled });
  }
};

/**
 * 从状态中移除世界书
 */
export const removeLorebookFromState = (bookName: string): void => {
  const index = appState.allLorebooks.findIndex(book => book.name === bookName);
  if (index !== -1) {
    appState.allLorebooks.splice(index, 1);
  }
  safeDeleteLorebookEntries(bookName);
};

/**
 * 更新世界书状态
 */
export const updateLorebookState = (bookName: string, enabled: boolean): void => {
  const book = appState.allLorebooks.find(book => book.name === bookName);
  if (book) {
    book.enabled = enabled;
  }
};

/**
 * 使用初始数据负载填充应用程序状态
 */
export const hydrateAppState = (data: InitialDataPayload): void => {
  appState.globalRegex = data.globalRegex ?? [];
  appState.characterRegex = data.characterRegex ?? [];
  appState.allLorebooks = data.allLorebooks;
  appState.lorebooks.character = data.characterLorebooks;
  appState.chatLorebook = data.chatLorebook;
  appState.lorebookEntries = data.lorebookEntries;
  setDataLoaded(true);
};

/**
 * 重命名世界书
 */
export const renameLorebookInState = (oldName: string, newName: string): void => {
  const book = appState.allLorebooks.find(book => book.name === oldName);
  if (book) {
    book.name = newName;
  }
  
  // 更新条目映射
  const entries = safeGetLorebookEntries(oldName);
  if (entries.length > 0) {
    safeSetLorebookEntries(newName, entries);
    safeDeleteLorebookEntries(oldName);
  }
};

