/**
 * 世界书优化器类型定义
 * 包含所有接口定义和类型声明
 */

// --- 核心数据结构 ---
export interface LorebookEntry {
  uid: string;
  comment: string;
  content: string;
  keys: string[];
  enabled: boolean;
  display_index: number;
  [key: string]: any;
}

export interface AppState {
  globalRegex: TavernRegex[];
  characterRegex: TavernRegex[];
  lorebooks: {
    character: string[];
  };
  chatLorebook: string | null;
  allLorebooks: Array<{ name: string; enabled: boolean }>;
  lorebookEntries: Map<string, LorebookEntry[]>;
  lorebookUsage: Map<string, string[]>;
  activeTab: string;
  isDataLoaded: boolean;
  searchFilters: {
    bookName: boolean;
    entryName: boolean;
    keywords: boolean;
    content: boolean;
  };
  multiSelectMode: boolean;
  selectedItems: Set<string>;
}

export interface TavernRegex {
  scope: 'global' | 'character';
  pattern: string;
  replacement: string;
  enabled: boolean;
  [key: string]: any;
}

export interface Lorebook {
  name: string;
  enabled: boolean;
}

export interface InitialDataPayload {
  globalRegex: TavernRegex[];
  characterRegex: TavernRegex[];
  allLorebooks: Lorebook[];
  characterLorebooks: string[];
  chatLorebook: string | null;
  lorebookEntries: Map<string, LorebookEntry[]>;
}

// --- 模态框选项 ---
export interface ModalOptions {
  type?: 'alert' | 'confirm' | 'prompt';
  title?: string;
  text?: string;
  placeholder?: string;
  value?: string;
}

// --- 进度提示器接口 ---
export interface ProgressToast {
  update: (message: string) => void;
  remove: () => void;
}

// --- TavernHelper API 接口 ---
export interface TavernHelperAPI {
  createLorebook: (name: string) => Promise<any>;
  deleteLorebook: (name: string) => Promise<any>;
  getLorebooks: () => Promise<any>;
  setLorebookSettings: (settings: any) => Promise<any>;
  getCharData: () => Promise<any>;
  Character: any;
  getTavernRegexes: (options: any) => Promise<any>;
  replaceTavernRegexes: (regexes: any, options: any) => Promise<any>;
  createLorebookEntries: (bookName: string, entries: any[]) => Promise<any>;
  setLorebookEntries: (bookName: string, entries: any[]) => Promise<any>;
  getLorebookEntries: (bookName: string) => Promise<any>;
  deleteLorebookEntry: (bookName: string, uid: number) => Promise<any>;
  getLorebookSettings: () => Promise<any>;
  getCurrentCharLorebooks: () => Promise<any>;
  getChatLorebook: () => Promise<any>;
}

// --- 全局依赖接口 ---
export interface GlobalDependencies {
  jquery: any;
  parentWindow: any;
  tavernHelper: TavernHelperAPI;
}

// --- 事件处理器类型 ---
export type EventHandler = (event: any) => Promise<void> | void;

// --- 搜索匹配项接口 ---
export interface SearchMatch {
  type: 'book' | 'entry' | 'regex';
  bookName?: string;
  item: any;
  matchFields: string[];
}

// --- 批量操作结果 ---
export interface BatchOperationResult {
  success: number;
  failed: number;
  errors: string[];
}

// --- 拖拽事件接口 ---
export interface DragEvent {
  oldIndex: number;
  newIndex: number;
  item: HTMLElement;
}

// --- 世界书选项配置 ---
export interface LorebookOptions {
  position: Record<string, string>;
  logic: Record<string, string>;
}

// --- 过滤后的世界书数据 ---
export interface FilteredLorebookData {
  book: { name: string; enabled: boolean };
  entries: LorebookEntry[];
  shouldExpand: boolean;
}

// --- 项目类型枚举 ---
export enum ItemType {
  BOOK = 'book',
  ENTRY = 'entry',
  REGEX = 'regex',
}

// --- 标签页类型 ---
export type TabType = 'global-lore' | 'char-lore' | 'chat-lore' | 'global-regex' | 'char-regex';

// --- 项目键管理的复合ID类型 ---
export interface IBookEntryId {
  type: 'book' | 'entry' | 'regex';
  bookName?: string;
  entryId?: string;
  scope?: string;
  regexId?: string;
}

// --- 错误处理包装器类型 ---
export type ErrorCatchedFunction<T extends (...args: any[]) => any> = T;
