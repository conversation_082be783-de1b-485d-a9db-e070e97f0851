import * as __WEBPACK_EXTERNAL_MODULE_https_testingcf_jsdelivr_net_npm_dedent_esm_422736dc__ from "https://testingcf.jsdelivr.net/npm/dedent/+esm";

var __webpack_modules__ = {
  "./src/脚本示例/index.ts": 
  /*!***************************!*\
  !*** ./src/脚本示例/index.ts ***!
  \***************************/ (__unused_webpack_module, __webpack_exports__, __webpack_require__) => {
    eval('{__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var ___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./加载和卸载时执行函数 */ "./src/脚本示例/加载和卸载时执行函数.ts");\n/* harmony import */ var ___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(___WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var ___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./添加按钮和注册按钮事件 */ "./src/脚本示例/添加按钮和注册按钮事件.ts");\n/* harmony import */ var ___WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(___WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var ___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./监听消息修改 */ "./src/脚本示例/监听消息修改.ts");\n/* harmony import */ var ___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var ___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./调整消息楼层 */ "./src/脚本示例/调整消息楼层.ts");\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMv6ISa5pys56S65L6LL2luZGV4LnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQXNCO0FBQ0M7QUFDTDtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vdGF2ZXJuX2hlbHBlcl90ZW1wbGF0ZS8uL3NyYy/ohJrmnKznpLrkvosvaW5kZXgudHM/Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCAnLi/liqDovb3lkozljbjovb3ml7bmiafooYzlh73mlbAnO1xuaW1wb3J0ICcuL+a3u+WKoOaMiemSruWSjOazqOWGjOaMiemSruS6i+S7tic7XG5pbXBvcnQgJy4v55uR5ZCs5raI5oGv5L+u5pS5JztcbmltcG9ydCAnLi/osIPmlbTmtojmga/mpbzlsYInO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./src/脚本示例/index.ts\n\n}');
  },
  "./src/脚本示例/加载和卸载时执行函数.ts": 
  /*!********************************!*\
  !*** ./src/脚本示例/加载和卸载时执行函数.ts ***!
  \********************************/ () => {
    eval("{\n// 在加载脚本时执行某个函数\n$(() => {\n    toastr.success('你已经成功加载示例脚本!', '恭喜你!');\n});\n// 在卸载脚本时执行某个函数\n$(window).on('pagehide', () => {\n    toastr.info('你已经卸载示例脚本!', '再见!');\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMv6ISa5pys56S65L6LL+WKoOi9veWSjOWNuOi9veaXtuaJp+ihjOWHveaVsC50cyIsIm1hcHBpbmdzIjoiO0FBQUEsZUFBZTtBQUNmLENBQUMsQ0FBQyxHQUFHLEVBQUU7SUFDTCxNQUFNLENBQUMsT0FBTyxDQUFDLGNBQWMsRUFBRSxNQUFNLENBQUMsQ0FBQztBQUN6QyxDQUFDLENBQUMsQ0FBQztBQUVILGVBQWU7QUFDZixDQUFDLENBQUMsTUFBTSxDQUFDLENBQUMsRUFBRSxDQUFDLFVBQVUsRUFBRSxHQUFHLEVBQUU7SUFDNUIsTUFBTSxDQUFDLElBQUksQ0FBQyxZQUFZLEVBQUUsS0FBSyxDQUFDLENBQUM7QUFDbkMsQ0FBQyxDQUFDLENBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly90YXZlcm5faGVscGVyX3RlbXBsYXRlLy4vc3JjL+iEmuacrOekuuS+iy/liqDovb3lkozljbjovb3ml7bmiafooYzlh73mlbAudHM/Il0sInNvdXJjZXNDb250ZW50IjpbIi8vIOWcqOWKoOi9veiEmuacrOaXtuaJp+ihjOafkOS4quWHveaVsFxuJCgoKSA9PiB7XG4gIHRvYXN0ci5zdWNjZXNzKCfkvaDlt7Lnu4/miJDlip/liqDovb3npLrkvovohJrmnKwhJywgJ+aBreWWnOS9oCEnKTtcbn0pO1xuXG4vLyDlnKjljbjovb3ohJrmnKzml7bmiafooYzmn5DkuKrlh73mlbBcbiQod2luZG93KS5vbigncGFnZWhpZGUnLCAoKSA9PiB7XG4gIHRvYXN0ci5pbmZvKCfkvaDlt7Lnu4/ljbjovb3npLrkvovohJrmnKwhJywgJ+WGjeingSEnKTtcbn0pO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./src/脚本示例/加载和卸载时执行函数.ts\n\n}");
  },
  "./src/脚本示例/添加按钮和注册按钮事件.ts": 
  /*!*********************************!*\
  !*** ./src/脚本示例/添加按钮和注册按钮事件.ts ***!
  \*********************************/ () => {
    eval("{\n$(() => {\n    replaceScriptButtons([{ name: '晚上好', visible: true }]);\n    eventOn(getButtonEvent('晚上好'), () => {\n        toastr.warning('晚安, 络络');\n    });\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMv6ISa5pys56S65L6LL+a3u+WKoOaMiemSruWSjOazqOWGjOaMiemSruS6i+S7ti50cyIsIm1hcHBpbmdzIjoiO0FBQUEsQ0FBQyxDQUFDLEdBQUcsRUFBRTtJQUNMLG9CQUFvQixDQUFDLENBQUMsRUFBRSxJQUFJLEVBQUUsS0FBSyxFQUFFLE9BQU8sRUFBRSxJQUFJLEVBQUUsQ0FBQyxDQUFDLENBQUM7SUFFdkQsT0FBTyxDQUFDLGNBQWMsQ0FBQyxLQUFLLENBQUMsRUFBRSxHQUFHLEVBQUU7UUFDbEMsTUFBTSxDQUFDLE9BQU8sQ0FBQyxRQUFRLENBQUMsQ0FBQztJQUMzQixDQUFDLENBQUMsQ0FBQztBQUNMLENBQUMsQ0FBQyxDQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vdGF2ZXJuX2hlbHBlcl90ZW1wbGF0ZS8uL3NyYy/ohJrmnKznpLrkvosv5re75Yqg5oyJ6ZKu5ZKM5rOo5YaM5oyJ6ZKu5LqL5Lu2LnRzPyJdLCJzb3VyY2VzQ29udGVudCI6WyIkKCgpID0+IHtcbiAgcmVwbGFjZVNjcmlwdEJ1dHRvbnMoW3sgbmFtZTogJ+aZmuS4iuWlvScsIHZpc2libGU6IHRydWUgfV0pO1xuXG4gIGV2ZW50T24oZ2V0QnV0dG9uRXZlbnQoJ+aZmuS4iuWlvScpLCAoKSA9PiB7XG4gICAgdG9hc3RyLndhcm5pbmcoJ+aZmuWuiSwg57uc57ucJyk7XG4gIH0pO1xufSk7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./src/脚本示例/添加按钮和注册按钮事件.ts\n\n}");
  },
  "./src/脚本示例/监听消息修改.ts": 
  /*!****************************!*\
  !*** ./src/脚本示例/监听消息修改.ts ***!
  \****************************/ () => {
    eval("{\neventOn(tavern_events.MESSAGE_UPDATED, (message_id) => {\n    toastr.error(`谁让你动我第 ${message_id} 楼消息的😡`, `干什么!`);\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMv6ISa5pys56S65L6LL+ebkeWQrOa2iOaBr+S/ruaUuS50cyIsIm1hcHBpbmdzIjoiO0FBQUEsT0FBTyxDQUFDLGFBQWEsQ0FBQyxlQUFlLEVBQUUsQ0FBQyxVQUFrQixFQUFFLEVBQUU7SUFDNUQsTUFBTSxDQUFDLEtBQUssQ0FBQyxVQUFVLFVBQVUsU0FBUyxFQUFFLE1BQU0sQ0FBQyxDQUFDO0FBQ3RELENBQUMsQ0FBQyxDQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vdGF2ZXJuX2hlbHBlcl90ZW1wbGF0ZS8uL3NyYy/ohJrmnKznpLrkvosv55uR5ZCs5raI5oGv5L+u5pS5LnRzPyJdLCJzb3VyY2VzQ29udGVudCI6WyJldmVudE9uKHRhdmVybl9ldmVudHMuTUVTU0FHRV9VUERBVEVELCAobWVzc2FnZV9pZDogbnVtYmVyKSA9PiB7XG4gIHRvYXN0ci5lcnJvcihg6LCB6K6p5L2g5Yqo5oiR56ysICR7bWVzc2FnZV9pZH0g5qW85raI5oGv55qE8J+YoWAsIGDlubLku4DkuYghYCk7XG59KTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./src/脚本示例/监听消息修改.ts\n\n}");
  },
  "./src/脚本示例/调整消息楼层.ts": 
  /*!****************************!*\
  !*** ./src/脚本示例/调整消息楼层.ts ***!
  \****************************/ (__unused_webpack_module, __webpack_exports__, __webpack_require__) => {
    eval("{__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var dedent__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! dedent */ \"dedent\");\n\n$(async () => {\n    const message_id = getLastMessageId();\n    if (message_id !== 0) {\n        return;\n    }\n    await createChatMessages([\n        {\n            role: 'assistant',\n            message: (0,dedent__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(`\n                   [查看日记: 这是第二次看我的日记了呢~]\n                   <roleplay_options>\n                   接受日记本并翻阅: 青空黎接过她递来的粉色日记本，在天台阳光下缓缓翻开第一页\n                   保持沉默盯着她看: 青空黎没有接本子，只是盯着她略显紧张的表情和轻颤的声音\n                   坐到她身边: 青空黎没有立刻回应，而是缓缓走到络络身边坐下，等待她自己继续说\n                   开玩笑化解气氛: 青空黎微微一笑，开玩笑地说「所以，是要让我当监督官啦？」\n                   跳过时间: 青空黎接过日记本，安静地翻了几页，时间悄然流逝至黄昏深处\n                   和络络聊天: 青空黎试探性地问：「这本是从哪天开始写的？都写些什么呀？」\n                   </roleplay_options>\n                 `),\n        },\n        {\n            role: 'assistant',\n            message: (0,dedent__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(`\n                   [查看日记: 真是的, 就这么喜欢看吗(v〃ω〃)]\n                   <roleplay_options>\n                   阅读日记第一页：青空黎打开粉色的日记本，从第一页开始认真阅读络络的记录内容。\n                   问她封面上的兔子贴纸：青空黎好奇那枚蓝色兔子贴纸的来历，转头向络络询问。\n                   观察络络的小动作：青空黎不急着翻开日记，而是注意到络络表情里一丝期待与不安。\n                   和她闲聊天气：青空黎随口聊起傍晚风有点凉，试图舒缓紧张气氛。\n                   调侃她：“有哪页是‘不许看’的？”青空黎轻松地试图化解她的小慌乱。\n                   转身回教室：青空黎接过日记却并未立刻翻开，而是表示回教室再看，打算慢慢阅读。\n                   </roleplay_options>\n                 `),\n        },\n    ], { refresh: 'all' });\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/脚本示例/调整消息楼层.ts\n\n}");
  },
  dedent: 
  /*!*****************************************************************!*\
  !*** external "https://testingcf.jsdelivr.net/npm/dedent/+esm" ***!
  \*****************************************************************/ module => {
    module.exports = __WEBPACK_EXTERNAL_MODULE_https_testingcf_jsdelivr_net_npm_dedent_esm_422736dc__;
  }
};

var __webpack_module_cache__ = {};

function __webpack_require__(moduleId) {
  var cachedModule = __webpack_module_cache__[moduleId];
  if (cachedModule !== undefined) {
    return cachedModule.exports;
  }
  var module = __webpack_module_cache__[moduleId] = {
    exports: {}
  };
  __webpack_modules__[moduleId](module, module.exports, __webpack_require__);
  return module.exports;
}

(() => {
  __webpack_require__.n = module => {
    var getter = module && module.__esModule ? () => module["default"] : () => module;
    __webpack_require__.d(getter, {
      a: getter
    });
    return getter;
  };
})();

(() => {
  __webpack_require__.d = (exports, definition) => {
    for (var key in definition) {
      if (__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {
        Object.defineProperty(exports, key, {
          enumerable: true,
          get: definition[key]
        });
      }
    }
  };
})();

(() => {
  __webpack_require__.o = (obj, prop) => Object.prototype.hasOwnProperty.call(obj, prop);
})();

(() => {
  __webpack_require__.r = exports => {
    if (typeof Symbol !== "undefined" && Symbol.toStringTag) {
      Object.defineProperty(exports, Symbol.toStringTag, {
        value: "Module"
      });
    }
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
  };
})();

var __webpack_exports__ = __webpack_require__("./src/脚本示例/index.ts");