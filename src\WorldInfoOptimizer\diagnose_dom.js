/**
 * SillyTavern DOM 结构诊断脚本
 *
 * 目的：在SillyTavern运行时环境中，主动搜索并定位所有可能用于挂载UI的元素，
 * 特别是与“扩展”或“菜单”相关的元素。
 *
 * 如何使用:
 * 1. 在浏览器中打开 SillyTavern 界面。
 * 2. 打开浏览器的开发者工具 (通常按 F12)。
 * 3. 切换到 "Console" (控制台) 标签页。
 * 4. 将此脚本的全部内容复制并粘贴到控制台中。
 * 5. 按 Enter 键执行。
 * 6. 脚本将输出一个名为 `WIO_DIAGNOSTICS` 的可折叠日志组。
 * 7. 请展开这个日志组，并将所有内容截图或复制文本，提供给开发者进行分析。
 */
(() => {
  console.group('WIO_DIAGNOSTICS: SillyTavern DOM Structure Investigation');
  console.log(`诊断开始于 ${new Date().toISOString()}`);

  const keywords = ['extension', 'ext', 'plugin', 'menu', 'sidebar', 'panel', 'drawer'];
  const results = [];
  const processedWindows = new Set();

  function getElementPath(el) {
    if (!el || !(el instanceof Element)) return 'N/A';
    const path = [];
    while (el.nodeType === Node.ELEMENT_NODE) {
      let selector = el.nodeName.toLowerCase();
      if (el.id) {
        selector += `#${el.id}`;
        path.unshift(selector);
        break; // ID is unique, no need to go further
      } else {
        let sib = el,
          nth = 1;
        while (sib.previousElementSibling) {
          sib = sib.previousElementSibling;
          if (sib.nodeName.toLowerCase() === selector) nth++;
        }
        if (nth !== 1) selector += `:nth-of-type(${nth})`;
      }
      path.unshift(selector);
      el = el.parentNode;
    }
    return path.join(' > ');
  }

  function searchInDocument(doc, context) {
    console.log(`--- 正在搜索文档: ${context} ---`);
    const selectors = [];
    keywords.forEach(kw => {
      selectors.push(`[id*="${kw}"]`);
      selectors.push(`[class*="${kw}"]`);
    });

    try {
      const elements = doc.querySelectorAll(selectors.join(', '));
      console.log(`在 ${context} 中找到 ${elements.length} 个潜在元素。`);

      elements.forEach(el => {
        results.push({
          context: context,
          tagName: el.tagName,
          id: el.id || 'N/A',
          className: el.className || 'N/A',
          path: getElementPath(el),
          html: el.outerHTML.substring(0, 150) + '...', // First 150 chars
        });
      });
    } catch (e) {
      console.error(`在 ${context} 中搜索时出错:`, e);
    }
  }

  function traverseFrames(win, context) {
    if (processedWindows.has(win)) {
      return;
    }
    processedWindows.add(win);

    try {
      searchInDocument(win.document, context);

      // 搜索iframes
      const iframes = win.document.querySelectorAll('iframe');
      if (iframes.length > 0) {
        console.log(`在 ${context} 中找到 ${iframes.length} 个 iframe，尝试深入...`);
      }
      iframes.forEach((iframe, index) => {
        try {
          if (iframe.contentWindow) {
            traverseFrames(iframe.contentWindow, `${context} > iframe[${index}]`);
          }
        } catch (e) {
          console.warn(`无法访问 iframe ${index} 的内容，可能是跨域限制:`, context);
        }
      });
    } catch (e) {
      console.error(`遍历 ${context} 时出错:`, e);
    }
  }

  console.log('开始从顶层窗口 (top) 遍历...');
  traverseFrames(window.top, 'top');

  if (results.length > 0) {
    console.log('--- 诊断结果 ---');
    console.table(results);
    console.info('请将上面的表格内容展开并截图，或复制整个日志组，然后发送给开发者。');
  } else {
    console.warn('--- 诊断结果 ---');
    console.warn('没有找到任何包含指定关键字的元素。这可能意味着：');
    console.warn('1. 挂载点使用了完全不同的命名约定。');
    console.warn('2. 元素是动态生成的，在脚本运行时还不存在。');
    console.warn('3. 元素位于无法访问的 Shadow DOM 中。');
  }

  console.groupEnd();
})();
