name: bump_deps

on:
  schedule:
    - cron: 0 0 * * *
  workflow_dispatch:

permissions:
  actions: read
  contents: write

jobs:
  bump_deps:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - name: Use Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 22
      - name: Setup pnpm
        uses: pnpm/action-setup@v4.1.0
        with:
          version: 10

      - run: |-
          pnpm update
          pnpm install --lockfile-only

      - run: |-
          rm -rf @types
          mkdir -p @types
          wget https://gitlab.com/novi028/JS-Slash-Runner/-/raw/main/dist/@types.zip -O @types.zip
          tar -xf @types.zip -C @types
          rm -f @types.zip

      - name: Commit changes
        uses: EndBug/add-and-commit@v9.1.3
        with:
          default_author: github_actions
          message: '[bot] bump deps'
