// ==UserScript==
// @name         高级信息栏设置助手 (v10.0.29 - 超级手术刀)
// @namespace    SillyTavern.AdvancedInfoBar.EngineV10_0_29
// @match        */*
// @version      10.0.29
// @description  【最终优化版】1. 升级“格式化手术刀”，能处理中文逗号和更复杂的裸字符串数组。2. 升级“渲染器”，对AI未提供的资讯子项显示占位符，确保视觉完整性并反向激励AI。
// <AUTHOR> & AI Assistant
// @grant        GM_setValue
// @grant        GM_getValue
// @grant        GM_deleteValue
// @inject-into  content
// @require      https://code.jquery.com/jquery-3.7.1.min.js
// ==/UserScript==

'use strict';
/* global SillyTavern, jQuery, $, getContext, getChatMessages, replaceVariables, getVariables, eventOn, tavern_events, retrieveDisplayedMessage, saveChatVariable, loadChatVariable, getLastMessageId, world_info, TavernHelper */

(async function () {
    console.log('[高级信息栏设置助手 v10.0.29 - 超级手术刀] 脚本开始执行...');

    // ------------------- 配置 (版本号更新) -------------------
    const SCRIPT_VERSION_TAG = 'v10_0_29'; // 版本号更新
    const TASKLIST_WI_COMMENT_KEY = '[DO NOT EDIT] Infobar Settings Tasklist';
    const MEMORY_ASSIST_WI_COMMENT_KEY = '[DO NOT EDIT] Infobar Memory Assist Context';
    const TARGET_LOREBOOK_NAME = "信息栏 aplon";

    const BUTTON_ID_SETTINGS = `advanced-infobar-settings-button-${SCRIPT_VERSION_TAG}`;
    const BUTTON_ID_DATATABLE = `advanced-infobar-datatable-button-${SCRIPT_VERSION_TAG}`;
    const POPUP_ID_SETTINGS = `advanced-infobar-settings-popup-${SCRIPT_VERSION_TAG}`;
    const POPUP_ID_DATATABLE = `advanced-infobar-datatable-popup-${SCRIPT_VERSION_TAG}`;
    const STORAGE_KEY_CURRENT_SETTINGS = `advanced_infobar_cot_current_settings_${SCRIPT_VERSION_TAG}`;
    const GLOBAL_VAR_KEY_NAMED_CONFIGS = `infobar_named_configs_global_${SCRIPT_VERSION_TAG}`;
    const CHAT_VAR_KEY_INFOBAR_DATA_HISTORY = `infobar_cot_data_history_${SCRIPT_VERSION_TAG}`;
    const CHAT_VAR_KEY_AI_PROMPT_DEBUG = `infobar_ai_prompt_package_debug_${SCRIPT_VERSION_TAG}`;
    const RENDERED_INFO_BAR_CLASS = `infobar-cot-rendered-container-${SCRIPT_VERSION_TAG}`;
    const MAX_DATA_HISTORY_ENTRIES = 50;
    const NPC_SELECTOR_ID_PREFIX = 'infobar-npc-selector-';
    const NPC_DETAILS_CONTAINER_ID_PREFIX = 'infobar-npc-details-';
    const INTERNET_POST_DETAILS_PREFIX = 'infobar-internet-post-';
    const MAX_RENDERED_NPCS_IN_SELECTOR = 10;
    const MAX_INTERNET_ITEMS_INITIALLY_DISPLAYED = 2;

    const AI_DATA_BLOCK_REGEX = /<infobar_data>([\s\S]*?)<\/infobar_data>/si;
    const AI_THINK_PROCESS_REGEX = /<aiThinkProcess>[\s\S]*?<\/aiThinkProcess>/si;

    let SillyTavern_API, TavernHelper_API, jQuery_API, toastr_API;
    let coreApisAreReady = false;

    let currentSettings = {};
    let currentInfoBarData = { npcs: {} };
    let latestChangeset = new Set();

    let selectedNpcIdForInteractionPanel = null;
    let dataTableInstance = null;

    // 主题和字体配置 (与V10.0.28保持一致)
    const THEMES = {
        现代深色: {
            '--infobar-font-family': 'var(--font_ui, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif)',
            '--infobar-bg': '#282c34',
            '--infobar-text': '#abb2bf',
            '--infobar-border-color': '#4b5263',
            '--infobar-tab-bg': '#21252b',
            '--infobar-tab-active-bg': '#2c313a',
            '--infobar-tab-active-color': '#ffffff',
            '--infobar-tab-hover-bg': '#323842',
            '--infobar-panel-toggle-bg': '#2c313a',
            '--infobar-section-title': '#61afef',
            '--infobar-input-bg': '#21252b',
            '--primary': '#61afef',
            '--infobar-rendered-bg': 'rgba(40, 44, 52, 0.9)',
            '--infobar-rendered-border': '#4b5263',
            '--infobar-rendered-text': '#abb2bf',
            '--infobar-rendered-title-text': '#e5c07b',
            '--infobar-rendered-label': '#98c379',
            '--infobar-rendered-value': '#abb2bf',
            '--infobar-rendered-header-bg': 'rgba(44, 49, 58, 0.95)',
            '--infobar-task-card-bg': 'rgba(50, 55, 65, 0.8)',
            '--infobar-task-progress-bg': '#3a3f4b',
            '--infobar-task-progress-fill': 'var(--primary)',
            '--infobar-data-changed-bg': 'rgba(97, 175, 239, 0.2)',
        },
        浅色: {
            '--infobar-font-family': 'var(--font_ui, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif)',
            '--infobar-bg': '#fafafa',
            '--infobar-text': '#383a42',
            '--infobar-border-color': '#d1d1d1',
            '--infobar-tab-bg': '#eaeaeb',
            '--infobar-tab-active-bg': '#ffffff',
            '--infobar-tab-active-color': '#282a36',
            '--infobar-tab-hover-bg': '#f0f0f0',
            '--infobar-panel-toggle-bg': '#f0f0f0',
            '--infobar-section-title': '#4078f2',
            '--infobar-input-bg': '#ffffff',
            '--primary': '#4078f2',
            '--infobar-rendered-bg': 'rgba(245, 245, 245, 0.9)',
            '--infobar-rendered-border': '#e0e0e0',
            '--infobar-rendered-text': '#383a42',
            '--infobar-rendered-title-text': '#c18401',
            '--infobar-rendered-label': '#50a14f',
            '--infobar-rendered-value': '#383a42',
            '--infobar-rendered-header-bg': 'rgba(230, 230, 230, 0.95)',
            '--infobar-task-card-bg': 'rgba(235, 235, 235, 0.8)',
            '--infobar-task-progress-bg': '#e0e0e0',
            '--infobar-task-progress-fill': 'var(--primary)',
            '--infobar-data-changed-bg': 'rgba(64, 120, 242, 0.15)',
        },
        护眼绿: {
            '--infobar-font-family': 'var(--font_ui, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif)',
            '--infobar-bg': '#f0f8f0',
            '--infobar-text': '#364e36',
            '--infobar-border-color': '#a9c4a9',
            '--infobar-tab-bg': '#e0f0e0',
            '--infobar-tab-active-bg': '#c8e6c9',
            '--infobar-tab-active-color': '#2e442e',
            '--infobar-tab-hover-bg': '#d4e8d4',
            '--infobar-panel-toggle-bg': '#e8f5e9',
            '--infobar-section-title': '#4caf50',
            '--infobar-input-bg': '#f8fff8',
            '--primary': '#66bb6a',
            '--infobar-rendered-bg': 'rgba(240, 248, 240, 0.97)',
            '--infobar-rendered-border': '#a9c4a9',
            '--infobar-rendered-text': '#364e36',
            '--infobar-rendered-title-text': '#388e3c',
            '--infobar-rendered-label': '#4caf50',
            '--infobar-rendered-value': '#364e36',
            '--infobar-rendered-header-bg': 'rgba(200, 230, 201, 0.95)',
            '--infobar-task-card-bg': 'rgba(224, 240, 224, 0.9)',
            '--infobar-task-progress-bg': '#c8e6c9',
            '--infobar-task-progress-fill': 'var(--primary)',
            '--infobar-data-changed-bg': 'rgba(76, 175, 80, 0.15)',
        },
        赛博朋克: {
            '--infobar-font-family': '"Orbitron", "Audiowide", sans-serif',
            '--infobar-bg': '#0a0f21',
            '--infobar-text': '#00f0c0',
            '--infobar-border-color': '#301b49',
            '--infobar-tab-bg': '#101830',
            '--infobar-tab-active-bg': '#203050',
            '--infobar-tab-active-color': '#ff007f',
            '--infobar-tab-hover-bg': '#182440',
            '--infobar-panel-toggle-bg': '#101830',
            '--infobar-section-title': '#ff007f',
            '--infobar-input-bg': '#101830',
            '--primary': '#ff007f',
            '--infobar-rendered-bg': 'rgba(10, 15, 33, 0.92)',
            '--infobar-rendered-border': '#301b49',
            '--infobar-rendered-text': '#00f0c0',
            '--infobar-rendered-title-text': '#f0f000',
            '--infobar-rendered-label': '#ff007f',
            '--infobar-rendered-value': '#00f0c0',
            '--infobar-rendered-header-bg': 'rgba(20, 30, 60, 0.95)',
            '--infobar-task-card-bg': 'rgba(20, 30, 60, 0.85)',
            '--infobar-task-progress-bg': '#152035',
            '--infobar-task-progress-fill': 'var(--primary)',
            '--infobar-data-changed-bg': 'rgba(255, 0, 127, 0.25)',
        },
        蒸汽朋克: {
            '--infobar-font-family': '"IM Fell English SC", "Uncial Antiqua", serif',
            '--infobar-bg': '#4a3b2a',
            '--infobar-text': '#e0d0b0',
            '--infobar-border-color': '#6a503a',
            '--infobar-tab-bg': '#3a2b1a',
            '--infobar-tab-active-bg': '#5a402a',
            '--infobar-tab-active-color': '#f0e0c0',
            '--infobar-tab-hover-bg': '#453525',
            '--infobar-panel-toggle-bg': '#3a2b1a',
            '--infobar-section-title': '#d4a017',
            '--infobar-input-bg': '#3a2b1a',
            '--primary': '#d4a017',
            '--infobar-rendered-bg': 'rgba(74, 59, 42, 0.92)',
            '--infobar-rendered-border': '#6a503a',
            '--infobar-rendered-text': '#e0d0b0',
            '--infobar-rendered-title-text': '#f4d03f',
            '--infobar-rendered-label': '#b8860b',
            '--infobar-rendered-value': '#e0d0b0',
            '--infobar-rendered-header-bg': 'rgba(60, 45, 30, 0.95)',
            '--infobar-task-card-bg': 'rgba(60, 45, 30, 0.85)',
            '--infobar-task-progress-bg': '#504030',
            '--infobar-task-progress-fill': 'var(--primary)',
            '--infobar-data-changed-bg': 'rgba(212, 160, 23, 0.2)',
        },
        羊皮纸魔法: {
            '--infobar-font-family': '"Cinzel Decorative", "MedievalSharp", serif',
            '--infobar-bg': '#f3e9d0',
            '--infobar-text': '#4a3b2a',
            '--infobar-border-color': '#8b6f47',
            '--infobar-tab-bg': '#e8d5a3',
            '--infobar-tab-active-bg': '#d9c092',
            '--infobar-tab-active-color': '#3c2f2f',
            '--infobar-tab-hover-bg': '#e0d0b0',
            '--infobar-panel-toggle-bg': '#e8d5a3',
            '--infobar-section-title': '#5a3f24',
            '--infobar-input-bg': '#f0e0c0',
            '--primary': '#8b6f47',
            '--infobar-rendered-bg': 'rgba(243, 233, 208, 0.92)',
            '--infobar-rendered-border': '#c8b085',
            '--infobar-rendered-text': '#4a3b2a',
            '--infobar-rendered-title-text': '#7a5c37',
            '--infobar-rendered-label': '#8b4513',
            '--infobar-rendered-value': '#4a3b2a',
            '--infobar-rendered-header-bg': 'rgba(230, 215, 180, 0.95)',
            '--infobar-task-card-bg': 'rgba(230, 215, 180, 0.85)',
            '--infobar-task-progress-bg': '#d0c0a0',
            '--infobar-task-progress-fill': 'var(--primary)',
            '--infobar-data-changed-bg': 'rgba(139, 111, 71, 0.15)',
        },
        可爱少女: {
            '--infobar-font-family': '"Comic Sans MS", "Chalkboard SE", "Comic Neue", cursive',
            '--infobar-bg': '#fff0f5',
            '--infobar-text': '#db7093',
            '--infobar-border-color': '#ffb6c1',
            '--infobar-tab-bg': '#ffe4e1',
            '--infobar-tab-active-bg': '#ffc0cb',
            '--infobar-tab-active-color': '#800080',
            '--infobar-tab-hover-bg': '#ffd1dc',
            '--infobar-panel-toggle-bg': '#fff5f8',
            '--infobar-section-title': '#ff1493',
            '--infobar-input-bg': '#fffafa',
            '--primary': '#ff69b4',
            '--infobar-rendered-bg': 'rgba(255, 240, 245, 0.97)',
            '--infobar-rendered-border': '1px solid #ffc0cb',
            '--infobar-rendered-text': '#5e2157',
            '--infobar-rendered-title-text': '#c71585',
            '--infobar-rendered-label': '#db7093',
            '--infobar-rendered-value': '#8a2be2',
            '--infobar-rendered-header-bg': 'rgba(255, 182, 193, 0.95)',
            '--infobar-task-card-bg': 'rgba(255, 228, 225, 0.9)',
            '--infobar-task-progress-bg': '#ffdab9',
            '--infobar-task-progress-fill': 'var(--primary)',
            '--infobar-data-changed-bg': 'rgba(255, 105, 180, 0.2)',
        },
        唯美典雅: {
            '--infobar-font-family': '"Georgia", "Times New Roman", Times, serif',
            '--infobar-bg': '#fdf5e6',
            '--infobar-text': '#695c54',
            '--infobar-border-color': '#d2b48c',
            '--infobar-tab-bg': '#f5f5dc',
            '--infobar-tab-active-bg': '#e0dcd1',
            '--infobar-tab-active-color': '#5d5147',
            '--infobar-tab-hover-bg': '#ece5d8',
            '--infobar-panel-toggle-bg': '#f5f5dc',
            '--infobar-section-title': '#8b7355',
            '--infobar-input-bg': '#fff8dc',
            '--primary': '#b08d57',
            '--infobar-rendered-bg': 'rgba(253, 245, 230, 0.97)',
            '--infobar-rendered-border': '1px solid #d2b48c',
            '--infobar-rendered-text': '#695c54',
            '--infobar-rendered-title-text': '#8b4513',
            '--infobar-rendered-label': '#a0522d',
            '--infobar-rendered-value': '#695c54',
            '--infobar-rendered-header-bg': 'rgba(245, 222, 179, 0.95)',
            '--infobar-task-card-bg': 'rgba(245, 222, 179, 0.9)',
            '--infobar-task-progress-bg': '#e0dcd1',
            '--infobar-task-progress-fill': 'var(--primary)',
            '--infobar-data-changed-bg': 'rgba(176, 141, 87, 0.15)',
        }
    };
    const FONT_OPTIONS = {
        "系统默认": 'var(--font_ui, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif)',
        "宋体 (SimSun)": '"SimSun", "STSong", serif',
        "楷体 (KaiTi)": '"KaiTi", "STKaiti", cursive',
        "圆体 (YuanTi)": '"PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "圆体", sans-serif',
        "无衬线 (Sans-Serif)": 'sans-serif',
        "衬线 (Serif)": 'serif',
        "等宽 (Monospace)": 'monospace',
        "手写风 (Comic Sans MS)": '"Comic Sans MS", "Chalkboard SE", "Comic Neue", cursive',
        "优雅衬线 (Georgia)": 'Georgia, Times, "Times New Roman", serif',
        "赛博朋克 (Orbitron)": '"Orbitron", "Audiowide", sans-serif',
        "魔法羊皮纸 (Cinzel Decorative)": '"Cinzel Decorative", "MedievalSharp", serif',
        "蒸汽朋克 (IM Fell English SC)": '"IM Fell English SC", "Uncial Antiqua", serif'
    };

    // PANEL_CONFIG (与V10.0.26保持一致)
    const PANEL_CONFIG = {
        panels: {
            general: {
                id: 'general',
                label: '基础设置',
                icon: 'fa-cogs',
                description: '信息栏的基础功能与外观设置',
                defaultEnabled: true,
                isUtilityPanel: true,
                items: [
                    { id: 'renderInfoBarInChat', label: '启用信息栏显示', type: 'toggle', defaultValue: true, description: '在AI回复末尾渲染信息栏面板。关闭后仍会在后台记录数据。' },
                    { id: 'enableDataTable', label: '启用数据表格', type: 'toggle', defaultValue: true, description: '在扩展菜单中显示“数据表格”按钮。' },
                    { id: 'memoryAssistEnabled', label: '启用记忆辅助', type: 'toggle', defaultValue: true, description: '将实时数据注入AI上下文，取代旧的剧情摘要。' },
                    { id: 'defaultCollapsed', label: '信息栏默认全部折叠', type: 'toggle', defaultValue: false },
                    { id: 'theme', label: '界面风格', type: 'select', options: Object.keys(THEMES), defaultValue: '现代深色' },
                    { id: 'fontFamily', label: '界面字体', type: 'select', options: Object.keys(FONT_OPTIONS), defaultValue: '系统默认' },
                    { id: 'autoRenderCheckEnabled', label: '启用自动渲染检测', type: 'toggle', defaultValue: true, description: '当AI提供了数据但信息栏未渲染时，发出通知。' },
                ]
            },
            personal: {
                id: 'personal',
                label: '个人信息',
                icon: 'fa-user-circle',
                description: '关于角色自身的基础信息设置',
                defaultEnabled: true,
                items: [
                    { id: 'name', label: '姓名', type: 'toggle', defaultValue: true },
                    { id: 'age', label: '年龄', type: 'toggle', defaultValue: true },
                    { id: 'gender', label: '性别', type: 'toggle', defaultValue: true },
                    { id: 'race', label: '种族/物种', type: 'toggle', defaultValue: true },
                    { id: 'occupation', label: '职业', type: 'toggle', defaultValue: true },
                    { id: 'currentLocation', label: '当前位置', type: 'toggle', defaultValue: true },
                    { id: 'residence', label: '居住地点', type: 'toggle', defaultValue: true },
                    { id: 'thoughts', label: '当前想法', type: 'toggle', defaultValue: true },
                    { id: 'status', label: '身体状态', type: 'toggle', defaultValue: true },
                    { id: 'mood', label: '情绪', type: 'toggle', defaultValue: true },
                    { id: 'funds', label: '个人资金', type: 'toggle', defaultValue: true },
                    { id: 'points', label: '系统积分', type: 'toggle', defaultValue: true },
                    { id: 'appearance', label: '外貌描述', type: 'toggle', defaultValue: true },
                    { id: 'personality', label: '个性', type: 'toggle', defaultValue: true },
                    { id: 'health', label: '健康状态', type: 'toggle', defaultValue: false },
                    { id: 'background', label: '背景故事', type: 'toggle', defaultValue: false },
                    { id: 'bloodType', label: '血型', type: 'toggle', defaultValue: false },
                    { id: 'nativeLanguage', label: '母语', type: 'toggle', defaultValue: false },
                    { id: 'accent', label: '口音', type: 'toggle', defaultValue: false },
                    { id: 'allergies', label: '过敏史', type: 'toggle', defaultValue: false },
                    { id: 'addictions', label: '药物依赖/成瘾', type: 'toggle', defaultValue: false },
                    { id: 'arousalLevel', label: '兴奋度 (敏感内容)', type: 'toggle', defaultValue: false },
                    { id: 'intimacyStatus', label: '亲密状态 (敏感内容)', type: 'toggle', defaultValue: false },
                    { id: 'bodyTemperature', label: '体温状态 (敏感内容)', type: 'toggle', defaultValue: false },
                    { id: 'sensitiveAreas', label: '敏感部位 (敏感内容)', type: 'toggle', defaultValue: false },
                    { id: 'clothingState', label: '衣物状态 (敏感内容)', type: 'toggle', defaultValue: false },
                    { id: 'physicalReaction', label: '生理反应 (敏感内容)', type: 'toggle', defaultValue: false },
                    { id: 'intimateRelations', label: '亲密关系 (敏感内容)', type: 'toggle', defaultValue: false },
                    { id: 'desireLevel', label: '欲望等级 (敏感内容)', type: 'toggle', defaultValue: false },
                    { id: 'sensitivityLevel', label: '敏感度 (敏感内容)', type: 'toggle', defaultValue: false },
                    { id: 'intimatePreferences', label: '亲密偏好 (敏感内容)', type: 'toggle', defaultValue: false }
                ]
            },
            interaction: {
                id: 'interaction',
                label: '交互对象',
                icon: 'fa-users',
                description: '显示当前场景中主要NPC的信息。AI应为每个NPC提供独立数据块。',
                defaultEnabled: true,
                items: [
                    { id: 'name', label: '姓名', type: 'toggle', defaultValue: true },
                    { id: 'age', label: '年龄', type: 'toggle', defaultValue: true },
                    { id: 'gender', label: '性别', type: 'toggle', defaultValue: true },
                    { id: 'isPresent', label: '是否在场', type: 'toggle', defaultValue: true },
                    { id: 'identity', label: '身份/职业', type: 'toggle', defaultValue: true },
                    { id: 'mood', label: '情绪', type: 'toggle', defaultValue: true },
                    { id: 'currentState', label: '当前状态/动作', type: 'toggle', defaultValue: true },
                    { id: 'currentPosture', label: '当前姿势', type: 'toggle', defaultValue: false },
                    { id: 'gazeDirection', label: '视线方向', type: 'toggle', defaultValue: false },
                    { id: 'affection', label: '好感度', type: 'toggle', defaultValue: true },
                    { id: 'relationship', label: '关系', type: 'toggle', defaultValue: true },
                    { id: 'loyalty', label: '忠诚度', type: 'toggle', defaultValue: true },
                    { id: 'thoughts', label: '当前想法', type: 'toggle', defaultValue: true },
                    { id: 'residence', label: '居住地点', type: 'toggle', defaultValue: true },
                    { id: 'emotionalStatus', label: '情感状态', type: 'toggle', defaultValue: true },
                    { id: 'bodyShape', label: '身材', type: 'toggle', defaultValue: true },
                    { id: 'upperBody', label: '上身穿着', type: 'toggle', defaultValue: true },
                    { id: 'lowerBody', label: '下身穿着', type: 'toggle', defaultValue: true },
                    { id: 'underwear', label: '内衣', type: 'toggle', defaultValue: true },
                    { id: 'underpants', label: '内裤', type: 'toggle', defaultValue: true },
                    { id: 'footwear', label: '鞋袜', type: 'toggle', defaultValue: true },
                    { id: 'overallClothing', label: '整体穿着', type: 'toggle', defaultValue: false },
                    { id: 'physicalFeatures', label: '身体特征', type: 'toggle', defaultValue: true },
                    { id: 'specialMarkings', label: '特殊体征', type: 'toggle', defaultValue: false, description: '纹身、疤痕、胎记等' },
                    { id: 'bodyScent', label: '体香', type: 'toggle', defaultValue: false },
                    { id: 'skinTexture', label: '皮肤质感', type: 'toggle', defaultValue: false },
                    { id: 'hobbies', label: '爱好', type: 'toggle', defaultValue: true },
                    { id: 'shameLevel', label: '羞耻度', type: 'toggle', defaultValue: true },
                    { id: 'angerLevel', label: '愤怒度', type: 'toggle', defaultValue: true },
                    { id: 'pleasureLevel', label: '快感值 (敏感内容)', type: 'toggle', defaultValue: false },
                    { id: 'corruptionLevel', label: '堕落值 (敏感内容)', type: 'toggle', defaultValue: false },
                    { id: 'reputation', label: '声望', type: 'toggle', defaultValue: false },
                    { id: 'arousalLevel', label: '兴奋度 (敏感内容)', type: 'toggle', defaultValue: false },
                    { id: 'intimacyStatus', label: '亲密状态 (敏感内容)', type: 'toggle', defaultValue: false },
                    { id: 'bodyTemperature', label: '体温状态 (敏感内容)', type: 'toggle', defaultValue: false },
                    { id: 'sensitiveAreas', label: '敏感部位 (敏感内容)', type: 'toggle', defaultValue: false },
                    { id: 'clothingState', label: '衣物状态 (敏感内容)', type: 'toggle', defaultValue: false },
                    { id: 'physicalReaction', label: '生理反应 (敏感内容)', type: 'toggle', defaultValue: false },
                    { id: 'intimateRelations', label: '亲密关系 (敏感内容)', type: 'toggle', defaultValue: false },
                    { id: 'desireLevel', label: '欲望等级 (敏感内容)', type: 'toggle', defaultValue: false },
                    { id: 'sensitivityLevel', label: '敏感度 (敏感内容)', type: 'toggle', defaultValue: false },
                    { id: 'intimatePreferences', label: '亲密偏好 (敏感内容)', type: 'toggle', defaultValue: false }
                ]
            },
            inventory: {
                id: 'inventory',
                label: '背包/仓库',
                icon: 'fa-box',
                description: '管理角色的物品和资源',
                defaultEnabled: true,
                items: [
                    { id: 'inventoryItems', label: '背包物品', type: 'toggle', defaultValue: true },
                    { id: 'equipment', label: '装备物品', type: 'toggle', defaultValue: true },
                    { id: 'questItems', label: '任务物品栏', type: 'toggle', defaultValue: false },
                    { id: 'keychain', label: '钥匙串', type: 'toggle', defaultValue: false },
                    { id: 'resources', label: '资源库存', type: 'toggle', defaultValue: true },
                    { id: 'currency', label: '货币数量', type: 'toggle', defaultValue: true },
                    { id: 'weightLimit', label: '负重上限', type: 'toggle', defaultValue: false },
                    { id: 'rarityItems', label: '稀有物品', type: 'toggle', defaultValue: false },
                    { id: 'consumables', label: '消耗品', type: 'toggle', defaultValue: true },
                    { id: 'craftingMaterials', label: '制造材料', type: 'toggle', defaultValue: false }
                ]
            },
            company: {
                id: 'company',
                label: '公司信息',
                icon: 'fa-building',
                description: '关于角色当前主要关注的组织/公司信息',
                defaultEnabled: true,
                items: [
                    { id: 'name', label: '公司名称', type: 'toggle', defaultValue: true },
                    { id: 'type', label: '组织类型', type: 'toggle', defaultValue: true },
                    { id: 'status', label: '当前状态', type: 'toggle', defaultValue: true },
                    { id: 'mainBusiness', label: '主要业务/产品', type: 'toggle', defaultValue: true },
                    { id: 'employeeCount', label: '员工数量', type: 'toggle', defaultValue: false },
                    { id: 'hqLocation', label: '总部地点', type: 'toggle', defaultValue: false },
                    { id: 'valuation', label: '公司估值', type: 'toggle', defaultValue: true },
                    { id: 'funds', label: '流动资金', type: 'toggle', defaultValue: true },
                    { id: 'reputation', label: '影响力', type: 'toggle', defaultValue: true },
                    { id: 'shareholders', label: '股权结构', type: 'toggle', defaultValue: true },
                    { id: 'projects', label: '进行中项目', type: 'toggle', defaultValue: true },
                    { id: 'recentEvents', label: '近期事件/新闻', type: 'toggle', defaultValue: true },
                    { id: 'rivals', label: '主要竞争对手', type: 'toggle', defaultValue: false },
                    { id: 'marketShare', label: '市场份额', type: 'toggle', defaultValue: false },
                    { id: 'corporateCulture', label: '企业文化', type: 'toggle', defaultValue: false },
                    { id: 'coreTechnology', label: '核心技术', type: 'toggle', defaultValue: false },
                    { id: 'legalDisputes', label: '法律纠纷', type: 'toggle', defaultValue: false },
                    { id: 'mediaCoverage', label: '媒体评价', type: 'toggle', defaultValue: false },
                    { id: 'stockSymbol', label: '股票代码', type: 'toggle', defaultValue: false },
                ]
            },
            tasks: {
                id: 'tasks',
                label: '任务系统',
                icon: 'fa-tasks',
                description: '当前的主要任务、支线任务和目标管理',
                defaultEnabled: true,
                items: [
                    { id: 'showTaskType', label: '显示任务类型', type: 'toggle', defaultValue: true },
                    { id: 'showTaskStatus', label: '显示任务状态', type: 'toggle', defaultValue: true },
                    { id: 'showTaskDescription', label: '显示任务描述', type: 'toggle', defaultValue: true },
                    { id: 'showTaskProgress', label: '显示任务进度', type: 'toggle', defaultValue: true },
                    { id: 'showTaskRewards', label: '显示任务奖励', type: 'toggle', defaultValue: true },
                    { id: 'mainQuest', label: '主线任务 (整体)', type: 'toggle', defaultValue: true },
                    { id: 'sideQuests', label: '支线任务 (整体)', type: 'toggle', defaultValue: true },
                    { id: 'dailyTasks', label: '每日任务 (整体)', type: 'toggle', defaultValue: false },
                    { id: 'achievements', label: '成就 (整体)', type: 'toggle', defaultValue: false }
                ]
            },
            abilities: {
                id: 'abilities',
                label: '能力系统',
                icon: 'fa-magic',
                description: '角色的特殊能力和已习得技能',
                defaultEnabled: true,
                items: [
                    { id: 'specialAbilities', label: '特殊能力', type: 'toggle', defaultValue: true },
                    { id: 'learnedSkills', label: '已获得技能', type: 'toggle', defaultValue: true },
                    { id: 'passiveSkills', label: '被动技能', type: 'toggle', defaultValue: false },
                    { id: 'auraEffects', label: '光环效果', type: 'toggle', defaultValue: false },
                    { id: 'skillLevels', label: '技能等级', type: 'toggle', defaultValue: false },
                    { id: 'skillProficiency', label: '技能熟练度', type: 'toggle', defaultValue: false },
                    { id: 'experiencePoints', label: '经验值', type: 'toggle', defaultValue: false },
                    { id: 'talentTree', label: '天赋树', type: 'toggle', defaultValue: false },
                    { id: 'cooldowns', label: '技能冷却', type: 'toggle', defaultValue: false }
                ]
            },
            internet: {
                id: 'internet',
                label: '资讯内容',
                icon: 'fa-newspaper',
                description: '社交媒体、论坛、新闻和其他网络信息',
                defaultEnabled: false,
                items: [
                    { id: 'socialMediaFeed', label: '社交媒体流', type: 'toggle', defaultValue: true },
                    { id: 'forumPosts', label: '热门论坛帖子', type: 'toggle', defaultValue: true },
                    { id: 'jiuzhouExpress', label: '九州快报', type: 'toggle', defaultValue: false },
                    { id: 'localGossip', label: '坊间八卦', type: 'toggle', defaultValue: false },
                    { id: 'newsHeadlines', label: '新闻头条', type: 'toggle', defaultValue: false },
                    { id: 'trendingTopics', label: '热门话题', type: 'toggle', defaultValue: false },
                    { id: 'onlineStatus', label: '在线状态', type: 'toggle', defaultValue: false },
                    { id: 'notifications', label: '网络通知', type: 'toggle', defaultValue: false }
                ]
            },
            apocalypse: {
                id: 'apocalypse',
                label: '末日末世',
                icon: 'fa-biohazard',
                description: '在末日废土环境下的生存状态与资源。',
                defaultEnabled: false,
                items: [
                    { id: 'health', label: '生命值', type: 'toggle', defaultValue: true, description: '格式: 当前/最大 或 状态描述' },
                    { id: 'hunger', label: '饥饿度', type: 'toggle', defaultValue: true, description: '格式: 当前/最大 或 状态描述' },
                    { id: 'thirst', label: '口渴度', type: 'toggle', defaultValue: true, description: '格式: 当前/最大 或 状态描述' },
                    { id: 'stamina', label: '精力值/体力', type: 'toggle', defaultValue: true, description: '格式: 当前/最大 或 状态描述' },
                    { id: 'sanity', label: '精神状态/理智', type: 'toggle', defaultValue: true, description: '例如: 稳定, 濒临崩溃, 受到惊吓' },
                    { id: 'statusEffects', label: '当前状态效果', type: 'toggle', defaultValue: true, description: '例如: 辐射病(轻微), 骨折(左腿)' },
                    { id: 'radiationLevel', label: '辐射等级/暴露', type: 'toggle', defaultValue: true, description: '例如: 安全, 轻度污染, 致命剂量' },
                    { id: 'shelterStatus', label: '庇护所状态', type: 'toggle', defaultValue: true, description: '例如: 安全屋(良好), 无庇护所' },
                    { id: 'shelterDefense', label: '庇护所防御等级', type: 'toggle', defaultValue: false },
                    { id: 'mutationLevel', label: '变异等级/状态', type: 'toggle', defaultValue: false, description: '例如: 无变异, 轻微变异(皮肤硬化)' },
                    { id: 'infectionStatus', label: '感染状态', type: 'toggle', defaultValue: true, description: '例如: 未感染, 疑似感染, 已感染(T病毒)' },
                    { id: 'morale', label: '士气', type: 'toggle', defaultValue: true, description: '例如: 高昂, 低落, 绝望' },
                    { id: 'fatigue', label: '疲劳度', type: 'toggle', defaultValue: false, description: '例如: 轻微疲劳, 极度疲劳' },
                    { id: 'carryingCapacity', label: '负重', type: 'toggle', defaultValue: true, description: '格式: 当前/最大 (kg/lbs)' },
                    { id: 'ammoSupplies', label: '弹药储备', type: 'toggle', defaultValue: true, description: '例如: 步枪弹药:30, 手枪弹药:12' },
                    { id: 'foodSupplies', label: '食物储备', type: 'toggle', defaultValue: true, description: '例如: 罐头:3, 压缩饼干:5' },
                    { id: 'waterSupplies', label: '水源储备', type: 'toggle', defaultValue: true, description: '例如: 纯净水:1L, 脏水:0.5L' },
                    { id: 'medicalSupplies', label: '医疗物资', type: 'toggle', defaultValue: true, description: '例如: 绷带:2, 抗生素:1' },
                    { id: 'craftingMaterialsApoc', label: '特殊制造材料', type: 'toggle', defaultValue: false, description: '例如: 废金属:10, 电子元件:3' },
                    { id: 'hazardProtection', label: '危害防护', type: 'toggle', defaultValue: true, description: '例如: 防毒面具(完好), 防护服(破损)' },
                    { id: 'environmentThreats', label: '周围环境威胁', type: 'toggle', defaultValue: true, description: '例如: 丧尸群, 辐射区, 敌对拾荒者' },
                    { id: 'timeSinceLastRest', label: '上次休息时间', type: 'toggle', defaultValue: false, description: '例如: 8小时前, 刚休息过' },
                    { id: 'hopeLevel', label: '希望值', type: 'toggle', defaultValue: false, description: '例如: 尚存希望, 渺茫, 重燃希望' },
                    { id: 'daysSurvived', label: '已存活天数', type: 'toggle', defaultValue: false },
                    { id: 'factionReputationApoc', label: '派系声望(末日)', type: 'toggle', defaultValue: false, description: '例如: 拾荒者联盟:友好, 变异者部落:敌对' },
                    { id: 'availablePower', label: '可用电力/能源', type: 'toggle', defaultValue: false, description: '例如: 发电机燃料:20%, 电池:3/5格' },
                    { id: 'transportation', label: '交通工具状态', type: 'toggle', defaultValue: false, description: '例如: 摩托车(燃料:50%,耐久:80%)' },
                    { id: 'communicationDevice', label: '通讯设备状态', type: 'toggle', defaultValue: false },
                ]
            },
            ancientHistory: {
                id: 'ancientHistory',
                label: '古风历史',
                icon: 'fa-landmark',
                description: '古代或历史背景下的相关信息',
                defaultEnabled: false,
                items: [
                    { id: 'dynasty', label: '当前朝代/纪年', type: 'toggle', defaultValue: true },
                    { id: 'ruler', label: '君主/掌权者', type: 'toggle', defaultValue: true },
                    { id: 'socialRank', label: '社会阶层/身份', type: 'toggle', defaultValue: true },
                    { id: 'officialPosition', label: '官职/爵位', type: 'toggle', defaultValue: false },
                    { id: 'sectSchool', label: '所属门派/学派', type: 'toggle', defaultValue: false },
                    { id: 'jianghuAffiliation', label: '江湖势力/地位', type: 'toggle', defaultValue: false },
                    { id: 'martialArtsStyle', label: '武功流派/境界', type: 'toggle', defaultValue: false },
                    { id: 'internalEnergy', label: '内力/真气修为', type: 'toggle', defaultValue: false },
                    { id: 'historicalEvents', label: '重要历史事件', type: 'toggle', defaultValue: true },
                    { id: 'currencyAncient', label: '古代货币/财富', type: 'toggle', defaultValue: true },
                    { id: 'etiquetteCustoms', label: '礼仪习俗', type: 'toggle', defaultValue: false },
                    { id: 'importantBooks', label: '重要典籍/秘籍', type: 'toggle', defaultValue: false },
                    { id: 'legendaryWeapons', label: '神兵利器传说', type: 'toggle', defaultValue: false },
                    { id: 'philosophy', label: '主要哲学思想', type: 'toggle', defaultValue: false },
                    { id: 'artForms', label: '主要艺术形式', type: 'toggle', defaultValue: false }
                ]
            },
            cultivation: {
                id: 'cultivation',
                label: '修仙系统',
                icon: 'fa-bolt',
                description: '修仙、武功、境界等相关信息',
                defaultEnabled: false,
                items: [
                    { id: 'cultivationLevel', label: '修为境界', type: 'toggle', defaultValue: true },
                    { id: 'spiritualPower', label: '灵力值', type: 'toggle', defaultValue: true },
                    { id: 'techniques', label: '功法技能', type: 'toggle', defaultValue: true },
                    { id: 'artifacts', label: '法宝装备', type: 'toggle', defaultValue: true },
                    { id: 'sect', label: '门派信息', type: 'toggle', defaultValue: false },
                    { id: 'karma', label: '因果业力', type: 'toggle', defaultValue: false },
                    { id: 'tribulation', label: '天劫状态', type: 'toggle', defaultValue: false },
                    { id: 'spiritualRoots', label: '灵根属性', type: 'toggle', defaultValue: false }
                ]
            },
            urban: {
                id: 'urban',
                label: '都市生活',
                icon: 'fa-city',
                description: '现代都市生活相关信息',
                defaultEnabled: false,
                items: [
                    { id: 'socialStatus', label: '社会地位', type: 'toggle', defaultValue: true },
                    { id: 'bankAccount', label: '银行账户', type: 'toggle', defaultValue: true },
                    { id: 'creditScore', label: '信用评分', type: 'toggle', defaultValue: false },
                    { id: 'properties', label: '房产信息', type: 'toggle', defaultValue: false },
                    { id: 'vehicles', label: '车辆信息', type: 'toggle', defaultValue: false },
                    { id: 'contacts', label: '联系人', type: 'toggle', defaultValue: true },
                    { id: 'schedule', label: '日程安排', type: 'toggle', defaultValue: false },
                    { id: 'socialNetwork', label: '社交网络', type: 'toggle', defaultValue: false }
                ]
            },
            fantasy: {
                id: 'fantasy',
                label: '奇幻世界',
                icon: 'fa-dragon',
                description: '奇幻、魔法世界相关信息',
                defaultEnabled: false,
                items: [
                    { id: 'magicPower', label: '魔力值', type: 'toggle', defaultValue: true },
                    { id: 'spells', label: '法术列表', type: 'toggle', defaultValue: true },
                    { id: 'magicItems', label: '魔法物品', type: 'toggle', defaultValue: true },
                    { id: 'guild', label: '公会信息', type: 'toggle', defaultValue: false },
                    { id: 'reputation', label: '声望系统', type: 'toggle', defaultValue: false },
                    { id: 'blessings', label: '祝福/诅咒', type: 'toggle', defaultValue: false },
                    { id: 'familiars', label: '魔宠/使魔', type: 'toggle', defaultValue: false },
                    { id: 'manaRegeneration', label: '魔力恢复', type: 'toggle', defaultValue: false }
                ]
            },
            scifi: {
                id: 'scifi',
                label: '科幻未来',
                icon: 'fa-rocket',
                description: '科幻、未来世界相关信息',
                defaultEnabled: false,
                items: [
                    { id: 'cybernetics', label: '义体改造', type: 'toggle', defaultValue: true },
                    { id: 'netrunning', label: '网络潜行', type: 'toggle', defaultValue: false },
                    { id: 'reputation', label: '企业声望', type: 'toggle', defaultValue: false },
                    { id: 'augmentations', label: '增强植入', type: 'toggle', defaultValue: true },
                    { id: 'aiCompanion', label: 'AI伙伴', type: 'toggle', defaultValue: false },
                    { id: 'spaceTravel', label: '星际旅行', type: 'toggle', defaultValue: false },
                    { id: 'techLevel', label: '科技等级', type: 'toggle', defaultValue: false },
                    { id: 'cyberwareStatus', label: '赛博装备状态', type: 'toggle', defaultValue: false }
                ]
            },
            story: {
                id: 'story',
                label: '剧情面板',
                icon: 'fa-book',
                description: '管理剧情进展和关键事件',
                defaultEnabled: true,
                items: [
                    { id: 'mainQuest', label: '主线任务', type: 'toggle', defaultValue: true },
                    { id: 'keyEvents', label: '关键事件', type: 'toggle', defaultValue: true },
                    { id: 'storyArcs', label: '剧情章节', type: 'toggle', defaultValue: true },
                    { id: 'clues', label: '线索追踪', type: 'toggle', defaultValue: false },
                    { id: 'storySummary', label: '剧情摘要', type: 'toggle', defaultValue: true },
                    { id: 'factionRelations', label: '派系关系', type: 'toggle', defaultValue: false },
                    { id: 'plotTwists', label: '剧情转折', type: 'toggle', defaultValue: false },
                    { id: 'characterArcs', label: '角色弧光', type: 'toggle', defaultValue: false },
                    { id: 'unresolvedMysteries', label: '未解之谜', type: 'toggle', defaultValue: false },
                    { id: 'themesAndMotifs', label: '主题与母题', type: 'toggle', defaultValue: false },
                    { id: 'foreshadowing', label: '预兆与伏笔', type: 'toggle', defaultValue: false },
                    { id: 'timelineSummary', label: '时间线摘要', type: 'toggle', defaultValue: false }
                ]
            },
            world: {
                id: 'world',
                label: '世界面板',
                icon: 'fa-globe-americas',
                description: '世界背景和环境信息',
                defaultEnabled: true,
                items: [
                    { id: 'time', label: '世界时间', type: 'toggle', defaultValue: true },
                    { id: 'location', label: '世界地点', type: 'toggle', defaultValue: true },
                    { id: 'weather', label: '世界天气', type: 'toggle', defaultValue: true },
                    { id: 'worldMap', label: '世界地图', type: 'toggle', defaultValue: true },
                    { id: 'factions', label: '主要派系', type: 'toggle', defaultValue: true },
                    { id: 'worldHistory', label: '世界历史', type: 'toggle', defaultValue: false },
                    { id: 'culture', label: '文化背景', type: 'toggle', defaultValue: false },
                    { id: 'localLaws', label: '当地法律', type: 'toggle', defaultValue: false },
                    { id: 'localBeliefs', label: '当地信仰', type: 'toggle', defaultValue: false },
                    { id: 'gravity', label: '引力', type: 'toggle', defaultValue: false },
                    { id: 'airComposition', label: '空气成分', type: 'toggle', defaultValue: false },
                    { id: 'resources', label: '资源分布', type: 'toggle', defaultValue: false },
                    { id: 'worldEvents', label: '世界事件', type: 'toggle', defaultValue: true },
                    { id: 'worldRules', label: '世界规则', type: 'toggle', defaultValue: true },
                    { id: 'geography', label: '地理环境', type: 'toggle', defaultValue: false },
                    { id: 'climate', label: '气候条件', type: 'toggle', defaultValue: false },
                    { id: 'keyLocationsDetails', label: '重要地点详情', type: 'toggle', defaultValue: false },
                    { id: 'creaturesAndRaces', label: '生物与种族', type: 'toggle', defaultValue: false },
                    { id: 'organizationsAndPowers', label: '组织与势力', type: 'toggle', defaultValue: false },
                    { id: 'economyAndTrade', label: '经济与贸易', type: 'toggle', defaultValue: false },
                    { id: 'magicTechSystem', label: '魔法/科技系统细则', type: 'toggle', defaultValue: false },
                    { id: 'legendsAndMythology', label: '传说与神话', type: 'toggle', defaultValue: false }
                ]
            },
            dataManagement: {
                id: 'dataManagement',
                label: '数据管理',
                icon: 'fa-database',
                description: '管理信息栏的存储数据。此面板信息不会在聊天中显示。',
                defaultEnabled: true,
                isUtilityPanel: true,
                items: []
            }
        }
    };

    // 错误处理、通知、HTML转义 (与V10.0.27保持一致)
    function errorCatched(fn, context = null, functionName = 'anonymous') { return (...args) => { try { const result = fn.apply(context, args); if (result instanceof Promise) { return result.catch(error => { console.error(`[高级信息栏设置 ERROR] in ${functionName}:`, error, error.stack); notifyUser(`脚本错误 in ${functionName}: ${error.message}`, 'error', 5000); }); } return result; } catch (error) { console.error(`[高级信息栏设置 ERROR] in ${functionName}:`, error, error.stack); notifyUser(`脚本错误 in ${functionName}: ${error.message}`, 'error', 5000); } }; }
    function notifyUser(message, type = 'info', duration = 3000) { if (typeof SillyTavern !== 'undefined' && SillyTavern.TemmuzAdalet && SillyTavern.TemmuzAdalet.NotifSuccess) { const options = { timeOut: duration }; try { switch (type) { case 'success': SillyTavern.TemmuzAdalet.NotifSuccess(message, options); break; case 'error': SillyTavern.TemmuzAdalet.NotifError(message, options); break; case 'warning': SillyTavern.TemmuzAdalet.NotifWarning(message, options); break; default: SillyTavern.TemmuzAdalet.NotifInfo(message, options); break; } } catch (e) { console.error('[高级信息栏设置] 通知失败:', e); console.log(`[高级信息栏设置 Notification / ${type.toUpperCase()}]: ${message}`); } } else { console.log(`[高级信息栏设置 Notification / ${type.toUpperCase()}]: ${message}`); } }
    function escapeHtml(unsafe) { if (unsafe === null || unsafe === undefined) return ''; if (typeof unsafe !== 'string') return String(unsafe); return unsafe.replace(/&/g, "&amp;").replace(/</g, "&lt;").replace(/>/g, "&gt;").replace(/"/g, "&quot;").replace(/'/g, "&#039;"); }

    // 应用主题和字体 (与V10.0.27保持一致)
    function applyThemeAndFont(themeName, fontFamilyName) { errorCatched(() => { const theme = THEMES[themeName] || THEMES.现代深色; const font = FONT_OPTIONS[fontFamilyName] || FONT_OPTIONS.系统默认; const styleId = 'advanced-infobar-theme-styles'; $(`#${styleId}`).remove(); let css = ':root {\n'; for (const [key, value] of Object.entries(theme)) { css += `  ${key}: ${value};\n`; } css += `  --infobar-font-family: ${font};\n`; css += '}'; css += `\n#${POPUP_ID_SETTINGS}, #${POPUP_ID_DATATABLE}, .${RENDERED_INFO_BAR_CLASS} { font-family: var(--infobar-font-family); }`; $('head').append(`<style id="${styleId}">${css}</style>`); const $popup = $(`#${POPUP_ID_SETTINGS}`, window.parent.document); if ($popup.length) { $popup.css('font-family', `var(--infobar-font-family)`); $popup.hide().show(0); } $(`#${RENDERED_INFO_BAR_CLASS}`, window.parent.document).each(function() { $(this).css('font-family', `var(--infobar-font-family)`); $(this).css({ 'background': `var(--infobar-rendered-bg)`, 'border-color': `var(--infobar-rendered-border)`, 'color': `var(--infobar-rendered-text)` }); $(this).find('.rendered-panel-header').css('background', `var(--infobar-rendered-header-bg)`); $(this).find('.rendered-panel-title').css('color', `var(--infobar-rendered-title-text)`); $(this).find('.rendered-item-label').css('color', `var(--infobar-rendered-label)`); $(this).find('.rendered-item-value').css('color', `var(--infobar-rendered-value)`); }); }, null, 'applyThemeAndFont')(); }

    // 加载设置 (与V10.0.27保持一致)
    async function loadSettings() {
        return errorCatched(async () => {
            const saved = localStorage.getItem(STORAGE_KEY_CURRENT_SETTINGS);
            const parsedSettings = saved ? JSON.parse(saved) : {};
            const newSettings = {};

            for (const panelId in PANEL_CONFIG.panels) {
                const panelConfig = PANEL_CONFIG.panels[panelId];
                newSettings[panelId] = { enabled: parsedSettings[panelId]?.enabled ?? panelConfig.defaultEnabled, items: {} };
                for (const item of panelConfig.items) {
                    newSettings[panelId].items[item.id] = parsedSettings[panelId]?.items?.[item.id] ?? item.defaultValue;
                }
            }
            currentSettings = newSettings;

            const savedTheme = currentSettings.general?.items?.theme || '现代深色';
            const savedFont = currentSettings.general?.items?.fontFamily || '系统默认';
            applyThemeAndFont(savedTheme, savedFont);
        }, null, 'loadSettings')();
    }

    // 【V10.0.28 升级】世界信息管理 (任务清单指令升级)
    async function initializeOrUpdateWorldInfoEntry(entryType = 'all') {
        return errorCatched(async () => {
            if (!coreApisAreReady || !TavernHelper_API?.getLorebookEntries || !TavernHelper_API?.createLorebookEntries || !TavernHelper_API?.setLorebookEntries || !TavernHelper_API?.getCurrentCharPrimaryLorebook) {
                console.warn("[高级信息栏设置] World Info API尚未就绪，无法同步设置。");
                return;
            }

            let targetLorebookToUse = TARGET_LOREBOOK_NAME;
            if (!targetLorebookToUse) {
                try {
                    let retries = 0; const maxRetries = 5; const retryDelay = 1000;
                    while (retries < maxRetries) {
                        targetLorebookToUse = await TavernHelper_API.getCurrentCharPrimaryLorebook();
                        if (targetLorebookToUse) break;
                        retries++;
                        console.warn(`[高级信息栏设置] 未找到角色主世界书，正在重试 (${retries}/${maxRetries})...`);
                        await new Promise(resolve => setTimeout(resolve, retryDelay));
                    }
                    if (!targetLorebookToUse) {
                        notifyUser("多次尝试后仍未找到主世界书，无法自动创建/更新信息栏设置条目。请为当前角色选择一个主世界书，或在脚本中配置 TARGET_LOREBOOK_NAME。", "error", 7000);
                        return;
                    }
                } catch (e) {
                     notifyUser(`获取角色主世界书时出错: ${e.message}。请确保角色已加载并已选择世界书。`, "error", 7000);
                     return;
                }
            }

            const allEntries = await TavernHelper_API.getLorebookEntries(targetLorebookToUse);
            if (!allEntries) {
                notifyUser(`未能从世界书 "${targetLorebookToUse}" 获取条目列表，可能该世界书不存在。`, "error");
                return;
            }

            if (entryType === 'all' || entryType === 'tasklist') {
                const existingTasklistEntry = allEntries.find(entry => entry.comment === TASKLIST_WI_COMMENT_KEY);
                
                let taskListContent = "【数据生成任务清单】\n";
                taskListContent += "### 核心规则：\n";
                taskListContent += "1.  **【数据动态清理铁则】**:\n";
                taskListContent += "    *   **任务**: 对于 `tasks` 面板中，`status` 字段值为 '已完成' 或 '已失败' 的任务，在下一次生成 `<infobar_data>` 时，【必须】将该任务的整个JSON对象从任务数组（如 `sideQuests`）中彻底移除，不得再次输出。\n";
                taskListContent += "    *   **消耗品**: 对于 `inventory` 面板中的 `consumables` 字段，如果你在剧情中明确描写了角色使用了某个消耗品（例如，“林凡喝下了一瓶治疗药水”），则在下一次生成 `<infobar_data>` 时，【必须】将该消耗品从 `consumables` 列表中移除或减少其数量。\n";
                taskListContent += "    *   **一次性信息**: 对于 `internet` 面板中的 `notifications` 等一次性通知类信息，一旦角色在剧情中明确表示“已阅”或已处理，则在下一次生成 `<infobar_data>` 时，【必须】将其移除。\n";
                taskListContent += "2.  **【资讯全量强制更新】**: 对于 'internet' 面板，每一次生成，你都【必须】为所有用户在设置中启用的子项（例如，如果用户开启了 socialMediaFeed, forumPosts, newsHeadlines，你就必须为这三项都生成新的、符合当前世界观的内容，不得遗漏或仅用\"无\"填充无关项）进行一次符合当前世界观的更新。你需要模拟时间的流逝和信息的变化，即使变化很小也要体现。这是强制要求，不是可选任务。\n\n";
                taskListContent += "### 本次需更新项目：\n";

                let hasEnabledItems = false;
                if (currentSettings && Object.keys(currentSettings).length > 0) {
                    for (const panelId in PANEL_CONFIG.panels) {
                        if (currentSettings[panelId]?.enabled && !PANEL_CONFIG.panels[panelId].isUtilityPanel) {
                            const enabledItems = PANEL_CONFIG.panels[panelId].items
                                .filter(item => currentSettings[panelId].items[item.id])
                                .map(item => item.id);
                            if (enabledItems.length > 0) {
                                hasEnabledItems = true;
                                taskListContent += `- ${panelId}: ${enabledItems.join(', ')}\n`;
                            }
                        }
                    }
                }
                if (!hasEnabledItems) taskListContent += "- (当前没有启用任何项目)";

                if (existingTasklistEntry) {
                    if (existingTasklistEntry.content.trim() !== taskListContent.trim()) {
                        const updatedEntry = { ...existingTasklistEntry, content: taskListContent };
                        await TavernHelper_API.setLorebookEntries(targetLorebookToUse, [updatedEntry]);
                        console.log(`[高级信息栏设置] 已更新任务清单WI条目 "${TASKLIST_WI_COMMENT_KEY}"。`);
                    }
                } else {
                    const newEntryData = { comment: TASKLIST_WI_COMMENT_KEY, content: taskListContent, keys: [], enabled: true, type: 'constant', selective: false, position: 'before_character_definition', order: 9998 };
                    await TavernHelper_API.createLorebookEntries(targetLorebookToUse, [newEntryData]);
                    console.log(`[高级信息栏设置] 已创建任务清单WI条目 "${TASKLIST_WI_COMMENT_KEY}"。`);
                }
            }

            if (entryType === 'all' || entryType === 'memory') {
                const existingMemoryEntry = allEntries.find(entry => entry.comment === MEMORY_ASSIST_WI_COMMENT_KEY);
                const memoryContent = await generateFullDataContextSummary(true);

                if (existingMemoryEntry) {
                    if (existingMemoryEntry.content !== memoryContent || !existingMemoryEntry.enabled) {
                        const updatedEntry = { ...existingMemoryEntry, content: memoryContent, enabled: true };
                        await TavernHelper_API.setLorebookEntries(targetLorebookToUse, [updatedEntry]);
                        console.log(`[高级信息栏设置] 已更新记忆辅助WI条目 "${MEMORY_ASSIST_WI_COMMENT_KEY}"。`);
                    }
                } else {
                    const newEntryData = { comment: MEMORY_ASSIST_WI_COMMENT_KEY, content: memoryContent, keys: [], enabled: true, type: 'constant', selective: false, position: 'before_character_definition', order: 9999 };
                    await TavernHelper_API.createLorebookEntries(targetLorebookToUse, [newEntryData]);
                    console.log(`[高级信息栏设置] 已创建记忆辅助WI条目 "${MEMORY_ASSIST_WI_COMMENT_KEY}"。`);
                }
            }
            if (entryType === 'all') notifyUser(`信息栏设置已同步到世界书 "${targetLorebookToUse}"。`, "success", 2000);

        }, null, 'initializeOrUpdateWorldInfoEntry')();
    }

    // 保存设置 (与V10.0.27保持一致)
    async function saveCurrentActiveSettings() {
        errorCatched(async () => {
            localStorage.setItem(STORAGE_KEY_CURRENT_SETTINGS, JSON.stringify(currentSettings));
            await initializeOrUpdateWorldInfoEntry('tasklist');
        }, null, 'saveCurrentActiveSettings')();
    }

    // CSS (与V10.0.27保持一致)
    function addGlobalStyles() { errorCatched(() => { const styleId = 'advanced-infobar-base-styles'; if ($(`#${styleId}`).length > 0) return; const styles = `
        #${POPUP_ID_SETTINGS}, #${POPUP_ID_DATATABLE} { position: fixed; top: 5%; left: 50%; transform: translateX(-50%); width: 80vw; max-width: 1200px; height: 85vh; border-radius: 12px; z-index: 10000; display: flex; flex-direction: column; overflow: hidden; box-shadow: 0 6px 12px rgba(0,0,0,0.4); background: var(--infobar-bg); color: var(--infobar-text); border: 1px solid var(--infobar-border-color); font-family: var(--infobar-font-family); }
        #${POPUP_ID_SETTINGS}-overlay, #${POPUP_ID_DATATABLE}-overlay { position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.6); z-index: 9999; }
        #${POPUP_ID_SETTINGS} .infobar-main-content { display: flex; flex-grow: 1; overflow: hidden; }
        .infobar-tab-container { width: 30%; max-width: 300px; overflow-y: auto; height: 100%; scrollbar-width: thin; background: var(--infobar-tab-bg); border-right: 1px solid var(--infobar-border-color); scrollbar-color: var(--primary) var(--infobar-tab-bg); }
        .infobar-tab-container::-webkit-scrollbar { width: 8px; } .infobar-tab-container::-webkit-scrollbar-track { background: var(--infobar-tab-bg); } .infobar-tab-container::-webkit-scrollbar-thumb { background: var(--primary); border-radius: 4px; }
        .infobar-tab-button { display: flex; align-items: center; padding: 14px; border: none; width: 100%; text-align: left; cursor: pointer; font-size: 15px; transition: background 0.2s; background: none; color: var(--infobar-text); border-bottom: 1px solid var(--infobar-border-color); }
        .infobar-tab-button.active { background: var(--infobar-tab-active-bg); color: var(--infobar-tab-active-color); } .infobar-tab-button:hover { background: var(--infobar-tab-hover-bg); } .infobar-tab-button i { margin-right: 10px; width: 18px; }
        .infobar-content-container { flex: 1; padding: 25px; overflow-y: auto; height: 100%; scrollbar-width: thin; background: var(--infobar-bg); scrollbar-color: var(--primary) var(--infobar-bg); }
        .infobar-content-container::-webkit-scrollbar { width: 8px; } .infobar-content-container::-webkit-scrollbar-track { background: var(--infobar-bg); } .infobar-content-container::-webkit-scrollbar-thumb { background: var(--primary); border-radius: 4px; }
        .infobar-panel-header { font-size: 20px; font-weight: bold; margin-bottom: 12px; display: flex; align-items: center; padding-bottom: 10px; border-bottom: 2px solid var(--infobar-border-color); color: var(--infobar-text); } .infobar-panel-header i { margin-right: 10px; }
        .infobar-panel-description { font-size: 14px; margin-bottom: 25px; font-style: italic; color: var(--infobar-text-muted, #aaa); }
        .infobar-item-row { display: flex; justify-content: space-between; align-items: center; padding: 12px 0; border-bottom: 1px solid var(--infobar-border-color); }
        .infobar-item-row.panel-toggle { padding: 18px; margin-bottom: 20px; border-radius: 8px; background: var(--infobar-panel-toggle-bg); border: 1px solid var(--infobar-border-color); }
        .infobar-item-label { flex: 1; font-size: 15px; font-weight: 500; color: var(--infobar-text); } .infobar-item-control { flex: 0 0 auto; }
        .infobar-switch { position: relative; display: inline-block; width: 50px; height: 24px; } .infobar-switch input { opacity: 0; width: 0; height: 0; }
        .infobar-slider { position: absolute; cursor: pointer; top: 0; left: 0; right: 0; bottom: 0; background-color: #ccc; transition: .4s; border-radius: 24px; }
        .infobar-slider:before { position: absolute; content: ""; height: 18px; width: 18px; left: 3px; bottom: 3px; background-color: white; transition: .4s; border-radius: 50%; }
        input:checked + .infobar-slider { background-color: var(--primary); } input:checked + .infobar-slider:before { transform: translateX(26px); }
        .infobar-close-button { position: absolute; top: 15px; right: 15px; background: none; border: none; font-size: 22px; cursor: pointer; z-index: 10001; color: var(--infobar-text); } .infobar-close-button:hover { color: #ff6b6b; }
        .infobar-select { border-radius: 6px; padding: 8px 12px; font-size: 14px; min-width: 120px; background: var(--infobar-input-bg); color: var(--infobar-text); border: 1px solid var(--infobar-border-color); }
        .infobar-items-section { margin-top: 25px; } .infobar-items-title { font-size: 18px; font-weight: bold; margin-bottom: 20px; padding-left: 12px; color: var(--infobar-section-title); border-left: 4px solid var(--primary); }
        #infobar-panel-dataManagement .infobar-item-row { flex-direction: column; align-items: flex-start; }
        #infobar-panel-dataManagement button, #infobar-panel-dataManagement input[type="text"], #infobar-panel-dataManagement select, #infobar-panel-dataManagement input[type="file"] { margin-top: 10px; padding: 8px 15px; cursor: pointer; background-color: var(--primary); color: white; border: none; border-radius: 4px; font-size: 14px; width: auto; margin-right:10px; }
        #infobar-panel-dataManagement input[type="text"] { background-color: var(--infobar-input-bg); color: var(--infobar-text); border: 1px solid var(--infobar-border-color); width: calc(100% - 20px); }
        #infobar-panel-dataManagement input[type="file"] { background-color: var(--infobar-input-bg); color: var(--infobar-text); border: 1px solid var(--infobar-border-color); padding: 5px; }
        #infobar-panel-dataManagement button:hover { filter: brightness(1.1); } #infobar-panel-dataManagement button.danger { background-color: #e74c3c; }
        #infobar-panel-dataManagement .data-preview { margin-top:10px; padding:10px; background: var(--infobar-input-bg); border:1px solid var(--infobar-border-color); border-radius:4px; max-height: 150px; overflow-y:auto; white-space:pre-wrap; word-break:break-all; font-size:0.9em; color: var(--infobar-text); }
        .${RENDERED_INFO_BAR_CLASS} { font-family: var(--infobar-font-family); padding: 0; margin-top: 10px; margin-bottom: 10px; border-radius: 8px; background: var(--infobar-rendered-bg); border: 1px solid var(--infobar-rendered-border); color: var(--infobar-rendered-text); font-size: 0.9em; backdrop-filter: blur(3px); box-shadow: 0 2px 5px rgba(0,0,0,0.2); }
        .${RENDERED_INFO_BAR_CLASS} .rendered-panel-header { display: flex; align-items: center; justify-content: space-between; padding: 8px 12px; cursor: pointer; background: var(--infobar-rendered-header-bg); border-bottom: 1px solid var(--infobar-rendered-border); border-top-left-radius: 7px; border-top-right-radius: 7px; }
        .${RENDERED_INFO_BAR_CLASS} .rendered-panel:last-of-type .rendered-panel-header.collapsed { border-bottom-left-radius: 7px; border-bottom-right-radius: 7px; border-bottom:none; }
        .${RENDERED_INFO_BAR_CLASS} .rendered-panel:last-of-type .rendered-panel-content.expanded { border-bottom-left-radius: 7px; border-bottom-right-radius: 7px; }
        .${RENDERED_INFO_BAR_CLASS} .rendered-panel-title { font-weight: bold; color: var(--infobar-rendered-title-text); }
        .${RENDERED_INFO_BAR_CLASS} .rendered-panel-icon { margin-left: 8px; transition: transform 0.2s ease-in-out; }
        .${RENDERED_INFO_BAR_CLASS} .rendered-panel-icon.collapsed { transform: rotate(-90deg); }
        .${RENDERED_INFO_BAR_CLASS} .rendered-panel-content { padding: 5px 12px 10px 12px; max-height: 300px; overflow-y: auto; display: none; border-bottom: 1px solid var(--infobar-rendered-border); }
        .${RENDERED_INFO_BAR_CLASS} .rendered-panel:last-child .rendered-panel-content { border-bottom: none; }
        .${RENDERED_INFO_BAR_CLASS} .rendered-panel-content.expanded { display: block; }
        .${RENDERED_INFO_BAR_CLASS} .rendered-item { display: flex; justify-content: flex-start; padding: 4px 0; border-bottom: 1px dotted rgba(170,170,170, 0.2); }
        .${RENDERED_INFO_BAR_CLASS} .rendered-item:last-child { border-bottom: none; }
        .${RENDERED_INFO_BAR_CLASS} .rendered-item-label { font-weight: 500; margin-right: 8px; color: var(--infobar-rendered-label); flex-shrink: 0; text-align: left !important; }
        .${RENDERED_INFO_BAR_CLASS} .rendered-item-value { text-align: left !important; word-break: break-all; color: var(--infobar-rendered-value); margin-left: 0; }
        .${RENDERED_INFO_BAR_CLASS} .rendered-npc-block { margin-top: 8px; padding-top: 8px; border-top: 1px dashed var(--infobar-rendered-border); }
        .${RENDERED_INFO_BAR_CLASS} .rendered-npc-block:first-child { margin-top: 0; padding-top: 0; border-top: none; }
        .${RENDERED_INFO_BAR_CLASS} .rendered-npc-name { font-weight: bold; font-size: 1.05em; margin-bottom: 5px; color: var(--infobar-rendered-title-text); }
        .infobar-task-card { background-color: var(--infobar-task-card-bg); border: 1px solid var(--infobar-rendered-border); border-radius: 6px; padding: 0; margin-bottom: 10px; overflow: hidden; }
        .infobar-task-card .task-header { display: flex; justify-content: space-between; align-items: center; padding: 8px 12px; cursor: pointer; }
        .infobar-task-card .task-header .task-title { font-size: 1.1em; font-weight: bold; color: var(--infobar-rendered-title-text); margin: 0 8px; flex-grow: 1; }
        .infobar-task-card .task-header .task-meta { font-size: 0.85em; color: var(--infobar-text-muted, #aaa); display: flex; align-items: center; }
        .infobar-task-card .task-header .task-type { font-weight: bold; margin-right: 8px; }
        .infobar-task-card .task-header .task-status { padding: 2px 6px; border-radius: 4px; background-color: var(--primary); color: white; font-size: 0.8em; }
        .infobar-task-card .task-content { padding: 10px; border-top: 1px solid var(--infobar-rendered-border); display: none; }
        .infobar-task-card .task-description { margin-bottom: 8px; line-height: 1.4; color: var(--infobar-rendered-text); }
        .infobar-task-card .task-progress-bar { width: 100%; background-color: var(--infobar-task-progress-bg); border-radius: 4px; height: 8px; overflow: hidden; margin-bottom: 5px; }
        .infobar-task-card .task-progress-fill { height: 100%; background-color: var(--infobar-task-progress-fill); border-radius: 4px; transition: width 0.3s ease; }
        .infobar-task-card .task-progress-text { font-size: 0.8em; color: var(--infobar-text-muted, #aaa); margin-bottom: 8px; }
        .infobar-task-card .task-rewards { font-size: 0.9em; color: var(--infobar-rendered-text); }
        .infobar-task-card .task-rewards strong { color: var(--infobar-rendered-label); }
        .infobar-internet-post, .infobar-custom-feed-item { background-color: var(--infobar-task-card-bg); border: 1px solid var(--infobar-rendered-border); border-radius: 6px; padding: 0; margin-bottom: 10px; overflow: hidden; }
        .infobar-internet-post .post-header, .infobar-custom-feed-item .custom-feed-header { cursor: pointer; display: flex; justify-content: space-between; align-items: center; padding: 8px 12px; }
        .infobar-internet-post .post-title, .infobar-custom-feed-item .custom-feed-title { font-size: 1.1em; font-weight: bold; color: var(--infobar-rendered-title-text); flex-grow: 1; margin-right: 8px; }
        .infobar-internet-post .post-detail-content, .infobar-custom-feed-item .custom-feed-detail-content { display: none; padding: 10px; border-top: 1px solid var(--infobar-rendered-border); }
        .infobar-internet-post .post-meta, .infobar-custom-feed-item .custom-feed-meta { font-size: 0.85em; color: var(--infobar-text-muted, #aaa); margin-bottom: 5px; }
        .infobar-internet-post .post-summary, .infobar-custom-feed-item .custom-feed-content { margin-bottom: 8px; line-height: 1.4; color: var(--infobar-rendered-text); }
        .infobar-internet-post .comment { background-color: var(--infobar-internet-comment-bg); border: 1px solid var(--infobar-border-color); border-radius: 4px; padding: 8px; margin-bottom: 8px; font-size: 0.9em; }
        .infobar-internet-post .comment-author { font-weight: bold; color: var(--infobar-rendered-label); margin-bottom: 3px; }
        .infobar-internet-post .comment-text { line-height: 1.3; color: var(--infobar-rendered-text); }
        .feed-expand-icon { transition: transform 0.2s ease-in-out; margin-left: 5px; }
        .jiuzhouExpress-item { font-family: 'KaiTi', 'SimSun', serif; }
        .localGossip-item { font-style: italic; }
        .infobar-progress-bar-container { display: flex; align-items: center; width: 100%; }
        .infobar-progress-bar-wrapper { flex-grow: 1; height: 12px; background-color: var(--infobar-task-progress-bg); border-radius: 6px; overflow: hidden; margin: 0 8px; border: 1px solid var(--infobar-rendered-border); }
        .infobar-progress-bar-fill { height: 100%; transition: width 0.3s ease; text-align:center; font-size:9px; line-height:12px; color: white; text-shadow: 1px 1px 1px rgba(0,0,0,0.3); }
        .infobar-progress-bar-value { min-width: 40px; text-align: right; font-size: 0.9em; }
        #${POPUP_ID_DATATABLE} .datatable-container { padding: 25px; overflow: auto; flex-grow: 1; }
        #${POPUP_ID_DATATABLE} .datatable-container h3 { font-size: 18px; color: var(--infobar-section-title); border-bottom: 2px solid var(--infobar-border-color); padding-bottom: 8px; margin-top: 20px; margin-bottom: 15px; }
        #${POPUP_ID_DATATABLE} .datatable-container h3:first-child { margin-top: 0; }
        #${POPUP_ID_DATATABLE} .datatable-panel-wrapper { display: flex; flex-wrap: wrap; gap: 20px; }
        #${POPUP_ID_DATATABLE} .datatable-panel-block { flex: 1 1 400px; border: 1px solid var(--infobar-border-color); border-radius: 8px; overflow: hidden; display: flex; flex-direction: column; }
        #${POPUP_ID_DATATABLE} .datatable-panel-block-header { background-color: var(--infobar-tab-active-bg); color: var(--infobar-tab-active-color); font-weight: bold; padding: 10px 15px; }
        #${POPUP_ID_DATATABLE} .datatable-panel-block-content { max-height: 350px; overflow-y: auto; padding: 10px; }
        #${POPUP_ID_DATATABLE} .datatable-kv-row { display: flex; border-bottom: 1px solid var(--infobar-border-color); }
        #${POPUP_ID_DATATABLE} .datatable-kv-row:last-child { border-bottom: none; }
        #${POPUP_ID_DATATABLE} .datatable-kv-key { flex: 0 0 150px; padding: 8px; font-weight: 500; background-color: var(--infobar-tab-bg); text-align: right; border-right: 1px solid var(--infobar-border-color); }
        #${POPUP_ID_DATATABLE} .datatable-kv-value { flex-grow: 1; padding: 8px; text-align: left; white-space: normal; word-break: break-all; }
        #${POPUP_ID_DATATABLE} .datatable-kv-value[contenteditable="true"] { background-color: var(--infobar-input-bg); cursor: cell; }
        #${POPUP_ID_DATATABLE} .datatable-kv-value[contenteditable="true"]:focus { outline: 2px solid var(--primary); box-shadow: 0 0 5px var(--primary); }
        #${POPUP_ID_DATATABLE} .datatable-kv-value.infobar-data-changed { background-color: var(--infobar-data-changed-bg) !important; animation: infobar-highlight-fade 2s ease-out; }
        @keyframes infobar-highlight-fade { 0% { background-color: var(--infobar-data-changed-bg); } 100% { background-color: transparent; } }
        .infobar-npc-selector-wrapper { position: sticky; top: -1px; background: var(--infobar-rendered-header-bg); padding: 8px 12px; z-index: 1; margin: 0 -12px 5px -12px; border-bottom: 1px solid var(--infobar-rendered-border); }
        .infobar-expand-button { display: block; text-align: center; padding: 5px; margin-top: 5px; cursor: pointer; color: var(--primary); font-size: 0.9em; border: 1px solid var(--primary); border-radius: 4px; background: var(--infobar-input-bg); }
        .infobar-expand-button:hover { background: var(--infobar-tab-hover-bg); }
            `;
            $('head').append(`<style id="${styleId}">${styles}</style>`);
        }, null, 'addGlobalStyles')();
    }

    // UI创建与管理 (与V10.0.27保持一致)
    function updateMenuButtonVisibility() {
        const localParentDoc = window.parent.document;
        const $dataTableButton = $(`#${BUTTON_ID_DATATABLE}`, localParentDoc);
        if (currentSettings.general?.items?.enableDataTable) {
            if ($dataTableButton.length === 0) {
                const dataTableButtonHtml = `<div id="${BUTTON_ID_DATATABLE}" class="list-group-item flex-container flexGap5 interactable" title="打开信息栏数据表格" tabIndex="0"><i class="fa-solid fa-table"></i><span>数据表格</span></div>`;
                $(`#${BUTTON_ID_SETTINGS}`, localParentDoc).after(dataTableButtonHtml);
                $(`#${BUTTON_ID_DATATABLE}`, localParentDoc).on('click', function (e) { e.preventDefault(); e.stopPropagation(); showDataTablePopup(); });
            }
        } else {
            $dataTableButton.remove();
        }
    }
    function createMenuButton() {
        errorCatched(() => {
            if (!window.parent || !window.parent.document) { setTimeout(createMenuButton, 1000); return; }
            const localParentDoc = window.parent.document;
            const $extensionsMenu = $('#extensionsMenu', localParentDoc);
            if ($extensionsMenu.length === 0) { setTimeout(createMenuButton, 1000); return; }
            addGlobalStyles();
            $(`#${BUTTON_ID_SETTINGS}`, localParentDoc).remove();
            $(`#${BUTTON_ID_DATATABLE}`, localParentDoc).remove();
            const settingsButtonHtml = `<div id="${BUTTON_ID_SETTINGS}" class="list-group-item flex-container flexGap5 interactable" title="打开高级信息栏设置界面" tabIndex="0"><i class="fa-solid fa-cog"></i><span>信息栏设置</span></div>`;
            $extensionsMenu.append(settingsButtonHtml);
            $(`#${BUTTON_ID_SETTINGS}`, localParentDoc).on('click', function (e) { e.preventDefault(); e.stopPropagation(); showSettingsPopup(); });
            updateMenuButtonVisibility();
        }, null, 'createMenuButton')();
    }
    function generateItemControl(item, panelId) { const currentValue = currentSettings[panelId].items[item.id]; switch (item.type) { case 'toggle': return `<label class="infobar-switch"><input type="checkbox" data-panel-id="${panelId}" data-item-id="${item.id}" ${currentValue ? 'checked' : ''}><span class="infobar-slider"></span></label>`; case 'select': const options = item.options.map(opt => `<option value="${escapeHtml(opt)}" ${currentValue === opt ? 'selected' : ''}>${escapeHtml(opt)}</option>`).join(''); return `<select class="infobar-select" data-panel-id="${panelId}" data-item-id="${item.id}">${options}</select>`; default: return `<span>未知类型</span>`; } }
    async function showSettingsPopup() {
        await errorCatched(async () => {
            await loadSettings();
            const localParentDoc = window.parent.document;
            if (!localParentDoc) { console.error('[高级信息栏设置 ERROR] showSettingsPopup: 未找到父文档。'); return; }

            $(`#${POPUP_ID_SETTINGS}`, localParentDoc).remove();
            $(`#${POPUP_ID_SETTINGS}-overlay`, localParentDoc).remove();

            let panelTabsHtml = '', panelContentHtml = '', firstPanel = true;
            for (const panelId in PANEL_CONFIG.panels) {
                const panelConfig = PANEL_CONFIG.panels[panelId];
                if (!panelConfig) continue;

                const iconHtml = panelConfig.icon ? `<i class="fa-solid ${panelConfig.icon}"></i>` : '';
                panelTabsHtml += `<button class="infobar-tab-button ${firstPanel ? 'active' : ''}" data-tab="${panelConfig.id}">${iconHtml}<span class="infobar-tab-label">${escapeHtml(panelConfig.label)}</span></button>`;

                let itemsHtml = '';
                if (panelConfig.id === 'dataManagement') {
                    const globalVars = await getVariables({ type: 'global' }) || {};
                    const namedConfigs = globalVars[GLOBAL_VAR_KEY_NAMED_CONFIGS] || {};
                    let configOptionsHtml = '<option value="">--选择一个配置--</option>';
                    for (const configName in namedConfigs) {
                        configOptionsHtml += `<option value="${escapeHtml(configName)}">${escapeHtml(configName)}</option>`;
                    }
                    itemsHtml = `
                        <div class="infobar-items-title">当前数据状态</div>
                        <div class="infobar-item-row"><label class="infobar-item-label">当前聊天变量数据预览 (最新快照):</label><div class="data-preview" id="infobar-data-preview">(加载中...)</div></div>
                        <div class="infobar-item-row"><label class="infobar-item-label">当前记忆辅助WI条目内容:</label><div class="data-preview" id="infobar-memory-assist-preview">(加载中...)</div></div>
                        <div class="infobar-item-row"><button id="infobar-clear-chat-data" class="danger">清除当前聊天所有信息栏历史数据</button></div>
                        <hr style="margin: 20px 0; border-color: var(--infobar-border-color);">
                        <div class="infobar-items-title">配置管理 (存储于SillyTavern全局变量)</div>
                        <div class="infobar-item-row"><label class="infobar-item-label" for="infobar-config-name">新配置名称:</label><input type="text" id="infobar-config-name" class="infobar-select" style="flex-grow:1; margin-right:10px;" placeholder="例如：战斗场景配置"></div>
                        <div class="infobar-item-row" style="justify-content: flex-end;"><button id="infobar-save-named-config">保存当前设置为新配置</button></div>
                        <div class="infobar-item-row"><label class="infobar-item-label" for="infobar-load-config-select">已存配置:</label><select id="infobar-load-config-select" class="infobar-select" style="flex-grow:1; margin-right:10px;">${configOptionsHtml}</select></div>
                        <div class="infobar-item-row" style="justify-content: flex-end;"><button id="infobar-load-selected-config">加载选中</button><button id="infobar-delete-selected-config" class="danger" style="margin-left:5px;">删除选中</button></div>
                        <hr style="margin: 15px 0; border-color: var(--infobar-border-color);">
                        <div class="infobar-item-row"><button id="infobar-export-configs">导出所有命名配置</button></div>
                        <div class="infobar-item-row"><label class="infobar-item-label" for="infobar-import-file">导入配置 (JSON文件):</label><input type="file" id="infobar-import-file" accept=".json" style="flex-grow:1; margin-right:10px; display:none;"> <button id="infobar-trigger-import-file">选择文件...</button> </div>
                        <div class="infobar-item-row" style="justify-content: flex-end;"><button id="infobar-import-configs-btn" style="display:none;">确认导入</button> </div>
                    `;
                } else if (panelConfig.items && panelConfig.items.length > 0) {
                    itemsHtml = `<div class="infobar-items-section"><div class="infobar-items-title">子项设置</div>${panelConfig.items.map(item => `
                        <div class="infobar-item-row">
                            <label class="infobar-item-label">${escapeHtml(item.label)}${item.description ? `<small style="display:block;opacity:0.7;">${escapeHtml(item.description)}</small>` : ''}</label>
                            <div class="infobar-item-control">${generateItemControl(item, panelConfig.id)}</div>
                        </div>`).join('')}
                    </div>`;
                }

                panelContentHtml += `
                    <div class="infobar-content-panel" id="infobar-panel-${panelConfig.id}" style="display: ${firstPanel ? 'block' : 'none'};">
                        <div class="infobar-panel-header">${iconHtml}${escapeHtml(panelConfig.label)}</div>
                        <div class="infobar-panel-description">${escapeHtml(panelConfig.description)}</div>
                        ${!(panelConfig.isUtilityPanel) ? `
                            <div class="infobar-item-row panel-toggle">
                                <label for="infobar-panel-${panelConfig.id}-toggle" class="infobar-item-label">启用此面板</label>
                                <div class="infobar-item-control">
                                    <label class="infobar-switch"><input type="checkbox" id="infobar-panel-${panelConfig.id}-toggle" data-panel-id="${panelConfig.id}" ${currentSettings[panelId]?.enabled ? 'checked' : ''}><span class="infobar-slider"></span></label>
                                </div>
                            </div>` : ''}
                        ${itemsHtml}
                    </div>`;
                firstPanel = false;
            }

            const popupHtml = `
                <div id="${POPUP_ID_SETTINGS}-overlay"></div>
                <div id="${POPUP_ID_SETTINGS}">
                    <button class="infobar-close-button" title="关闭"><i class="fa-solid fa-times"></i></button>
                    <div class="infobar-main-content">
                        <div class="infobar-tab-container">${panelTabsHtml}</div>
                        <div class="infobar-content-container">${panelContentHtml}</div>
                    </div>
                </div>`;

            $('body', localParentDoc).append(popupHtml);
            const $popup = $(`#${POPUP_ID_SETTINGS}`, localParentDoc), $overlay = $(`#${POPUP_ID_SETTINGS}-overlay`, localParentDoc);

            $overlay.on('click', function() { $popup.remove(); $overlay.remove(); });
            $popup.find('.infobar-close-button').on('click', function() { $popup.remove(); $overlay.remove(); });

            $popup.find('.infobar-tab-button').on('click', function () {
                const $this = $(this), tabId = $this.data('tab');
                $popup.find('.infobar-tab-button').removeClass('active');
                $this.addClass('active');
                $popup.find('.infobar-content-panel').hide();
                $popup.find(`#infobar-panel-${tabId}`).show();
                if (tabId === 'dataManagement') { updateDataManagementPanel(); }
            });

            $popup.find('input[type="checkbox"][data-panel-id]').on('change', async function () {
                const $this = $(this), panelId = $this.data('panel-id'), itemId = $this.data('item-id');
                if (itemId) {
                    currentSettings[panelId].items[itemId] = $this.is(':checked');
                    if (itemId === 'enableDataTable' || itemId === 'memoryAssistEnabled') {
                        updateMenuButtonVisibility();
                        await initializeOrUpdateWorldInfoEntry('all');
                    }
                    notifyUser(`${PANEL_CONFIG.panels[panelId].label} - ${PANEL_CONFIG.panels[panelId].items.find(i => i.id === itemId).label} ${$this.is(':checked') ? '已启用' : '已禁用'}`, 'success');
                } else {
                    currentSettings[panelId].enabled = $this.is(':checked');
                    await initializeOrUpdateWorldInfoEntry('all');
                    notifyUser(`面板 ${PANEL_CONFIG.panels[panelId].label} ${$this.is(':checked') ? '已启用' : '已禁用'}`, 'success');
                }
                await saveCurrentActiveSettings();
            });

            $popup.find('select[data-panel-id]').on('change', async function () {
                const $this = $(this), panelId = $this.data('panel-id'), itemId = $this.data('item-id');
                currentSettings[panelId].items[itemId] = $this.val();
                if (itemId === 'theme' || itemId === 'fontFamily') {
                    applyThemeAndFont(currentSettings.general.items.theme, currentSettings.general.items.fontFamily);
                }
                await saveCurrentActiveSettings();
                notifyUser(`设置已更新`, 'success');
            });
            
            $popup.find('#infobar-clear-chat-data').on('click', async () => { if (confirm('确定要清除当前聊天的【所有】信息栏历史数据和记忆辅助摘要吗？此操作不可逆。')) { await clearChatVarsData(); notifyUser('当前聊天的信息栏数据已清除。', 'info'); updateDataManagementPanel(); } });
            $popup.find('#infobar-save-named-config').on('click', async () => { const configName = $(`#infobar-config-name`, localParentDoc).val().trim(); if (!configName) { notifyUser('请输入配置名称！', 'warning'); return; } let globalVars = await getVariables({ type: 'global' }) || {}; if (!globalVars[GLOBAL_VAR_KEY_NAMED_CONFIGS]) globalVars[GLOBAL_VAR_KEY_NAMED_CONFIGS] = {}; globalVars[GLOBAL_VAR_KEY_NAMED_CONFIGS][configName] = JSON.parse(JSON.stringify(currentSettings)); await replaceVariables(globalVars, { type: 'global' }); notifyUser(`配置 "${configName}" 已保存到全局变量！`, 'success'); await updateConfigSelector(localParentDoc); $(`#infobar-config-name`, localParentDoc).val(''); });
            $popup.find('#infobar-load-selected-config').on('click', async () => { const configName = $(`#infobar-load-config-select`, localParentDoc).val(); if (!configName) { notifyUser('请选择一个要加载的配置！', 'warning'); return; } const globalVars = await getVariables({ type: 'global' }) || {}; const namedConfigs = globalVars[GLOBAL_VAR_KEY_NAMED_CONFIGS] || {}; if (namedConfigs[configName]) { currentSettings = JSON.parse(JSON.stringify(namedConfigs[configName])); await saveCurrentActiveSettings(); applyThemeAndFont(currentSettings.general.items.theme, currentSettings.general.items.fontFamily); $popup.remove(); $overlay.remove(); showSettingsPopup(); notifyUser(`配置 "${configName}" 已加载并应用！`, 'success'); } else { notifyUser(`未找到配置 "${configName}"！`, 'error'); } });
            $popup.find('#infobar-delete-selected-config').on('click', async () => { const configName = $(`#infobar-load-config-select`, localParentDoc).val(); if (!configName) { notifyUser('请选择一个要删除的配置！', 'warning'); return; } if (confirm(`确定要删除全局配置 "${configName}" 吗？`)) { let globalVars = await getVariables({ type: 'global' }) || {}; if (globalVars[GLOBAL_VAR_KEY_NAMED_CONFIGS] && globalVars[GLOBAL_VAR_KEY_NAMED_CONFIGS][configName]) { delete globalVars[GLOBAL_VAR_KEY_NAMED_CONFIGS][configName]; await replaceVariables(globalVars, { type: 'global' }); notifyUser(`配置 "${configName}" 已从全局变量删除！`, 'info'); await updateConfigSelector(localParentDoc); } else { notifyUser(`未找到配置 "${configName}" 无法删除！`, 'error'); } } });
            $popup.find('#infobar-export-configs').on('click', async () => { const globalVars = await getVariables({ type: 'global' }) || {}; const namedConfigs = globalVars[GLOBAL_VAR_KEY_NAMED_CONFIGS] || {}; if (Object.keys(namedConfigs).length === 0) { notifyUser('没有可导出的配置。', 'info'); return; } const filename = `infobar_configs_${SCRIPT_VERSION_TAG}.json`; const jsonStr = JSON.stringify(namedConfigs, null, 2); const blob = new Blob([jsonStr], { type: 'application/json' }); const url = URL.createObjectURL(blob); const a = document.createElement('a'); a.href = url; a.download = filename; document.body.appendChild(a); a.click(); document.body.removeChild(a); URL.revokeObjectURL(url); notifyUser('配置已导出！', 'success'); });
            $popup.find('#infobar-trigger-import-file').on('click', () => { $(`#infobar-import-file`, localParentDoc).click(); });
            $(`#infobar-import-file`, localParentDoc).on('change', async function(event) { const file = event.target.files[0]; if (!file) { $(`#infobar-import-configs-btn`, localParentDoc).hide(); return; } $(`#infobar-import-configs-btn`, localParentDoc).show(); });
            $popup.find('#infobar-import-configs-btn').on('click', async () => { const fileInput = $(`#infobar-import-file`, localParentDoc)[0]; const file = fileInput.files[0]; if (!file) { notifyUser('未选择文件或文件已处理。请重新通过“选择文件”按钮选择。', 'warning'); return; } const reader = new FileReader(); reader.onload = async (e) => { try { const importedConfigs = JSON.parse(e.target.result); if (typeof importedConfigs !== 'object' || importedConfigs === null) { throw new Error('文件内容不是有效的JSON对象。'); } let globalVars = await getVariables({ type: 'global' }) || {}; if (!globalVars[GLOBAL_VAR_KEY_NAMED_CONFIGS]) globalVars[GLOBAL_VAR_KEY_NAMED_CONFIGS] = {}; for (const configName in importedConfigs) { if (PANEL_CONFIG.panels) { globalVars[GLOBAL_VAR_KEY_NAMED_CONFIGS][configName] = importedConfigs[configName]; } } await replaceVariables(globalVars, { type: 'global' }); notifyUser('配置已成功导入并合并！请重新打开设置界面查看。', 'success'); await updateConfigSelector(localParentDoc); fileInput.value = ''; $(`#infobar-import-configs-btn`, localParentDoc).hide(); $popup.remove(); $overlay.remove(); showSettingsPopup(); } catch (err) { notifyUser(`导入配置失败: ${err.message}`, 'error'); console.error("导入配置失败:", err); $(`#infobar-import-configs-btn`, localParentDoc).hide(); fileInput.value = ''; } }; reader.readAsText(file); });

            if ($popup.find('.infobar-tab-button.active').data('tab') === 'dataManagement') {
                updateDataManagementPanel();
            }
        }, null, 'showSettingsPopup')();
    }
    async function updateConfigSelector(docContext) { const $select = $(`#infobar-load-config-select`, docContext); if (!$select.length) return; const globalVars = await getVariables({ type: 'global' }) || {}; const namedConfigs = globalVars[GLOBAL_VAR_KEY_NAMED_CONFIGS] || {}; let configOptionsHtml = '<option value="">--选择一个配置--</option>'; for (const configName in namedConfigs) { configOptionsHtml += `<option value="${escapeHtml(configName)}">${escapeHtml(configName)}</option>`; } $select.html(configOptionsHtml); }
    async function updateDataManagementPanel() {
        const $preview = $(`#infobar-data-preview`, window.parent.document);
        const $memoryPreview = $(`#infobar-memory-assist-preview`, window.parent.document);
        if ($preview.length) {
            try {
                const data = await loadDataHistorySnapshot();
                if (data && Object.keys(data).length > 0) {
                    const dataString = JSON.stringify(data, null, 2);
                    $preview.text(dataString.substring(0, 500) + (dataString.length > 500 ? '...' : ''));
                } else { $preview.text('(当前聊天无已存数据)'); }
            } catch (e) { $preview.text('(加载数据预览失败)'); console.error("[高级信息栏设置 v10.0.26] 更新数据预览失败:", e); }
        }
        if ($memoryPreview.length) {
            if (currentSettings.general?.items?.memoryAssistEnabled) {
                const memorySummary = await generateFullDataContextSummary(true);
                $memoryPreview.text(memorySummary || '(无记忆辅助内容生成或WI条目未找到)');
            } else { $memoryPreview.text('(记忆辅助功能未启用)'); }
        }
    }
    
    // 数据表格 (与V10.0.27保持一致)
    async function showDataTablePopup() {
        await errorCatched(async () => {
            const localParentDoc = window.parent.document;
            if (!localParentDoc) { console.error('[高级信息栏设置 ERROR] showDataTablePopup: 未找到父文档。'); return; }

            $(`#${POPUP_ID_DATATABLE}`, localParentDoc).remove();
            $(`#${POPUP_ID_DATATABLE}-overlay`, localParentDoc).remove();

            await loadSettings();
            const dataForTableDisplay = JSON.parse(JSON.stringify(currentInfoBarData));
            const currentChanges = await loadLatestChangeset();

            const generateDataTableForPanel = (panelId, panelConfig, data) => {
                if (panelId === 'internet' || panelConfig.isUtilityPanel || !currentSettings[panelId] || !currentSettings[panelId].enabled) return '';
                
                const itemsToDisplay = panelConfig.items.filter(item => currentSettings[panelId].items[item.id]);
                if (itemsToDisplay.length === 0) return '';

                let panelBlockHtml = `<div class="datatable-panel-block">`;
                panelBlockHtml += `<div class="datatable-panel-block-header">${escapeHtml(panelConfig.label)}</div>`;
                panelBlockHtml += `<div class="datatable-panel-block-content">`;

                if (panelId === 'interaction') {
                    const npcKeys = data.npcs ? Object.keys(data.npcs).sort((a,b) => parseInt(a) - parseInt(b)) : [];
                    if (npcKeys.length > 0) {
                        npcKeys.forEach((npcId, index) => {
                            const npcData = data.npcs[npcId] || {};
                            if (index > 0) panelBlockHtml += `<hr style="margin: 15px 0; border-color: var(--infobar-border-color);">`;
                            panelBlockHtml += `<h4 style="text-align:center; margin: 5px 0; color: var(--infobar-section-title);">${escapeHtml(npcData.name || `NPC ${npcId}`)}</h4>`;
                            itemsToDisplay.forEach(item => {
                                const value = npcData[item.id] || '';
                                const dataKey = `npcs.${npcId}.${item.id}`;
                                const isChanged = currentChanges.has(dataKey);
                                panelBlockHtml += `<div class="datatable-kv-row">
                                    <div class="datatable-kv-key">${escapeHtml(item.label)}</div>
                                    <div class="datatable-kv-value ${isChanged ? 'infobar-data-changed' : ''}" contenteditable="true" data-key="${escapeHtml(dataKey)}">${escapeHtml(String(value))}</div>
                                </div>`;
                            });
                        });
                    } else {
                        panelBlockHtml += `<div style="text-align:center; padding: 10px;">当前无NPC数据。</div>`;
                    }
                } else {
                    itemsToDisplay.forEach(item => {
                        const dataKey = `${panelId}.${item.id}`;
                        const value = data[dataKey] || '';
                        const displayValue = (typeof value === 'object' && value !== null) ? JSON.stringify(value, null, 2) : String(value);
                        const isChanged = currentChanges.has(dataKey);
                        panelBlockHtml += `<div class="datatable-kv-row">
                            <div class="datatable-kv-key">${escapeHtml(item.label)}</div>
                            <div class="datatable-kv-value ${isChanged ? 'infobar-data-changed' : ''}" contenteditable="true" data-key="${escapeHtml(dataKey)}">${escapeHtml(displayValue)}</div>
                        </div>`;
                    });
                }

                panelBlockHtml += `</div></div>`;
                return panelBlockHtml;
            };

            let allPanelsHtml = ''; let panelsGenerated = 0;
            for (const panelId in PANEL_CONFIG.panels) { const panelHtml = generateDataTableForPanel(panelId, PANEL_CONFIG.panels[panelId], dataForTableDisplay); if (panelHtml) { allPanelsHtml += panelHtml; panelsGenerated++; } }

            const popupHtml = `<div id="${POPUP_ID_DATATABLE}-overlay"></div><div id="${POPUP_ID_DATATABLE}"><button class="infobar-close-button" title="关闭"><i class="fa-solid fa-times"></i></button><div class="datatable-container"><div class="datatable-panel-wrapper">${panelsGenerated > 0 ? allPanelsHtml : '<p>没有已启用的数据面板可供显示。请在“信息栏设置”中启用相应面板及其子项。</p>'}</div></div></div>`;
            $('body', localParentDoc).append(popupHtml);

            dataTableInstance = $(`#${POPUP_ID_DATATABLE}`, localParentDoc);
            const $overlay = $(`#${POPUP_ID_DATATABLE}-overlay`, localParentDoc);
            const $tableContainer = dataTableInstance.find('.datatable-container');

            const closeDataTablePopup = async () => {
                const lastAiMsgId = await getLastMessageId();
                if (lastAiMsgId !== null) {
                    await saveDataSnapshotToHistory(`msg_${lastAiMsgId}`, JSON.parse(JSON.stringify(currentInfoBarData)));
                    console.log("[高级信息栏设置 v10.0.26] 数据表格关闭，最终数据已保存到最新记录。");
                    await handleMessageRendering(String(lastAiMsgId), true);
                }
                if(dataTableInstance) dataTableInstance.remove();
                $overlay.remove();
                dataTableInstance = null;
            };

            dataTableInstance.find('.infobar-close-button').on('click', closeDataTablePopup);
            $overlay.on('click', closeDataTablePopup);

            $tableContainer.on('blur', '.datatable-kv-value[contenteditable="true"]', async function() {
                const $cell = $(this); const key = $cell.data('key'); let newValue = $cell.text();
                try { const parsed = JSON.parse(newValue); if (typeof parsed === 'object' && parsed !== null) newValue = parsed; } catch(e) { /*保持字符串*/ }
                setNestedValue(currentInfoBarData, key, newValue);
                $cell.removeClass('infobar-data-changed');
                console.log(`[高级信息栏设置 v10.0.26] 数据表格编辑: ${key} = ${newValue} (暂存于 currentInfoBarData)`);
                notifyUser(`数据已在表格中更新: ${key}`, 'info', 1000);
            });
        }, null, 'showDataTablePopup')();
    }

    // 数据历史记录管理 (与V10.0.27保持一致)
    async function loadDataHistory() { if (typeof getVariables !== 'function') return {}; const chatVars = await getVariables({ type: 'chat' }); return chatVars ? (chatVars[CHAT_VAR_KEY_INFOBAR_DATA_HISTORY] || {}) : {}; }
    async function loadDataHistoryEntry(messageIdKey = null) {
        const history = await loadDataHistory();
        if (messageIdKey && history[messageIdKey]) {
            return JSON.parse(JSON.stringify(history[messageIdKey]));
        } else if (!messageIdKey && Object.keys(history).length > 0) {
            const sortedKeys = Object.keys(history).map(k => parseInt(k.replace('msg_',''))).filter(k => !isNaN(k)).sort((a, b) => b - a);
            if (sortedKeys.length > 0) return JSON.parse(JSON.stringify(history[`msg_${sortedKeys[0]}`]));
        }
        return null;
    }
    async function loadDataHistorySnapshot(messageIdKey = null) {
        const entry = await loadDataHistoryEntry(messageIdKey);
        return entry ? entry.snapshot : null;
    }
    async function loadLatestChangeset() {
        const entry = await loadDataHistoryEntry();
        return entry && entry.changes ? new Set(entry.changes) : new Set();
    }
    async function saveDataSnapshotAndChangesToHistory(messageId, dataSnapshot, changesSet) {
        if (typeof getVariables !== 'function' || typeof replaceVariables !== 'function') return;
        let chatVars = await getVariables({ type: 'chat' }) || {};
        if (!chatVars[CHAT_VAR_KEY_INFOBAR_DATA_HISTORY]) {
            chatVars[CHAT_VAR_KEY_INFOBAR_DATA_HISTORY] = {};
        }
        chatVars[CHAT_VAR_KEY_INFOBAR_DATA_HISTORY][messageId] = {
            snapshot: JSON.parse(JSON.stringify(dataSnapshot)),
            changes: Array.from(changesSet || new Set())
        };
        const historyKeys = Object.keys(chatVars[CHAT_VAR_KEY_INFOBAR_DATA_HISTORY]);
        if (historyKeys.length > MAX_DATA_HISTORY_ENTRIES) {
            historyKeys.sort((a, b) => parseInt(a.replace('msg_','')) - parseInt(b.replace('msg_','')));
            const keysToDelete = historyKeys.slice(0, historyKeys.length - MAX_DATA_HISTORY_ENTRIES);
            keysToDelete.forEach(key => delete chatVars[CHAT_VAR_KEY_INFOBAR_DATA_HISTORY][key]);
        }
        await replaceVariables(chatVars, { type: 'chat' });
    }
    async function saveDataSnapshotToHistory(messageId, dataSnapshot) {
        await saveDataSnapshotAndChangesToHistory(messageId, dataSnapshot, new Set());
    }
    async function clearChatVarsData() { if (typeof getVariables !== 'function' || typeof replaceVariables !== 'function') { console.warn("Chat variable functions not available for clearing."); return; } try { let chatVars = await getVariables({ type: 'chat' }) || {}; delete chatVars[CHAT_VAR_KEY_INFOBAR_DATA_HISTORY]; delete chatVars[CHAT_VAR_KEY_AI_PROMPT_DEBUG]; await replaceVariables(chatVars, { type: 'chat' }); currentInfoBarData = { npcs: {} }; latestChangeset.clear(); await initializeOrUpdateWorldInfoEntry('memory'); } catch (e) { console.error("Error clearing chat vars:", e); } }

    // 生成差异集函数 (与V10.0.27保持一致)
    function generateDiff(oldObj, newObj, path = '', diffSet = new Set()) {
        if (oldObj === newObj) return diffSet;
        if (oldObj === null || newObj === null || typeof oldObj !== 'object' || typeof newObj !== 'object') {
            if (JSON.stringify(oldObj) !== JSON.stringify(newObj)) {
                diffSet.add(path);
            }
            return diffSet;
        }

        const oldKeys = Object.keys(oldObj);
        const newKeys = Object.keys(newObj);
        const allKeys = new Set([...oldKeys, ...newKeys]);

        for (const key of allKeys) {
            const currentPath = path ? `${path}.${key}` : key;
            const oldVal = oldObj[key];
            const newVal = newObj[key];

            if (typeof oldVal === 'object' && oldVal !== null && typeof newVal === 'object' && newVal !== null) {
                generateDiff(oldVal, newVal, currentPath, diffSet);
            } else if (JSON.stringify(oldVal) !== JSON.stringify(newVal)) {
                diffSet.add(currentPath);
            }
        }
        return diffSet;
    }

    // 【V10.0.28 升级】解析器 (超级手术刀)
    function parseValue(valueStr) {
        valueStr = (valueStr || "").trim();
        if (valueStr.toLowerCase() === '无' || valueStr === '""' || valueStr === "''") return "无";
        
        let innerContent = valueStr;
        let isQuoted = false;

        if ((valueStr.startsWith('"') && valueStr.endsWith('"')) ||
            (valueStr.startsWith("'") && valueStr.endsWith("'")) ||
            (valueStr.startsWith('`') && valueStr.endsWith('`'))) {
            innerContent = valueStr.substring(1, valueStr.length - 1);
            isQuoted = true;
        }
        
        if ((innerContent.startsWith('[') && innerContent.endsWith(']')) || (innerContent.startsWith('{') && innerContent.endsWith('}'))) {
            try {
                // 1. 替换中文逗号为英文逗号
                let sanitizedContent = innerContent.replace(/，/g, ',');
                // 2. 将所有用作字符串包裹的单引号替换为双引号
                sanitizedContent = sanitizedContent.replace(/'((?:\\.|[^'\\])*)'/g, '"$1"');
                // 3. 尝试处理裸字符串数组，如 [a,b,c] 或 [名字1, 名字2]
                if (sanitizedContent.startsWith('[') && !sanitizedContent.includes(':') && !sanitizedContent.includes('{')) {
                    // 匹配非空白、非引号、非方括号、非逗号的连续字符作为裸字符串
                    sanitizedContent = sanitizedContent.replace(/([^[\],"\s]+)/g, '"$1"');
                }
                return JSON.parse(sanitizedContent);
            } catch (e) {
                console.warn(`[高级信息栏设置 v10.0.28] JSON.parse 解析失败 for: ${innerContent}. 将作为字符串处理。Error: ${e.message}`);
                return innerContent;
            }
        }

        if (isQuoted) {
            return innerContent;
        }
        
        return valueStr;
    }
    function parseCompressedAIDataBlock(dataString) {
        return errorCatched(() => {
            if (!dataString || !currentSettings) {
                console.warn("[高级信息栏设置 v10.0.27] parseCompressedAIDataBlock: dataString 或 currentSettings 为空。");
                return null;
            }
            let updatePatch = {};
            const dataBlock = dataString.trim();
            const allPanelIds = Object.keys(PANEL_CONFIG.panels);
            const panelSplitRegex = new RegExp(`(?:^|\\n)\\s*(${allPanelIds.join('|')}):`, 'g');
            const panelSegments = dataBlock.split(panelSplitRegex);

            for (let i = 1; i < panelSegments.length; i += 2) {
                const panelId = panelSegments[i];
                const panelDataStr = (panelSegments[i + 1] || "").trim();

                if (!currentSettings[panelId] || !currentSettings[panelId].enabled) {
                    continue;
                }
                
                const kvPairRegex = /([\w\.]+)\s*=\s*((["'`])(?:(?=(\\?))\4.)*?\3|\[(?:[^[\]]|\[[^[\]]*\])*\]|\{(?:[^{}]|\{[^{}]*\})*\}|[^,]*?)(?=\s*,\s*[\w\.]+\s*=|\s*$)/g;
                let kvMatch;
                kvPairRegex.lastIndex = 0;
                while ((kvMatch = kvPairRegex.exec(panelDataStr)) !== null) {
                    const key = kvMatch[1];
                    let value = parseValue(kvMatch[2]);

                    if (panelId === 'interaction') {
                        if (!updatePatch.npcs) updatePatch.npcs = {};
                        const npcKeyMatch = key.match(/^(npc(\d+))\.(.+)$/i);
                        if (npcKeyMatch) {
                            const npcIndexStr = npcKeyMatch[2];
                            const propertyName = npcKeyMatch[3];
                            if (!updatePatch.npcs[npcIndexStr]) {
                                updatePatch.npcs[npcIndexStr] = {};
                            }
                            updatePatch.npcs[npcIndexStr][propertyName] = value;
                        } else {
                             console.warn(`[高级信息栏设置 v10.0.27] 在 interaction 面板中发现无效键格式: "${key}"。`);
                        }
                    } else {
                        updatePatch[`${panelId}.${key}`] = value;
                    }
                }
            }
            console.log("[高级信息栏设置 v10.0.27] 解析到的压缩数据更新补丁:", JSON.parse(JSON.stringify(updatePatch)));
            return (Object.keys(updatePatch).length > 0 || (updatePatch.npcs && Object.keys(updatePatch.npcs).length > 0)) ? updatePatch : null;
        }, null, 'parseCompressedAIDataBlock')();
    }
    const setNestedValue = (obj, path, value) => {
        const keys = path.split('.');
        let current = obj;
        for (let i = 0; i < keys.length - 1; i++) {
            const key = keys[i];
            const nextKey = keys[i+1];
            const isNextKeyNumeric = /^\d+$/.test(nextKey);

            if (key === 'npcs') {
                if (!current[key] || typeof current[key] !== 'object' || Array.isArray(current[key])) {
                    current[key] = {};
                }
            } else if (isNextKeyNumeric) {
                 if (!Array.isArray(current[key])) {
                    current[key] = [];
                }
            } else {
                if (!current[key] || typeof current[key] !== 'object' || Array.isArray(current[key])) {
                     current[key] = {};
                }
            }
            current = current[key];
        }
        current[keys[keys.length - 1]] = value;
    };

    // 【V10.0.28 升级】渲染与交互逻辑 (资讯面板占位符)
    function renderNpcDetails(npcData, panelConfig, panelSettings, messageId) { let npcDetailsHtml = ''; if (!npcData) return '<div class="rendered-item">(未选择NPC或无数据)</div>'; for (const itemConfig of panelConfig.items) { if (!panelSettings.items[itemConfig.id]) continue; const value = npcData[itemConfig.id]; if (value !== undefined && value !== null && String(value).trim() !== "") { if ((itemConfig.id === 'pleasureLevel' || itemConfig.id === 'corruptionLevel') && String(value).includes('/')) { const parts = String(value).split('/'); const current = parseInt(parts[0], 10); const max = parseInt(parts[1], 10) || 100; if (!isNaN(current) && !isNaN(max) && max > 0) { const percent = Math.min(100, Math.max(0, (current / max) * 100)); const barColorVar = itemConfig.id === 'pleasureLevel' ? 'var(--infobar-pleasure-fill)' : 'var(--infobar-corruption-fill)'; npcDetailsHtml += `<div class="rendered-item"><span class="rendered-item-label">${escapeHtml(itemConfig.label)}:</span><div class="infobar-progress-bar-container"><div class="infobar-progress-bar-wrapper"><div class="infobar-progress-bar-fill" style="width: ${percent}%; background-color: ${barColorVar};"></div></div><span class="infobar-progress-bar-value">${escapeHtml(String(value))}</span></div></div>`; } else { npcDetailsHtml += `<div class="rendered-item"><span class="rendered-item-label">${escapeHtml(itemConfig.label)}:</span><span class="rendered-item-value">${escapeHtml(String(value))}</span></div>`; } } else { npcDetailsHtml += `<div class="rendered-item"><span class="rendered-item-label">${escapeHtml(itemConfig.label)}:</span><span class="rendered-item-value">${escapeHtml(String(value))}</span></div>`; } } } return npcDetailsHtml || '<div class="rendered-item">(此NPC无启用信息)</div>'; }
    function renderTaskCard(taskData, taskTypeLabel, panelSettings, messageId, taskIndex) {
        if (!taskData || typeof taskData !== 'object' || !taskData.name) {
            console.warn(`[高级信息栏设置 v10.0.27] renderTaskCard: 无效的任务数据或缺少名称 for index ${taskIndex}`, taskData);
            return '';
        }
        const uniqueTaskId = `task-${messageId}-${taskIndex}`;
        let cardHtml = `<div class="infobar-task-card">`;
        let headerContent = `<div class="task-meta">`;
        if (panelSettings.items.showTaskType && (taskData.type || taskTypeLabel)) {
            headerContent += `<span class="task-type">[${escapeHtml(taskData.type || taskTypeLabel)}]</span>`;
        }
        headerContent += `</div><span class="task-title">${escapeHtml(taskData.name)}</span>`;
        if (panelSettings.items.showTaskStatus && taskData.status) {
            headerContent += `<span class="task-status">${escapeHtml(taskData.status)}</span>`;
        }
        cardHtml += `<div class="task-header" data-target-id="${uniqueTaskId}">${headerContent}<i class="fa-solid fa-chevron-down rendered-panel-icon collapsed"></i></div>`;
        cardHtml += `<div class="task-content" id="${uniqueTaskId}">`;
        let contentGenerated = false;
        if (panelSettings.items.showTaskDescription && taskData.description) {
            cardHtml += `<div class="task-description">${escapeHtml(taskData.description)}</div>`;
            contentGenerated = true;
        }
        if (panelSettings.items.showTaskProgress && taskData.progress) {
            let progressPercent = 0;
            let progressText = String(taskData.progress);
            if (progressText.includes('%')) {
                progressPercent = parseInt(progressText, 10);
            } else if (progressText.includes('/')) {
                const parts = progressText.split('/');
                if (parts.length === 2 && !isNaN(parseInt(parts[0])) && !isNaN(parseInt(parts[1])) && parseInt(parts[1]) > 0) {
                    progressPercent = (parseInt(parts[0]) / parseInt(parts[1])) * 100;
                }
            }
            cardHtml += `<div class="task-progress-text">${escapeHtml(progressText)}</div>`;
            if (progressPercent > 0 && progressPercent <= 100) {
                cardHtml += `<div class="task-progress-bar"><div class="task-progress-fill" style="width: ${Math.min(100, progressPercent)}%;"></div></div>`;
            }
            contentGenerated = true;
        }
        if (panelSettings.items.showTaskRewards && taskData.reward) {
            cardHtml += `<div class="task-rewards"><strong>奖励:</strong> ${escapeHtml(taskData.reward)}</div>`;
            contentGenerated = true;
        }
        if (!contentGenerated) {
            cardHtml += `<div class="rendered-item">(此任务无更多详情)</div>`;
        }
        cardHtml += `</div></div>`;
        return cardHtml;
    }
    function renderInternetPost(postData, messageId, postIndex, feedType) {
        if (!postData || typeof postData !== 'object') {
             console.warn(`[高级信息栏设置 v10.0.27] renderInternetPost: 无效的帖子数据 for index ${postIndex}`, postData);
            return '';
        }
        const uniqueId = `${feedType}-${messageId}-${postIndex}`;
        let postHtml = '';

        if (feedType === 'jiuzhouExpress' || feedType === 'localGossip') {
            postHtml += `<div class="infobar-custom-feed-item ${feedType}-item">`;
            let headerText = '';
            let contentText = '';
            if (feedType === 'jiuzhouExpress') {
                headerText = postData.title || postData.headline || '九州快报';
                contentText = postData.content || postData.summary || '(无内容)';
            } else if (feedType === 'localGossip') {
                headerText = postData.overheard_location ? `[${escapeHtml(postData.overheard_location)}] 坊间八卦` : (postData.title || '坊间八卦');
                contentText = postData.gossip_content || postData.content || '(无内容)';
            }
            postHtml += `<div class="custom-feed-header" data-target-id="${uniqueId}"><span class="custom-feed-title">${escapeHtml(headerText)}</span><i class="fa-solid fa-chevron-down feed-expand-icon rendered-panel-icon collapsed"></i></div>`;
            postHtml += `<div class="custom-feed-detail-content" id="${uniqueId}">`;
            let detailContentGenerated = false;
            if (contentText && contentText !== '(无内容)') {
                postHtml += `<div class="custom-feed-content">${escapeHtml(contentText)}</div>`;
                detailContentGenerated = true;
            }
            if (postData.source) {
                postHtml += `<div class="custom-feed-meta">来源: ${escapeHtml(postData.source)}</div>`;
                detailContentGenerated = true;
            }
            if (postData.speaker_description) {
                postHtml += `<div class="custom-feed-meta">说话人: ${escapeHtml(postData.speaker_description)}</div>`;
                detailContentGenerated = true;
            }
             if (postData.timestamp) {
                postHtml += `<div class="custom-feed-meta">时间: ${escapeHtml(postData.timestamp)}</div>`;
                detailContentGenerated = true;
            }
            if (postData.comments && Array.isArray(postData.comments) && postData.comments.length > 0) {
                postHtml += `<div class="post-comments-section"><strong>评论区:</strong>`;
                postData.comments.forEach(comment => {
                    if (comment && typeof comment === 'object' && comment.user && comment.text) {
                        postHtml += `<div class="comment"><div class="comment-author">${escapeHtml(comment.user)}:</div><div class="comment-text">${escapeHtml(comment.text)}</div></div>`;
                    } else if (typeof comment === 'string') {
                         postHtml += `<div class="comment"><div class="comment-text">${escapeHtml(comment)}</div></div>`;
                    }
                });
                postHtml += `</div>`;
                detailContentGenerated = true;
            }
            if (!detailContentGenerated) {
                 postHtml += `<div class="rendered-item">(此条目无更多详情)</div>`;
            }
            postHtml += `</div></div>`;
        } else {
            const postId = `${INTERNET_POST_DETAILS_PREFIX}${messageId}-${postIndex}`;
            postHtml += `<div class="infobar-internet-post">`;
            let headerDisplay = '';
            if(postData.title) headerDisplay += `<span class="post-title">${escapeHtml(postData.title)}</span>`;
            else if(postData.author) headerDisplay += `<span class="post-title">来自 ${escapeHtml(postData.author)} 的帖子</span>`;
            else headerDisplay += `<span class="post-title">帖子 #${postIndex + 1}</span>`;
            postHtml += `<div class="post-header" data-target-id="${postId}">${headerDisplay} <i class="fa-solid fa-chevron-down feed-expand-icon rendered-panel-icon collapsed"></i></div>`;
            postHtml += `<div class="post-detail-content" id="${postId}">`;
            let detailContentGenerated = false;
            if (postData.author || postData.platform || postData.timestamp || postData.source) {
                postHtml += `<div class="post-meta">`;
                if (postData.author) postHtml += `<span>作者: ${escapeHtml(postData.author)}</span> | `;
                if (postData.platform) postHtml += `<span>平台: ${escapeHtml(postData.platform)}</span> | `;
                if (postData.source) postHtml += `<span>来源: ${escapeHtml(postData.source)}</span> | `;
                if (postData.timestamp) postHtml += `<span>时间: ${escapeHtml(postData.timestamp)}</span>`;
                postHtml = postHtml.replace(/ \| $/, '');
                postHtml += `</div>`;
                detailContentGenerated = true;
            }
            if (postData.summary || postData.content) {
                postHtml += `<div class="post-summary">${escapeHtml(postData.summary || postData.content)}</div>`;
                detailContentGenerated = true;
            }
            if (postData.comments && Array.isArray(postData.comments) && postData.comments.length > 0) {
                postHtml += `<div class="post-comments-section"><strong>评论区:</strong>`;
                postData.comments.forEach(comment => {
                    if (comment && typeof comment === 'object' && comment.user && comment.text) {
                        postHtml += `<div class="comment"><div class="comment-author">${escapeHtml(comment.user)}:</div><div class="comment-text">${escapeHtml(comment.text)}</div></div>`;
                    } else if (typeof comment === 'string') {
                         postHtml += `<div class="comment"><div class="comment-text">${escapeHtml(comment)}</div></div>`;
                    }
                });
                postHtml += `</div>`;
                detailContentGenerated = true;
            }
            if (!detailContentGenerated) {
                 postHtml += `<div class="rendered-item">(此帖子无更多详情)</div>`;
            }
            postHtml += `</div></div>`;
        }
        return postHtml;
    }
    function renderInfoBarHTML(dataToRender, messageId) {
        return errorCatched(() => {
            if (!currentSettings || Object.keys(currentSettings).length === 0) { console.warn("[高级信息栏设置 v10.0.27] renderInfoBarHTML: currentSettings 未加载或为空。"); return ''; }
            if (!dataToRender || (Object.keys(dataToRender).length === 0 && (!dataToRender.npcs || Object.keys(dataToRender.npcs).length === 0))) { console.log("[高级信息栏设置 v10.0.27] renderInfoBarHTML: dataToRender 为空。"); return ''; }
            
            let infoBarHtml = `<div class="${RENDERED_INFO_BAR_CLASS}" data-message-id="${messageId}">`;
            let hasAnyContentOverall = false;
            const defaultCollapsed = currentSettings.general?.items?.defaultCollapsed ?? false;

            const listFields = ['learnedSkills', 'specialAbilities', 'inventoryItems', 'keyEvents', 'storyArcs', 'trendingTopics'];
            const formatListValue = (value) => {
                if (Array.isArray(value)) {
                    return value.map(item => escapeHtml(String(item))).join('<br>');
                }
                return String(value).split(',')
                    .map(item => escapeHtml(item.trim()))
                    .filter(Boolean)
                    .join('<br>');
            };

            for (const panelId in PANEL_CONFIG.panels) {
                const panelConfig = PANEL_CONFIG.panels[panelId];
                if (!panelConfig || panelConfig.isUtilityPanel) continue;
                if (!currentSettings[panelId] || !currentSettings[panelId].enabled) { continue; }
                
                let panelItemsHtml = '';
                let hasPanelItemsInSection = false;
                const uniquePanelIdForDOM = `${panelId}-${messageId}`;

                if (panelId === 'interaction') {
                    console.log(`[高级信息栏设置 v10.0.27] 渲染交互面板:`, { npcs: dataToRender.npcs, selectedNpcId: selectedNpcIdForInteractionPanel });
                    if (dataToRender.npcs && Object.keys(dataToRender.npcs).length > 0) {
                        const npcEntries = Object.entries(dataToRender.npcs).filter(([npcKey, npcVal]) => {
                            if (!npcVal || npcVal.isPresent === undefined) return false;
                            const isPresentVal = String(npcVal.isPresent).toLowerCase();
                            return isPresentVal !== 'false' && isPresentVal !== '离开' && isPresentVal !== '不在场' && isPresentVal !== 'no';
                        }).sort(([keyA], [keyB]) => parseInt(keyA) - parseInt(keyB));

                        if (npcEntries.length > 0) {
                            hasPanelItemsInSection = true;
                            const validNpcKeys = npcEntries.map(entry => entry[0]);
                            if (!selectedNpcIdForInteractionPanel || !validNpcKeys.includes(selectedNpcIdForInteractionPanel)) {
                                selectedNpcIdForInteractionPanel = validNpcKeys[0];
                                console.log(`[高级信息栏设置 v10.0.27] selectedNpcIdForInteractionPanel 已重置为第一个有效NPC: ${selectedNpcIdForInteractionPanel}`);
                            }

                            let selectorHtml = `<div class="infobar-npc-selector-wrapper"><label for="${NPC_SELECTOR_ID_PREFIX}${uniquePanelIdForDOM}">选择NPC:</label><select id="${NPC_SELECTOR_ID_PREFIX}${uniquePanelIdForDOM}" class="infobar-npc-selector">`;
                            npcEntries.slice(0, MAX_RENDERED_NPCS_IN_SELECTOR).forEach(([npcKey, npcVal]) => {
                                const npcName = npcVal.name || `NPC ${npcKey}`;
                                const isSelected = (selectedNpcIdForInteractionPanel === npcKey);
                                selectorHtml += `<option value="${escapeHtml(npcKey)}" ${isSelected ? 'selected' : ''}>${escapeHtml(npcName)}</option>`;
                            });
                            selectorHtml += `</select></div>`;
                            panelItemsHtml += selectorHtml;
                            panelItemsHtml += `<div id="${NPC_DETAILS_CONTAINER_ID_PREFIX}${uniquePanelIdForDOM}" class="infobar-npc-details-content">`;
                            if (selectedNpcIdForInteractionPanel && dataToRender.npcs[selectedNpcIdForInteractionPanel]) {
                                panelItemsHtml += renderNpcDetails(dataToRender.npcs[selectedNpcIdForInteractionPanel], panelConfig, currentSettings[panelId], messageId);
                            } else {
                                panelItemsHtml += '<div class="rendered-item">(请选择一个NPC查看详情或无在场NPC)</div>';
                            }
                            panelItemsHtml += `</div>`;
                        } else {
                            panelItemsHtml += '<div class="rendered-item">(当前无在场NPC信息)</div>';
                            hasPanelItemsInSection = true;
                            selectedNpcIdForInteractionPanel = null;
                        }
                    } else {
                        panelItemsHtml += '<div class="rendered-item">(当前无NPC信息)</div>';
                        hasPanelItemsInSection = true;
                        selectedNpcIdForInteractionPanel = null;
                    }
                } else if (panelId === 'tasks') {
                    const taskPanelSettings = currentSettings[panelId];
                    if (taskPanelSettings && taskPanelSettings.enabled) {
                        let taskIndex = 0;
                        let tasksRenderedCount = 0;
                        
                        const processTasks = (taskData, defaultType) => {
                            if (!taskData) return;
                            let quests = [];
                            if (Array.isArray(taskData)) {
                                quests = taskData;
                            } else if (typeof taskData === 'object' && taskData !== null) {
                                quests = [taskData];
                            } else if (typeof taskData === 'string' && taskData.toLowerCase() !== '无') {
                                quests = [{ name: taskData, type: defaultType }];
                            }
                            
                            quests.forEach(quest => {
                                if (typeof quest === 'object' && quest.name) {
                                    panelItemsHtml += renderTaskCard(quest, defaultType, taskPanelSettings, messageId, taskIndex++);
                                    tasksRenderedCount++;
                                }
                            });
                        };

                        if (taskPanelSettings.items.mainQuest) processTasks(dataToRender['tasks.mainQuest'], '主线');
                        if (taskPanelSettings.items.sideQuests) processTasks(dataToRender['tasks.sideQuests'], '支线');
                        
                        if (tasksRenderedCount > 0) hasPanelItemsInSection = true;
                        else { panelItemsHtml += '<div class="rendered-item">(当前无任务信息)</div>'; hasPanelItemsInSection = true; }
                    }
                } else if (panelId === 'internet') {
                    const internetPanelSettings = currentSettings[panelId];
                    if (internetPanelSettings && internetPanelSettings.enabled) {
                        let postIndex = 0;
                        let allFeedItemsHtml = '';
                        let totalFeedItems = 0;
                        const feedTypes = ['socialMediaFeed', 'forumPosts', 'newsHeadlines', 'jiuzhouExpress', 'localGossip'];
                        
                        // 【V10.0.28 升级】遍历所有启用的资讯子项，即使AI没提供数据也显示占位符
                        feedTypes.forEach(feedType => {
                            if (internetPanelSettings.items[feedType]) {
                                const feedDataRaw = dataToRender[`internet.${feedType}`];
                                if (feedDataRaw && String(feedDataRaw).toLowerCase() !== '无') {
                                    let items = [];
                                    if (Array.isArray(feedDataRaw)) {
                                        items = feedDataRaw;
                                    } else if (typeof feedDataRaw === 'object' && feedDataRaw !== null) {
                                        items = [feedDataRaw];
                                    } else if (typeof feedDataRaw === 'string') {
                                        items = [{ content: feedDataRaw, title: feedType }];
                                    }

                                    items.forEach((itemData, idx) => {
                                        const itemHtml = renderInternetPost(itemData, messageId, postIndex++, feedType);
                                        if (itemHtml) {
                                            allFeedItemsHtml += `<div class="infobar-internet-item-wrapper" style="${idx >= MAX_INTERNET_ITEMS_INITIALLY_DISPLAYED ? 'display:none;' : ''}">${itemHtml}</div>`;
                                            totalFeedItems++;
                                        }
                                    });
                                } else {
                                    // 如果AI没提供数据，显示占位符
                                    const feedLabel = panelConfig.items.find(item => item.id === feedType)?.label || feedType;
                                    allFeedItemsHtml += `<div class="rendered-item"><span class="rendered-item-label">${escapeHtml(feedLabel)}:</span><span class="rendered-item-value" style="opacity:0.6;">(无新消息)</span></div>`;
                                }
                                hasPanelItemsInSection = true;
                            }
                        });

                        if (totalFeedItems > MAX_INTERNET_ITEMS_INITIALLY_DISPLAYED) {
                            allFeedItemsHtml += `<button class="infobar-expand-button" data-message-id="${messageId}" data-panel-id="${panelId}">展开更多 (${totalFeedItems - MAX_INTERNET_ITEMS_INITIALLY_DISPLAYED} 条)</button>`;
                        }
                        
                        if (internetPanelSettings.items.trendingTopics) {
                            const trendingValue = dataToRender['internet.trendingTopics'];
                            if (trendingValue && String(trendingValue).trim().toLowerCase() !== '无') {
                                allFeedItemsHtml += `<div class="rendered-item"><span class="rendered-item-label">热门话题:</span><span class="rendered-item-value">${formatListValue(trendingValue)}</span></div>`;
                            } else {
                                allFeedItemsHtml += `<div class="rendered-item"><span class="rendered-item-label">热门话题:</span><span class="rendered-item-value" style="opacity:0.6;">(无新话题)</span></div>`;
                            }
                            hasPanelItemsInSection = true;
                        }
                        
                        panelItemsHtml = allFeedItemsHtml;
                        if (!hasPanelItemsInSection) {
                            panelItemsHtml = '<div class="rendered-item">(资讯面板已启用，但无显示项目)</div>';
                            hasPanelItemsInSection = true;
                        }
                    }
                } else {
                    if (currentSettings[panelId] && currentSettings[panelId].items) {
                        let itemsInThisPanelCount = 0;
                        for (const itemConfig of panelConfig.items) {
                            if (!currentSettings[panelId].items[itemConfig.id]) {
                                continue;
                            }
                            const dataKey = `${panelId}.${itemConfig.id}`;
                            const value = dataToRender[dataKey];
                            if (value !== undefined && value !== null && String(value).trim() !== "" && String(value).trim().toLowerCase() !== '无') {
                                const displayValue = listFields.includes(itemConfig.id) ? formatListValue(value) : escapeHtml(String(value));
                                panelItemsHtml += `<div class="rendered-item"><span class="rendered-item-label">${escapeHtml(itemConfig.label)}:</span><span class="rendered-item-value" style="white-space: pre-wrap;">${displayValue}</span></div>`;
                                itemsInThisPanelCount++;
                            }
                        }
                        if (itemsInThisPanelCount > 0) hasPanelItemsInSection = true;
                        else {
                            panelItemsHtml += '<div class="rendered-item">(当前无此面板信息)</div>';
                            hasPanelItemsInSection = true;
                        }
                    }
                }
                
                if (hasPanelItemsInSection) {
                    hasAnyContentOverall = true;
                    const collapsedStateClass = defaultCollapsed ? 'collapsed' : '';
                    const iconGlyph = defaultCollapsed ? 'fa-chevron-down' : 'fa-chevron-up';
                    infoBarHtml += `<div class="rendered-panel"><div class="rendered-panel-header" data-panel-id="${panelId}"><span class="rendered-panel-title"><i class="fa-solid ${panelConfig.icon || 'fa-info'}"></i> ${escapeHtml(panelConfig.label)}</span><i class="fa-solid ${iconGlyph} rendered-panel-icon ${collapsedStateClass}"></i></div><div class="rendered-panel-content ${defaultCollapsed ? '' : 'expanded'}">${panelItemsHtml}</div></div>`;
                }
            }
            infoBarHtml += `</div>`;
            return hasAnyContentOverall ? infoBarHtml : '';
        }, null, 'renderInfoBarHTML')();
    }

    // 数据直连引擎 (与V10.0.27保持一致)
    async function generateFullDataContextSummary(getContentOnly = false, specificSnapshot = null) {
        return errorCatched(async () => {
            if (!currentSettings.general?.items?.memoryAssistEnabled) {
                if (!getContentOnly) await updateMemoryAssistWIEntry("");
                return "";
            }
            
            let contextStrings = [];
            const memoryAnchorInstruction = "【最优先指令：你的回复必须包含<aiThinkProcess>和<infobar_data>标签。这是绝对规则。】";
            contextStrings.push(memoryAnchorInstruction);
            
            const dataToProcess = specificSnapshot || currentInfoBarData;
            let hasRealData = false;

            for (const panelId in PANEL_CONFIG.panels) {
                const panelConfig = PANEL_CONFIG.panels[panelId];
                if (!panelConfig || panelConfig.isUtilityPanel || !currentSettings[panelId] || !currentSettings[panelId].enabled) continue;

                let panelDataStrings = [];
                if (panelId === 'interaction' && dataToProcess.npcs) {
                    for (const npcId in dataToProcess.npcs) {
                        const npcData = dataToProcess.npcs[npcId];
                        if (!npcData) continue;
                        for (const itemConfig of panelConfig.items) {
                            if (currentSettings[panelId].items[itemConfig.id] && npcData[itemConfig.id] && String(npcData[itemConfig.id]).trim().toLowerCase() !== '无') {
                                const value = npcData[itemConfig.id];
                                const valueStr = typeof value === 'object' ? JSON.stringify(value) : String(value);
                                panelDataStrings.push(`npc${npcId}.${itemConfig.id}="${valueStr}"`);
                            }
                        }
                    }
                } else if (panelId === 'internet') {
                    const internetPanelSettings = currentSettings[panelId];
                    if (internetPanelSettings && internetPanelSettings.enabled) {
                        ['socialMediaFeed', 'forumPosts', 'newsHeadlines', 'jiuzhouExpress', 'localGossip', 'trendingTopics'].forEach(feedType => {
                            const dataKey = `internet.${feedType}`;
                            if (internetPanelSettings.items[feedType] && dataToProcess[dataKey]) {
                                const value = dataToProcess[dataKey];
                                if (value && String(value).trim().toLowerCase() !== '无') {
                                    const valueStr = typeof value === 'object' ? JSON.stringify(value) : String(value);
                                    panelDataStrings.push(`${feedType}="${valueStr}"`);
                                }
                            }
                        });
                    }
                } else {
                    for (const itemConfig of panelConfig.items) {
                        const dataKey = `${panelId}.${itemConfig.id}`;
                        if (currentSettings[panelId].items[itemConfig.id] && dataToProcess[dataKey] && String(dataToProcess[dataKey]).trim().toLowerCase() !== '无') {
                            const value = dataToProcess[dataKey];
                            const valueStr = typeof value === 'object' ? JSON.stringify(value) : String(value);
                            panelDataStrings.push(`${itemConfig.id}="${valueStr}"`);
                        }
                    }
                }

                if (panelDataStrings.length > 0) {
                    hasRealData = true;
                    contextStrings.push(`${panelId}: ${panelDataStrings.join(', ')}`);
                }
            }
            
            let fullContext;
            if (hasRealData) {
                contextStrings.splice(1, 0, "--- 以下为当前世界实时数据 ---");
                fullContext = contextStrings.join('\n');
            } else {
                fullContext = memoryAnchorInstruction + "\n--- (当前无实时数据，请根据剧情生成) ---";
            }

            if (!getContentOnly) {
                await updateMemoryAssistWIEntry(fullContext);
                if (typeof getVariables === 'function' && typeof replaceVariables !== 'function') {
                    try {
                        let chatVars = await getVariables({ type: 'chat' }) || {};
                        chatVars[CHAT_VAR_KEY_AI_PROMPT_DEBUG] = fullContext;
                        await replaceVariables(chatVars, { type: 'chat' });
                    } catch (e) { console.error("保存记忆辅助调试信息到聊天变量时出错:", e); }
                }
            }
            return fullContext;
        }, null, 'generateFullDataContextSummary')();
    }
    async function updateMemoryAssistWIEntry(content) {
        if (!coreApisAreReady || !TavernHelper_API?.getLorebookEntries || !TavernHelper_API?.setLorebookEntries) {
            console.warn("[高级信息栏设置] 无法更新记忆辅助WI条目：API未就绪。");
            return;
        }
        let targetLorebookToUse = TARGET_LOREBOOK_NAME || await TavernHelper_API.getCurrentCharPrimaryLorebook();
        if (!targetLorebookToUse) {
            console.warn("[高级信息栏设置] 无法更新记忆辅助WI条目：未找到目标世界书。");
            return;
        }

        try {
            const allEntries = await TavernHelper_API.getLorebookEntries(targetLorebookToUse);
            if (!allEntries) return;
            const existingEntry = allEntries.find(entry => entry.comment === MEMORY_ASSIST_WI_COMMENT_KEY);

            if (existingEntry) {
                if (existingEntry.content !== content || !existingEntry.enabled) {
                    const updatedEntry = { ...existingEntry, content: content, enabled: true };
                    await TavernHelper_API.setLorebookEntries(targetLorebookToUse, [updatedEntry]);
                    console.log(`[高级信息栏设置] 记忆辅助WI条目已更新。内容长度: ${content.length}`);
                }
            } else {
                const newEntryData = { comment: MEMORY_ASSIST_WI_COMMENT_KEY, content: content, keys: [], enabled: true, type: 'constant', selective: false, position: 'before_character_definition', order: 9999 };
                await TavernHelper_API.createLorebookEntries(targetLorebookToUse, [newEntryData]);
                console.log(`[高级信息栏设置] 已创建记忆辅助WI条目 "${MEMORY_ASSIST_WI_COMMENT_KEY}"。`);
            }
        } catch (error) {
            console.error(`[高级信息栏设置] 更新记忆辅助WI条目时出错:`, error);
        }
    }

    // 状态机与渲染主函数 (与V10.0.27保持一致)
    async function handleMessageRendering(message_id_str, isRecovery = false) {
        await errorCatched(async () => {
            const $messageNode = retrieveDisplayedMessage(message_id_str);
            if (!isRecovery && (!$messageNode || $messageNode.length === 0 || $messageNode.attr('is_user') === 'true')) return;
            
            await loadSettings();
            
            let dataToUseForRendering;
            let newChanges = new Set();

            if (isRecovery) {
                dataToUseForRendering = JSON.parse(JSON.stringify(currentInfoBarData));
                latestChangeset.clear(); 
            } else {
                const oldInfoBarDataForDiff = JSON.parse(JSON.stringify(currentInfoBarData));

                let originalMessageText = '';
                try {
                    const msgIdNum = parseInt(message_id_str, 10);
                    if (isNaN(msgIdNum)) { console.error("[高级信息栏设置 v10.0.27] 无效的消息ID:", message_id_str); return; }
                    const messages = await getChatMessages(`${msgIdNum}-${msgIdNum}`);
                    if (messages && messages.length > 0) {
                        originalMessageText = messages[0].message;
                    }
                } catch (e) {
                    console.warn("[高级信息栏设置 v10.0.27] getChatMessages API失败:", e, "将从DOM中获取文本。");
                    if ($messageNode && $messageNode.length > 0) {
                        const $mTextNode = $messageNode.find('.mes_text').first();
                        if ($mTextNode.length) originalMessageText = $mTextNode.html();
                    }
                }
                if (!originalMessageText) { console.warn("[高级信息栏设置 v10.0.27] 无法获取消息文本:", message_id_str); return; }

                const dataBlockMatch = originalMessageText.match(AI_DATA_BLOCK_REGEX);
                const thinkProcessMatch = originalMessageText.match(AI_THINK_PROCESS_REGEX);

                let mainContent = originalMessageText;
                if (thinkProcessMatch) mainContent = mainContent.replace(AI_THINK_PROCESS_REGEX, '').trim();
                if (dataBlockMatch) mainContent = mainContent.replace(AI_DATA_BLOCK_REGEX, '').trim();
                
                if ($messageNode && $messageNode.length > 0) {
                    const $textNode = $messageNode.find('.mes_text').first();
                    if ($textNode.length) {
                        $textNode.html(mainContent); 
                    }
                }

                if (dataBlockMatch && dataBlockMatch[1]) {
                    const updatePatch = parseCompressedAIDataBlock(dataBlockMatch[1]);
                    if (!updatePatch) {
                        console.log("[高级信息栏设置 v10.0.27] 数据块存在，但解析出的补丁为空。");
                    } else {
                        if (updatePatch.npcs) {
                            if (!currentInfoBarData.npcs) currentInfoBarData.npcs = {};
                            for (const npcIdStr in updatePatch.npcs) {
                                if (!currentInfoBarData.npcs[npcIdStr]) {
                                    currentInfoBarData.npcs[npcIdStr] = {};
                                }
                                Object.assign(currentInfoBarData.npcs[npcIdStr], updatePatch.npcs[npcIdStr]);
                            }
                            delete updatePatch.npcs;
                        }
                        for (const key in updatePatch) {
                            currentInfoBarData[key] = updatePatch[key];
                        }
                        newChanges = generateDiff(oldInfoBarDataForDiff, currentInfoBarData);
                        latestChangeset = new Set(newChanges);
                        console.log("[高级信息栏设置 v10.0.27] 本次更新变更集:", Array.from(newChanges));
                    }
                } else {
                    console.log("[高级信息栏设置 v10.0.27] 本次AI回复未检测到<infobar_data>数据块。变更集将为空。");
                    latestChangeset.clear();
                }
                await saveDataSnapshotAndChangesToHistory(`msg_${message_id_str}`, currentInfoBarData, newChanges);
                dataToUseForRendering = JSON.parse(JSON.stringify(currentInfoBarData));
            }

            if (isRecovery) {
                const snapshotToUse = await loadDataHistorySnapshot(`msg_${message_id_str}`);
                await generateFullDataContextSummary(false, snapshotToUse || currentInfoBarData);
            } else {
                await generateFullDataContextSummary();
            }


            if (!currentSettings.general?.items?.renderInfoBarInChat) {
                console.log("[高级信息栏设置 v10.0.27] 信息栏显示已禁用，跳过渲染。");
                return;
            }

            if ($messageNode && $messageNode.length > 0) {
                $messageNode.find(`.${RENDERED_INFO_BAR_CLASS}`).remove();
            }
            const infoBarHtml = renderInfoBarHTML(dataToUseForRendering, message_id_str);
            if (infoBarHtml) {
                const $infoBarElement = $(infoBarHtml);
                let $targetMessageNode = $messageNode;
                if (isRecovery) {
                    const lastAiMsgId = await getLastMessageId();
                    if (lastAiMsgId) $targetMessageNode = retrieveDisplayedMessage(String(lastAiMsgId));
                }

                if ($targetMessageNode && $targetMessageNode.length > 0) {
                    let $anchor = $targetMessageNode.find('.mes_text').last();
                    if ($anchor.length) $anchor.append($infoBarElement);
                    else $targetMessageNode.append($infoBarElement);
                    
                    $infoBarElement.find('.rendered-panel-header').on('click', function() { $(this).next('.rendered-panel-content').slideToggle(200); $(this).find('.rendered-panel-icon').toggleClass('collapsed fa-chevron-down fa-chevron-up'); });
                    $infoBarElement.find('.task-header, .post-header, .custom-feed-header').on('click', function() { const targetId = $(this).data('target-id'); $infoBarElement.find(`#${targetId}`).slideToggle(200); $(this).find('.rendered-panel-icon, .feed-expand-icon').toggleClass('collapsed fa-chevron-down fa-chevron-up'); });
                    $infoBarElement.find(`select[id^="${NPC_SELECTOR_ID_PREFIX}"]`).on('change', function() { const $selector = $(this), selectedNpcKey = $selector.val(); selectedNpcIdForInteractionPanel = selectedNpcKey; const panelId = 'interaction', uniquePanelIdForDOM = `${panelId}-${message_id_str}`; const $detailsContainer = $infoBarElement.find(`#${NPC_DETAILS_CONTAINER_ID_PREFIX}${uniquePanelIdForDOM}`); if (dataToUseForRendering.npcs && dataToUseForRendering.npcs[selectedNpcKey]) { const npcDetailsHtml = renderNpcDetails(dataToUseForRendering.npcs[selectedNpcKey], PANEL_CONFIG.panels[panelId], currentSettings[panelId], message_id_str); $detailsContainer.html(npcDetailsHtml); } else $detailsContainer.html('<div class="rendered-item">(选择的NPC数据不存在)</div>'); });
                    $infoBarElement.find('.infobar-expand-button').on('click', function() { $(this).closest('.rendered-panel-content').find('.infobar-internet-item-wrapper').show(); $(this).remove(); });
                    const defaultCollapsed = currentSettings.general?.items?.defaultCollapsed ?? false; $infoBarElement.find('.rendered-panel-content').each(function() { const $content = $(this), $header = $content.prev('.rendered-panel-header'); if ($header.length > 0) { const $icon = $header.find('.rendered-panel-icon'); if (defaultCollapsed) { $content.hide(); $icon.removeClass('fa-chevron-up').addClass('fa-chevron-down collapsed'); } else { $content.show(); $icon.removeClass('fa-chevron-down collapsed').addClass('fa-chevron-up'); } } });
                }
            } else if (!isRecovery && currentSettings.general?.items?.autoRenderCheckEnabled) {
                notifyUser('AI已提供信息栏数据，但根据当前设置没有可显示的内容。请检查信息栏设置。', 'warning', 7000);
            }
        }, null, 'handleMessageRendering')();
    }

    // 核心API加载与初始化 (与V10.0.27保持一致)
    function attemptToLoadCoreApis() {
        const parentWin = typeof window.parent !== "undefined" ? window.parent : window;
        SillyTavern_API = (typeof SillyTavern !== 'undefined') ? SillyTavern : parentWin.SillyTavern;
        TavernHelper_API = (typeof TavernHelper !== 'undefined') ? TavernHelper : parentWin.TavernHelper;
        jQuery_API = (typeof $ !== 'undefined') ? $ : parentWin.jQuery;
        toastr_API = parentWin.toastr || (typeof toastr !== 'undefined' ? toastr : null);

        coreApisAreReady = !!(SillyTavern_API && TavernHelper_API && jQuery_API &&
                                TavernHelper_API.getLorebookEntries &&
                                TavernHelper_API.createLorebookEntries &&
                                TavernHelper_API.setLorebookEntries &&
                                TavernHelper_API.getCurrentCharPrimaryLorebook);

        if (coreApisAreReady) {
            console.log(`[高级信息栏设置 ${SCRIPT_VERSION_TAG}] 核心API已成功加载。`);
        } else {
            console.error(`[高级信息栏设置 ${SCRIPT_VERSION_TAG}] 一个或多个核心API (TavernHelper) 加载失败。脚本功能将受限。`);
        }
        return coreApisAreReady;
    }
    let initAttempts = 0;
    const maxInitAttempts = 20;
    const initInterval = 1500;
    let initialSyncDone = false;
    async function performInitialSync() {
        if (initialSyncDone) return;
        await initializeOrUpdateWorldInfoEntry('all');
        const latestHistoryEntry = await loadDataHistoryEntry();
        if (latestHistoryEntry) {
            currentInfoBarData = latestHistoryEntry.snapshot || { npcs: {} };
            latestChangeset = new Set(latestHistoryEntry.changes || []);
        } else {
            currentInfoBarData = { npcs: {} };
            latestChangeset.clear();
        }
        selectedNpcIdForInteractionPanel = null;
        await generateFullDataContextSummary();
        initialSyncDone = true;
        console.log(`[高级信息栏设置 ${SCRIPT_VERSION_TAG}] 首次数据同步完成。`);
    }
    async function mainInitialize() {
        initAttempts++;
        if (attemptToLoadCoreApis()) {
            console.log(`[高级信息栏设置 ${SCRIPT_VERSION_TAG}] 初始化成功！`);
            await loadSettings();
            console.log(`[高级信息栏设置 ${SCRIPT_VERSION_TAG}] 设置已加载。`);
            createMenuButton();
            console.log(`[高级信息栏设置 ${SCRIPT_VERSION_TAG}] 菜单按钮已创建。`);

            if (typeof tavern_events !== 'undefined' && typeof eventOn === 'function') {
                eventOn(tavern_events.APP_READY, async () => {
                    console.log(`[高级信息栏设置 ${SCRIPT_VERSION_TAG}] APP_READY 事件触发，执行首次数据同步。`);
                    await performInitialSync();
                });

                const handleRenderEvent = (messageId) => { if (messageId === undefined || messageId === null) return; const idToUse = (typeof messageId === 'object' && messageId.id) ? messageId.id : messageId; setTimeout(() => handleMessageRendering(String(idToUse)), 250); };
                eventOn(tavern_events.CHARACTER_MESSAGE_RENDERED, handleRenderEvent);
                eventOn(tavern_events.MESSAGE_EDITED, (messageId) => { const idToUse = (typeof messageId === 'object' && messageId.id) ? messageId.id : messageId; const $messageNode = retrieveDisplayedMessage(String(idToUse)); if ($messageNode && $messageNode.length > 0 && $messageNode.attr('is_user') !== 'true') { setTimeout(() => handleMessageRendering(String(idToUse)), 250); } });
                
                eventOn(tavern_events.MESSAGE_DELETED, async (deletedMessageIdObj) => {
                    const deletedMessageId = (typeof deletedMessageIdObj === 'object' && deletedMessageIdObj.id !== undefined) ? String(deletedMessageIdObj.id) : String(deletedMessageIdObj);
                    console.log(`[高级信息栏设置 v10.0.27] 消息 ${deletedMessageId} 已被删除。开始数据回滚...`);
                    
                    $(`#chat .${RENDERED_INFO_BAR_CLASS}[data-message-id="${deletedMessageId}"]`).remove();
                    
                    if (typeof getVariables === 'function' && typeof replaceVariables !== 'function') {
                        try {
                            let chatVars = await getVariables({ type: 'chat' }) || {};
                            if (chatVars[CHAT_VAR_KEY_INFOBAR_DATA_HISTORY] && chatVars[CHAT_VAR_KEY_INFOBAR_DATA_HISTORY][`msg_${deletedMessageId}`]) {
                                delete chatVars[CHAT_VAR_KEY_INFOBAR_DATA_HISTORY][`msg_${deletedMessageId}`];
                                await replaceVariables(chatVars, { type: 'chat' });
                                console.log(`[高级信息栏设置 v10.0.27] 已从历史记录中删除消息 ${deletedMessageId} 的数据条目。`);
                            }
                        } catch (e) { console.error("[高级信息栏设置 v10.0.27] 从历史记录中删除条目时出错:", e); }
                    }

                    const history = await loadDataHistory();
                    const sortedKeys = Object.keys(history)
                        .map(k => parseInt(k.replace('msg_','')))
                        .filter(k => !isNaN(k) && k < parseInt(deletedMessageId))
                        .sort((a, b) => b - a);

                    let rolledBack = false;
                    let snapshotForMemoryAssist = null;

                    if (sortedKeys.length > 0) {
                        const previousSnapshotKey = `msg_${sortedKeys[0]}`;
                        const previousEntry = history[previousSnapshotKey];
                        currentInfoBarData = previousEntry ? (previousEntry.snapshot || { npcs: {} }) : { npcs: {} };
                        latestChangeset = previousEntry ? new Set(previousEntry.changes || []) : new Set();
                        snapshotForMemoryAssist = currentInfoBarData;
                        console.log(`[高级信息栏设置 v10.0.27] 数据成功回溯到消息 ${sortedKeys[0]} 的状态。`);
                        rolledBack = true;
                    } else {
                        currentInfoBarData = { npcs: {} };
                        latestChangeset.clear();
                        snapshotForMemoryAssist = {};
                        console.log(`[高级信息栏设置 v10.0.27] 无更早历史数据可回溯，重置为初始状态。`);
                        rolledBack = true;
                    }
                    
                    if (rolledBack) {
                        selectedNpcIdForInteractionPanel = null;
                        const memoryContent = await generateFullDataContextSummary(true, snapshotForMemoryAssist);
                        await updateMemoryAssistWIEntry(memoryContent);
                        
                        const lastAiMsgId = await getLastMessageId();
                        if (lastAiMsgId !== null) {
                            const $lastMessageNode = retrieveDisplayedMessage(String(lastAiMsgId));
                            if ($lastMessageNode && $lastMessageNode.length > 0) {
                                $lastMessageNode.find(`.${RENDERED_INFO_BAR_CLASS}`).remove();
                            }
                            console.log(`[高级信息栏设置 v10.0.27] 尝试为最后一条AI消息 ${lastAiMsgId} 重新渲染信息栏以反映回滚。`);
                            await handleMessageRendering(String(lastAiMsgId), true);
                        } else {
                            console.log("[高级信息栏设置 v10.0.27] 删除后未找到有效的最后AI消息ID，不重渲染UI。");
                        }
                    }
                });

                const chatLoadEventName = tavern_events.CHAT_LOADED ? 'CHAT_LOADED' : (tavern_events.CHAT_CHANGED ? 'CHAT_CHANGED' : null);
                if(chatLoadEventName) {
                    eventOn(tavern_events[chatLoadEventName], async () => {
                        console.log(`[高级信息栏设置 v10.0.27] 事件 ${chatLoadEventName} 触发，重新同步设置与数据。`);
                        initialSyncDone = false;
                        await loadSettings();
                        await performInitialSync();
                        const lastAiMsgId = await getLastMessageId();
                        if (lastAiMsgId !== null) {
                             console.log(`[高级信息栏设置 v10.0.27] 聊天加载后，为最后一条AI消息 ${lastAiMsgId} 渲染信息栏。`);
                             await handleMessageRendering(String(lastAiMsgId));
                        }
                    });
                }
                eventOn(tavern_events.MESSAGE_SENT, async (msgObj) => {
                    if (msgObj && msgObj.is_user) {
                        console.log(`[高级信息栏设置 v10.0.27] 用户消息发送，准备更新记忆辅助WI。`);
                        setTimeout(() => {
                            generateFullDataContextSummary();
                        }, 100);
                    }
                });

                eventOn(tavern_events.GENERATE_AFTER_COMBINE_PROMPTS, (data) => {
                    const finalCommand = "\n\n【最终指令】在你的回复末尾，你必须严格按照格式输出 <aiThinkProcess> 和 <infobar_data> 标签。这是强制性要求，不得遗漏。";
                    
                    if (data && typeof data.prompt === 'string') {
                        data.prompt += finalCommand;
                        console.log(`[高级信息栏设置 v10.0.27] 已注入最终指令。`);
                    } else {
                        console.warn(`[高级信息栏设置 v10.0.27] 无法注入最终指令，prompt数据结构未知。`, data);
                    }
                    
                    return data;
                });

                eventOn(tavern_events.MESSAGE_RECEIVED, (data) => {
                    if (data && typeof data.message === 'string' && data.is_user === false) {
                        const hasThinkProcess = AI_THINK_PROCESS_REGEX.test(data.message);
                        const hasInfoBarData = AI_DATA_BLOCK_REGEX.test(data.message);

                        if (!hasThinkProcess || !hasInfoBarData) {
                            let correctedMessage = data.message;
                            if (!hasThinkProcess) {
                                correctedMessage += `\n<aiThinkProcess>\n[系统警告]：你的上一条回复严重违反了【绝对核心指令：输出结构铁则】。你遗漏了<aiThinkProcess>和<infobar_data>标签。这导致了数据流中断。在本次回复中，你【必须】重新分析全部上下文，并生成完整的思考过程和数据块。任何再次的遗漏都将被视为任务失败。\n</aiThinkProcess>`;
                                notifyUser("AI未生成思考过程，已自动修正。", "warning", 4000);
                            }
                            if (!hasInfoBarData) {
                                correctedMessage += `\n<infobar_data>\npersonal: thoughts="[系统修正]：AI未提供数据更新。"\n</infobar_data>`;
                                notifyUser("AI未生成数据块，已自动修正。", "warning", 4000);
                            }
                            data.message = correctedMessage;
                            console.log(`[高级信息栏设置 v10.0.27] 格式守护神已修正AI输出。`);
                        }
                    }
                    return data;
                });

            } else {
                console.warn(`[高级信息栏设置 ${SCRIPT_VERSION_TAG}] SillyTavern 事件系统 (tavern_events) 未完全可用。`);
                $(document).ready(function() {
                    setTimeout(async () => {
                        await loadSettings();
                        await performInitialSync();
                        const lastAiMsgId = await getLastMessageId();
                        if (lastAiMsgId !== null) {
                             await handleMessageRendering(String(lastAiMsgId));
                        }
                    }, 3000);
                });
            }
        } else if (initAttempts < maxInitAttempts) {
            console.log(`[高级信息栏设置 ${SCRIPT_VERSION_TAG}] 核心API未就绪，正在重试... (尝试 ${initAttempts})`);
            setTimeout(mainInitialize, initInterval);
        } else {
            console.error(`[高级信息栏设置 ${SCRIPT_VERSION_TAG}] 多次尝试后初始化失败。`);
            notifyUser("信息栏脚本初始化失败：核心API加载失败。", "error", 10000);
        }
    }
    
    $(document).ready(function() {
        setTimeout(mainInitialize, 2000);
    });

})();
