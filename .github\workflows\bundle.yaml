name: bundle

on:
  push:
    branches:
      - master
      - main
    paths-ignore:
      - dist/**
  workflow_dispatch:

permissions:
  actions: read
  contents: write

concurrency:
  group: ${{ github.workflow }}

jobs:
  bundle:
    if: github.repository != 'StageDog/tavern_helper_template'
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: JesseTG/rm@v1.0.3
        with:
          path: dist

      - name: Use Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 22
      - name: Setup pnpm
        uses: pnpm/action-setup@v4.1.0
        with:
          version: 10

      - run: pnpm install && pnpm format && pnpm build
      - name: Commit changes
        uses: EndBug/add-and-commit@v9.1.3
        with:
          default_author: github_actions
          message: '[bot] bundle'

      # 自动打版本 tag, 从而让 jsdelivr 基于版本号在 12h 内更新缓存而不是用非版本号的 7d 缓存
      - id: autotag
        uses: phish108/autotag-action@v1.1.64
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          with-v: true
      - if: ${{ steps.autotag.outputs.tag != '0.0.0' }}
        run: git push --delete origin ${{ steps.autotag.outputs.tag }}
