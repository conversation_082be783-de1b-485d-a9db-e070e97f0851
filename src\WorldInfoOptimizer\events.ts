/**
 * 世界书优化器事件处理模块
 * 包含所有事件处理函数和事件绑定逻辑
 */

import { TavernAPI, loadAllData } from './api';
import {
  appState,
  hydrateAppState,
  safeGetLorebookEntries,
  toggleMultiSelectMode,
  toggleSelectedItem,
} from './state';
import {
  hidePanel,
  renderContent,
  showModal,
  showPanel,
  showProgressToast,
  showSuccessTick,
  updateItemSelectionView,
  updateMultiSelectModeView,
  updateSelectionCount,
} from './ui';
import { debounce, errorCatched } from './utils';
import { parseItemKey, createBookItemKey, createEntryItemKey, createRegexItemKey } from './keyManager';
import { ItemTypePrefix } from './constants';
import type { IBookEntryId } from './types';

// --- 策略地图 ---

/**
 * 批量启用操作策略地图
 */
const batchEnableOperations: Record<ItemTypePrefix, (parsedKey: IBookEntryId) => Promise<boolean>> = {
  [ItemTypePrefix.BOOK]: async (parsedKey: IBookEntryId): Promise<boolean> => {
    if (!parsedKey.bookName) return false;

    // 更新本地状态
    const book = appState.allLorebooks.find(b => b.name === parsedKey.bookName);
    if (book) {
      book.enabled = true;
    }

    // 更新全局设置并调用API
    try {
      const globalSettings = await TavernAPI.getLorebookSettings();
      if (!globalSettings.world_info_include) {
        globalSettings.world_info_include = [];
      }
      if (!globalSettings.world_info_include.includes(parsedKey.bookName)) {
        globalSettings.world_info_include.push(parsedKey.bookName);
        await TavernAPI.setLorebookSettings(globalSettings);
      }
      return true;
    } catch (error) {
      console.error('[WorldInfoOptimizer] Error enabling book:', error);
      return false;
    }
  },

  [ItemTypePrefix.ENTRY]: async (parsedKey: IBookEntryId): Promise<boolean> => {
    if (!parsedKey.bookName || !parsedKey.entryId) return false;

    const entries = safeGetLorebookEntries(parsedKey.bookName);
    const entry = entries.find(e => e.uid === parsedKey.entryId);
    if (entry) {
      entry.enabled = true;
      try {
        await TavernAPI.setLorebookEntries(parsedKey.bookName, [{ uid: parsedKey.entryId, enabled: true }]);
        return true;
      } catch (error) {
        console.error('[WorldInfoOptimizer] Error enabling entry:', error);
        return false;
      }
    }
    return false;
  },

  [ItemTypePrefix.REGEX]: async (_parsedKey: IBookEntryId): Promise<boolean> => {
    // 正则表达式不支持启用/禁用操作
    return false;
  },
};

/**
 * 批量禁用操作策略地图
 */
const batchDisableOperations: Record<ItemTypePrefix, (parsedKey: IBookEntryId) => Promise<boolean>> = {
  [ItemTypePrefix.BOOK]: async (parsedKey: IBookEntryId): Promise<boolean> => {
    if (!parsedKey.bookName) return false;

    // 更新本地状态
    const book = appState.allLorebooks.find(b => b.name === parsedKey.bookName);
    if (book) {
      book.enabled = false;
    }

    // 更新全局设置并调用API
    try {
      const globalSettings = await TavernAPI.getLorebookSettings();
      if (globalSettings.world_info_include) {
        globalSettings.world_info_include = globalSettings.world_info_include.filter(
          (name: string) => name !== parsedKey.bookName
        );
        await TavernAPI.setLorebookSettings(globalSettings);
      }
      return true;
    } catch (error) {
      console.error('[WorldInfoOptimizer] Error disabling book:', error);
      return false;
    }
  },

  [ItemTypePrefix.ENTRY]: async (parsedKey: IBookEntryId): Promise<boolean> => {
    if (!parsedKey.bookName || !parsedKey.entryId) return false;

    const entries = safeGetLorebookEntries(parsedKey.bookName);
    const entry = entries.find(e => e.uid === parsedKey.entryId);
    if (entry) {
      entry.enabled = false;
      try {
        await TavernAPI.setLorebookEntries(parsedKey.bookName, [{ uid: parsedKey.entryId, enabled: false }]);
        return true;
      } catch (error) {
        console.error('[WorldInfoOptimizer] Error disabling entry:', error);
        return false;
      }
    }
    return false;
  },

  [ItemTypePrefix.REGEX]: async (_parsedKey: IBookEntryId): Promise<boolean> => {
    // 正则表达式不支持启用/禁用操作
    return false;
  },
};

/**
 * 批量删除操作策略地图
 */
const batchDeleteOperations: Record<ItemTypePrefix, (parsedKey: IBookEntryId) => Promise<boolean>> = {
  [ItemTypePrefix.BOOK]: async (parsedKey: IBookEntryId): Promise<boolean> => {
    if (!parsedKey.bookName) return false;

    try {
      await TavernAPI.deleteLorebook(parsedKey.bookName);
      // 从本地状态移除
      appState.allLorebooks = appState.allLorebooks.filter(b => b.name !== parsedKey.bookName);
      appState.lorebooks.character = appState.lorebooks.character.filter(b => b !== parsedKey.bookName);
      if (appState.chatLorebook === parsedKey.bookName) {
        appState.chatLorebook = null;
      }
      return true;
    } catch (error) {
      console.error('[WorldInfoOptimizer] Error deleting book:', error);
      return false;
    }
  },

  [ItemTypePrefix.ENTRY]: async (parsedKey: IBookEntryId): Promise<boolean> => {
    if (!parsedKey.bookName || !parsedKey.entryId) return false;

    try {
      await TavernAPI.deleteLorebookEntry(parsedKey.bookName, parseInt(parsedKey.entryId));
      // 从本地状态移除
      const entries = safeGetLorebookEntries(parsedKey.bookName);
      const index = entries.findIndex(e => e.uid === parsedKey.entryId);
      if (index > -1) {
        entries.splice(index, 1);
      }
      return true;
    } catch (error) {
      console.error('[WorldInfoOptimizer] Error deleting entry:', error);
      return false;
    }
  },

  [ItemTypePrefix.REGEX]: async (_parsedKey: IBookEntryId): Promise<boolean> => {
    // 正则表达式不支持批量删除操作（出于保护考虑）
    return false;
  },
};

// --- 全局变量 ---
let parentWin: any;
let $: any;

/**
 * 设置全局依赖
 */
export const setGlobalDependencies = (jquery: any, parentWindow: any) => {
  $ = jquery;
  parentWin = parentWindow;
};

// --- 辅助函数 ---

/**
 * 获取项目键
 */
const getItemKey = ($item: any): string | null => {
  const isGlobalLoreTab = appState.activeTab === 'global-lore';
  const isBookHeader = $item.hasClass('wio-book-group');

  if (isGlobalLoreTab && isBookHeader) {
    const isEditingEntries = $item.hasClass('editing-entries');
    if (isEditingEntries) return null;

    const bookName = $item.data('book-name');
    return bookName ? createBookItemKey(bookName) : null;
  }

  const type = $item.data('type');
  const id = $item.data('id');
  const bookName = $item.data('book-name');

  if (type === 'lore' && id && bookName) {
    return createEntryItemKey(bookName, id);
  } else if (type === 'regex' && id) {
    const scope = appState.activeTab === 'global-regex' ? 'global' : 'character';
    return createRegexItemKey(scope, id);
  }

  return null;
};

/**
 * 检查项目是否可选择
 */
const canSelectItem = ($item: any): boolean => {
  const isGlobalLoreTab = appState.activeTab === 'global-lore';
  const isBookHeader = $item.hasClass('wio-book-group');

  if (isGlobalLoreTab && isBookHeader) {
    return !$item.hasClass('editing-entries');
  }

  return true;
};

// --- 核心事件处理函数 ---

/**
 * 处理头部点击事件
 */
const handleHeaderClick = errorCatched(async (event: any) => {
  const $target = $(event.target);
  const $container = $(event.currentTarget).closest('.wio-item-container, .wio-book-group');

  // 如果点击的是按钮等可交互控件，则不执行后续逻辑
  if ($target.closest('.wio-item-controls, .wio-rename-ui').length > 0) {
    return;
  }

  // 多选模式下的选择逻辑
  if (appState.multiSelectMode) {
    const itemKey = getItemKey($container);
    if (itemKey && canSelectItem($container)) {
      const isSelected = toggleSelectedItem(itemKey);
      updateItemSelectionView(itemKey, isSelected);
      updateSelectionCount(appState.selectedItems.size);
    }
    return;
  }

  // 普通模式下的展开/折叠逻辑
  const $content = $container.find('.wio-collapsible-content').first();
  if ($content.length > 0) {
    const isCollapsed = $container.hasClass('collapsed');
    if (isCollapsed) {
      $content.slideDown(200);
      $container.removeClass('collapsed');
    } else {
      $content.slideUp(200);
      $container.addClass('collapsed');
    }
  }
}, 'handleHeaderClick');

/**
 * 处理状态切换事件
 */
const handleToggleState = errorCatched(async (event: any) => {
  event.stopPropagation();
  const $button = $(event.currentTarget);
  const $elementToSort = $button.closest('.wio-book-group, .wio-item-container');
  if ($elementToSort.hasClass('renaming')) return;

  const type = $elementToSort.data('type') || 'book';
  const id = $elementToSort.data('id');
  const bookName = $elementToSort.data('book-name');

  try {
    if (type === 'book') {
      // 切换世界书状态
      const book = appState.allLorebooks.find(b => b.name === bookName);
      if (book) {
        const newState = !book.enabled;
        book.enabled = newState;

        // 更新UI
        const $icon = $button.find('i');
        $icon.removeClass('fa-eye fa-eye-slash');
        $icon.addClass(newState ? 'fa-eye' : 'fa-eye-slash');
        $button.attr('title', newState ? '禁用' : '启用');

        showSuccessTick(`世界书已${newState ? '启用' : '禁用'}`);
      }
    } else if (type === 'lore') {
      // 切换条目状态
      const entries = safeGetLorebookEntries(bookName);
      const entry = entries.find(e => e.uid === id);
      if (entry) {
        const newState = !entry.enabled;
        entry.enabled = newState;

        // 更新到服务器
        await TavernAPI.setLorebookEntries(bookName, [{ uid: id, enabled: newState }]);

        // 更新UI
        const $icon = $button.find('i');
        $icon.removeClass('fa-eye fa-eye-slash');
        $icon.addClass(newState ? 'fa-eye' : 'fa-eye-slash');
        $button.attr('title', newState ? '禁用' : '启用');

        showSuccessTick(`条目已${newState ? '启用' : '禁用'}`);
      }
    }
  } catch (error) {
    console.error('[WorldInfoOptimizer] Error toggling state:', error);
    showModal({ type: 'alert', title: '错误', text: '状态切换失败，请重试。' });
  }
}, 'handleToggleState');

/**
 * 绑定所有事件处理器
 */
export const bindEventHandlers = (): void => {
  const parentDoc = parentWin.document;

  // 扩展菜单按钮点击事件
  $(parentDoc).on('click', `#world-info-optimizer-button`, async () => {
    const $panel = $(`#world-info-optimizer-panel`, parentDoc);
    if ($panel.is(':visible')) {
      hidePanel();
    } else {
      await showPanel(async () => {
        try {
          const data = await loadAllData({ $: $, parentWin: parentWin });
          hydrateAppState(data);
          renderContent();
        } catch (error) {
          console.error('[WorldInfoOptimizer] Failed to load data on panel show:', error);
        }
      });
    }
  });

  // 面板关闭按钮
  $(parentDoc).on('click', '#wio-close-btn', () => {
    hidePanel();
  });

  // 搜索输入框事件
  const debouncedSearch = debounce(() => {
    renderContent();
  }, 300);

  $(parentDoc).on('input', `#wio-search-input`, debouncedSearch);
  $(parentDoc).on('click', '#wio-clear-search-btn', () => {
    $(`#wio-search-input`, parentDoc).val('').trigger('input');
  });

  // 搜索过滤器复选框
  $(parentDoc).on('change', '#wio-filter-book-name', (e: any) => {
    appState.searchFilters.bookName = e.target.checked;
    renderContent();
  });
  $(parentDoc).on('change', '#wio-filter-entry-name', (e: any) => {
    appState.searchFilters.entryName = e.target.checked;
    renderContent();
  });
  $(parentDoc).on('change', '#wio-filter-keywords', (e: any) => {
    appState.searchFilters.keywords = e.target.checked;
    renderContent();
  });
  $(parentDoc).on('change', '#wio-filter-content', (e: any) => {
    appState.searchFilters.content = e.target.checked;
    renderContent();
  });

  // 刷新按钮
  $(parentDoc).on('click', `#wio-refresh-btn`, () => {
    loadAllData({ $: $, parentWin: parentWin })
      .then(data => {
        hydrateAppState(data);
        renderContent();
      })
      .catch(error => {
        console.error('[WorldInfoOptimizer] Failed to refresh data:', error);
      });
  });

  // 标签页切换
  $(parentDoc).on('click', `.wio-tab-btn`, (event: any) => {
    const $this = $(event.currentTarget);
    const tabId = $this.data('tab');

    $(`.wio-tab-btn`, parentDoc).removeClass('active');
    $this.addClass('active');

    appState.activeTab = tabId;
    renderContent();
  });

  // 多选模式切换
  $(parentDoc).on('click', '#wio-multi-select-toggle', () => {
    const isEnabled = toggleMultiSelectMode();
    updateMultiSelectModeView(isEnabled);
  });

  // 批量操作按钮
  $(parentDoc).on('click', '#wio-select-all-btn', handleSelectAll);
  $(parentDoc).on('click', '#wio-deselect-all-btn', handleDeselectAll);
  $(parentDoc).on('click', '#wio-batch-enable-btn', handleBatchEnable);
  $(parentDoc).on('click', '#wio-batch-disable-btn', handleBatchDisable);
  $(parentDoc).on('click', '#wio-batch-delete-btn', handleBatchDelete);

  // 折叠按钮
  $(parentDoc).on('click', `#wio-collapse-all-btn`, handleCollapseAll);
  $(parentDoc).on('click', `#wio-collapse-current-btn`, handleCollapseCurrent);

  // 动态事件绑定
  $(parentDoc).on('click', '.wio-item-header', handleHeaderClick);
  $(parentDoc).on('click', '.wio-toggle-state', handleToggleState);
};

// --- 批量操作处理函数 ---

/**
 * 处理全选
 */
const handleSelectAll = errorCatched(async () => {
  const parentDoc = parentWin.document;
  const $visibleItems = $(
    `#world-info-optimizer-panel .wio-item-container:visible, #world-info-optimizer-panel .wio-book-group:visible`,
    parentDoc,
  );

  $visibleItems.each((_: any, element: any) => {
    const $item = $(element);
    const itemKey = getItemKey($item);
    if (itemKey && canSelectItem($item)) {
      appState.selectedItems.add(itemKey);
      $item.addClass('selected');
    }
  });

  updateSelectionCount(appState.selectedItems.size);
  showSuccessTick('已全选可见项目');
}, 'handleSelectAll');

/**
 * 处理取消全选
 */
const handleDeselectAll = errorCatched(async () => {
  const parentDoc = parentWin.document;
  appState.selectedItems.clear();
  $(`#world-info-optimizer-panel .selected`, parentDoc).removeClass('selected');
  updateSelectionCount(appState.selectedItems.size);
  showSuccessTick('已取消全选');
}, 'handleDeselectAll');

/**
 * 处理批量启用
 */
const handleBatchEnable = errorCatched(async () => {
  const selectedItems = Array.from(appState.selectedItems);
  if (selectedItems.length === 0) {
    await showModal({ type: 'alert', title: '提示', text: '请先选择要启用的项目。' });
    return;
  }

  const progressToast = showProgressToast('正在批量启用...');
  let successCount = 0;

  try {
    for (const itemKey of selectedItems) {
      try {
        const parsedKey = parseItemKey(itemKey);
        const operation = batchEnableOperations[parsedKey.type as ItemTypePrefix];
        if (operation) {
          const success = await operation(parsedKey);
          if (success) successCount++;
        }
      } catch (error) {
        console.error('[WorldInfoOptimizer] Error parsing item key in batch enable:', error, itemKey);
      }
    }

    progressToast.remove();
    showSuccessTick(`成功启用 ${successCount} 个项目`);
    appState.selectedItems.clear();
    renderContent();
  } catch (error) {
    progressToast.remove();
    console.error('[WorldInfoOptimizer] Error in batch enable:', error);
    showModal({ type: 'alert', title: '错误', text: '批量启用操作失败。' });
  }
}, 'handleBatchEnable');

/**
 * 处理批量禁用
 */
const handleBatchDisable = errorCatched(async () => {
  const selectedItems = Array.from(appState.selectedItems);
  if (selectedItems.length === 0) {
    await showModal({ type: 'alert', title: '提示', text: '请先选择要禁用的项目。' });
    return;
  }

  const progressToast = showProgressToast('正在批量禁用...');
  let successCount = 0;

  try {
    for (const itemKey of selectedItems) {
      try {
        const parsedKey = parseItemKey(itemKey);
        const operation = batchDisableOperations[parsedKey.type as ItemTypePrefix];
        if (operation) {
          const success = await operation(parsedKey);
          if (success) successCount++;
        }
      } catch (error) {
        console.error('[WorldInfoOptimizer] Error parsing item key in batch disable:', error, itemKey);
      }
    }

    progressToast.remove();
    showSuccessTick(`成功禁用 ${successCount} 个项目`);
    appState.selectedItems.clear();
    renderContent();
  } catch (error) {
    progressToast.remove();
    console.error('[WorldInfoOptimizer] Error in batch disable:', error);
    showModal({ type: 'alert', title: '错误', text: '批量禁用操作失败。' });
  }
}, 'handleBatchDisable');

/**
 * 处理批量删除
 */
export const handleBatchDelete = errorCatched(async () => {
  const selectedItems = Array.from(appState.selectedItems);
  if (selectedItems.length === 0) {
    await showModal({ type: 'alert', title: '提示', text: '请先选择要删除的项目。' });
    return;
  }

  // 显示确认对话框
  try {
    await showModal({
      type: 'confirm',
      title: '确认批量删除',
      text: `确定要删除 ${selectedItems.length} 个选中的项目吗？此操作无法撤销。`,
    });

    const progressToast = showProgressToast('正在批量删除...');
    let successCount = 0;

    // 按类型对项目进行分组
    const booksToDelete: string[] = [];
    const entriesToDelete = new Map<string, number[]>();

    for (const itemKey of selectedItems) {
      try {
        const parsedKey = parseItemKey(itemKey);
        if (parsedKey.type === ItemTypePrefix.BOOK && parsedKey.bookName) {
          booksToDelete.push(parsedKey.bookName);
        } else if (parsedKey.type === ItemTypePrefix.ENTRY && parsedKey.bookName && parsedKey.entryId) {
          if (!entriesToDelete.has(parsedKey.bookName)) {
            entriesToDelete.set(parsedKey.bookName, []);
          }
          entriesToDelete.get(parsedKey.bookName)!.push(parseInt(parsedKey.entryId));
        }
      } catch (error) {
        console.error('[WorldInfoOptimizer] Error parsing item key in batch delete:', error, itemKey);
      }
    }

    // 批量删除书籍
    for (const bookName of booksToDelete) {
      try {
        const operation = batchDeleteOperations[ItemTypePrefix.BOOK];
        if (await operation({ type: ItemTypePrefix.BOOK, bookName })) {
          successCount++;
        }
      } catch (error) {
        console.error(`[WorldInfoOptimizer] Failed to delete book ${bookName}:`, error);
      }
    }

    // 批量删除条目
    for (const [bookName, uids] of entriesToDelete.entries()) {
      try {
        await TavernAPI.deleteLorebookEntries(bookName, uids);
        // 更新本地状态
        const entries = safeGetLorebookEntries(bookName);
        const uidSet = new Set(uids);
        const remainingEntries = entries.filter(e => !uidSet.has(Number(e.uid)));
        appState.lorebookEntries.set(bookName, remainingEntries);
        successCount += uids.length;
      } catch (error) {
        console.error(`[WorldInfoOptimizer] Failed to delete entries from ${bookName}:`, error);
      }
    }

    progressToast.remove();
    showSuccessTick(`成功删除 ${successCount} 个项目`);
    appState.selectedItems.clear();
    renderContent();
  } catch {
    // 用户取消操作
  }
}, 'handleBatchDelete');

/**
 * 处理全部折叠
 */
const handleCollapseAll = errorCatched(async () => {
  const parentDoc = parentWin.document;
  const $allCollapsible = $(`#world-info-optimizer-panel .wio-collapsible-content`, parentDoc);

  $allCollapsible.slideUp(200);
  $allCollapsible.closest('.wio-item-container, .wio-book-group').addClass('collapsed');
  showSuccessTick('已折叠所有项目');
}, 'handleCollapseAll');

/**
 * 处理当前标签页折叠
 */
const handleCollapseCurrent = errorCatched(async () => {
  const parentDoc = parentWin.document;
  const activeTab = appState.activeTab;
  const $currentTabContent = $(
    `#world-info-optimizer-panel .wio-tab-content[data-tab="${activeTab}"] .wio-collapsible-content`,
    parentDoc,
  );

  $currentTabContent.slideUp(200);
  $currentTabContent.closest('.wio-item-container, .wio-book-group').addClass('collapsed');
  showSuccessTick('已折叠当前标签页项目');
}, 'handleCollapseCurrent');
